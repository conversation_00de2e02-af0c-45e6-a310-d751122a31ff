#!/usr/bin/env node

/**
 * Test script for HR Notifications API
 * This script tests all the HR notification endpoints to ensure they're working correctly
 */

const BASE_URL = 'http://localhost:3001';

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    return { status: 0, error: error.message };
  }
}

async function testGetNotifications() {
  console.log('\n🔔 Testing GET /api/hr/notifications...');
  
  const result = await makeRequest(`${BASE_URL}/api/hr/notifications?userId=user-001`);
  
  if (result.status === 200) {
    console.log('✅ GET notifications successful');
    console.log(`   Found ${result.data.length} notifications for user-001`);
    result.data.forEach(notif => {
      console.log(`   - ${notif.title} (${notif.type}) - Read: ${notif.read}`);
    });
  } else {
    console.log('❌ GET notifications failed:', result.status, result.data || result.error);
  }
  
  return result.status === 200;
}

async function testCreateNotification() {
  console.log('\n📝 Testing POST /api/hr/notifications...');
  
  const newNotification = {
    userId: 'user-001',
    type: 'system',
    title: 'Test API Notification',
    message: 'This notification was created via API test',
    module: 'test',
    tenantId: 'default',
    data: { testId: 'api-test-001' }
  };
  
  const result = await makeRequest(`${BASE_URL}/api/hr/notifications`, {
    method: 'POST',
    body: JSON.stringify(newNotification),
  });
  
  if (result.status === 201) {
    console.log('✅ POST notification successful');
    console.log(`   Created notification with ID: ${result.data.id}`);
    return result.data.id;
  } else {
    console.log('❌ POST notification failed:', result.status, result.data || result.error);
    return null;
  }
}

async function testMarkAsRead(notificationId) {
  console.log('\n✅ Testing POST /api/hr/notifications/[id]/read...');
  
  const result = await makeRequest(`${BASE_URL}/api/hr/notifications/${notificationId}/read`, {
    method: 'POST',
  });
  
  if (result.status === 200) {
    console.log('✅ Mark as read successful');
    console.log(`   Notification ${notificationId} marked as read`);
  } else {
    console.log('❌ Mark as read failed:', result.status, result.data || result.error);
  }
  
  return result.status === 200;
}

async function testGetPreferences() {
  console.log('\n⚙️ Testing GET /api/hr/notifications/preferences...');
  
  const result = await makeRequest(`${BASE_URL}/api/hr/notifications/preferences?userId=user-001`);
  
  if (result.status === 200) {
    console.log('✅ GET preferences successful');
    console.log(`   Email: ${result.data.email}, Push: ${result.data.push}, InApp: ${result.data.inApp}`);
    console.log(`   Types enabled: ${Object.entries(result.data.types).filter(([k,v]) => v).map(([k,v]) => k).join(', ')}`);
  } else {
    console.log('❌ GET preferences failed:', result.status, result.data || result.error);
  }
  
  return result.status === 200;
}

async function testUpdatePreferences() {
  console.log('\n🔧 Testing POST /api/hr/notifications/preferences...');
  
  const updatedPreferences = {
    userId: 'user-001',
    email: true,
    push: false,
    inApp: true,
    types: {
      job_match: true,
      application_update: true,
      interview_scheduled: false,
      system: true,
      message: true
    }
  };
  
  const result = await makeRequest(`${BASE_URL}/api/hr/notifications/preferences`, {
    method: 'POST',
    body: JSON.stringify(updatedPreferences),
  });
  
  if (result.status === 200) {
    console.log('✅ POST preferences successful');
    console.log(`   Updated preferences for user-001`);
  } else {
    console.log('❌ POST preferences failed:', result.status, result.data || result.error);
  }
  
  return result.status === 200;
}

async function testEmailNotification() {
  console.log('\n📧 Testing POST /api/hr/notifications/email...');
  
  const emailData = {
    userId: 'user-001',
    notification: {
      id: 'test-email-001',
      title: 'Test Email Notification',
      message: 'This is a test email notification',
      data: { testType: 'email' }
    }
  };
  
  const result = await makeRequest(`${BASE_URL}/api/hr/notifications/email`, {
    method: 'POST',
    body: JSON.stringify(emailData),
  });
  
  if (result.status === 200) {
    console.log('✅ Email notification successful');
    console.log(`   Email sent with ID: ${result.data.emailId}`);
  } else {
    console.log('❌ Email notification failed:', result.status, result.data || result.error);
  }
  
  return result.status === 200;
}

async function testPushNotification() {
  console.log('\n📱 Testing POST /api/hr/notifications/push...');
  
  const pushData = {
    userId: 'user-001',
    notification: {
      id: 'test-push-001',
      title: 'Test Push Notification',
      message: 'This is a test push notification',
      data: { testType: 'push' }
    }
  };
  
  const result = await makeRequest(`${BASE_URL}/api/hr/notifications/push`, {
    method: 'POST',
    body: JSON.stringify(pushData),
  });
  
  if (result.status === 200) {
    console.log('✅ Push notification successful');
    console.log(`   Push sent with ID: ${result.data.pushId}`);
  } else {
    console.log('❌ Push notification failed:', result.status, result.data || result.error);
  }
  
  return result.status === 200;
}

async function runTests() {
  console.log('🚀 Starting HR Notifications API Tests...');
  console.log('=====================================');
  
  const results = [];
  
  // Test basic notification operations
  results.push(await testGetNotifications());
  const newNotificationId = await testCreateNotification();
  
  if (newNotificationId) {
    results.push(await testMarkAsRead(newNotificationId));
  }
  
  // Test preferences
  results.push(await testGetPreferences());
  results.push(await testUpdatePreferences());
  
  // Test notification delivery
  results.push(await testEmailNotification());
  results.push(await testPushNotification());
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('======================');
  const passed = results.filter(r => r).length;
  const total = results.length;
  console.log(`✅ Passed: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! HR Notifications API is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the output above for details.');
  }
  
  return passed === total;
}

// Run the tests
runTests().catch(console.error);
