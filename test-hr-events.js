#!/usr/bin/env node

/**
 * Test script for HR Notification Events
 * This script tests the event system to ensure real-time updates work correctly
 */

const BASE_URL = 'http://localhost:3001';

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    return { status: 0, error: error.message };
  }
}

async function testNotificationCreationAndEvents() {
  console.log('\n🔔 Testing notification creation and events...');
  
  // Get initial notification count
  const initialResult = await makeRequest(`${BASE_URL}/api/hr/notifications?userId=user-001`);
  if (initialResult.status !== 200) {
    console.log('❌ Failed to get initial notifications');
    return false;
  }
  
  const initialCount = initialResult.data.length;
  console.log(`📊 Initial notification count: ${initialCount}`);
  
  // Create a new notification
  const newNotification = {
    userId: 'user-001',
    type: 'system',
    title: 'Event Test Notification',
    message: 'This notification tests the event system',
    module: 'test-events',
    tenantId: 'default',
    data: { eventTest: true, timestamp: new Date().toISOString() }
  };
  
  const createResult = await makeRequest(`${BASE_URL}/api/hr/notifications`, {
    method: 'POST',
    body: JSON.stringify(newNotification),
  });
  
  if (createResult.status !== 201) {
    console.log('❌ Failed to create notification');
    return false;
  }
  
  console.log(`✅ Created notification with ID: ${createResult.data.id}`);
  
  // Wait a moment for any async processing
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // Get updated notification count
  const updatedResult = await makeRequest(`${BASE_URL}/api/hr/notifications?userId=user-001`);
  if (updatedResult.status !== 200) {
    console.log('❌ Failed to get updated notifications');
    return false;
  }
  
  const updatedCount = updatedResult.data.length;
  console.log(`📊 Updated notification count: ${updatedCount}`);
  
  if (updatedCount === initialCount + 1) {
    console.log('✅ Notification count increased correctly');
  } else {
    console.log('❌ Notification count did not increase as expected');
    return false;
  }
  
  // Test marking as read
  const markReadResult = await makeRequest(`${BASE_URL}/api/hr/notifications/${createResult.data.id}/read`, {
    method: 'POST',
  });
  
  if (markReadResult.status === 200) {
    console.log('✅ Successfully marked notification as read');
  } else {
    console.log('❌ Failed to mark notification as read');
    return false;
  }
  
  // Verify the notification is marked as read
  const verifyResult = await makeRequest(`${BASE_URL}/api/hr/notifications?userId=user-001`);
  if (verifyResult.status === 200) {
    const notification = verifyResult.data.find(n => n.id === createResult.data.id);
    if (notification && notification.read === true) {
      console.log('✅ Notification read status updated correctly');
    } else {
      console.log('❌ Notification read status not updated');
      return false;
    }
  }
  
  return true;
}

async function testNotificationFiltering() {
  console.log('\n🔍 Testing notification filtering...');
  
  // Test filtering by type
  const typeResult = await makeRequest(`${BASE_URL}/api/hr/notifications?userId=user-001&type=system`);
  if (typeResult.status === 200) {
    const systemNotifications = typeResult.data.filter(n => n.type === 'system');
    if (systemNotifications.length === typeResult.data.length) {
      console.log(`✅ Type filtering works - found ${systemNotifications.length} system notifications`);
    } else {
      console.log('❌ Type filtering failed');
      return false;
    }
  } else {
    console.log('❌ Failed to test type filtering');
    return false;
  }
  
  // Test filtering by module
  const moduleResult = await makeRequest(`${BASE_URL}/api/hr/notifications?userId=user-001&module=test-events`);
  if (moduleResult.status === 200) {
    const testNotifications = moduleResult.data.filter(n => n.module === 'test-events');
    if (testNotifications.length === moduleResult.data.length) {
      console.log(`✅ Module filtering works - found ${testNotifications.length} test-events notifications`);
    } else {
      console.log('❌ Module filtering failed');
      return false;
    }
  } else {
    console.log('❌ Failed to test module filtering');
    return false;
  }
  
  // Test unread only filtering
  const unreadResult = await makeRequest(`${BASE_URL}/api/hr/notifications?userId=user-001&unreadOnly=true`);
  if (unreadResult.status === 200) {
    const unreadNotifications = unreadResult.data.filter(n => !n.read);
    if (unreadNotifications.length === unreadResult.data.length) {
      console.log(`✅ Unread filtering works - found ${unreadNotifications.length} unread notifications`);
    } else {
      console.log('❌ Unread filtering failed');
      return false;
    }
  } else {
    console.log('❌ Failed to test unread filtering');
    return false;
  }
  
  return true;
}

async function testPreferencesIntegration() {
  console.log('\n⚙️ Testing preferences integration...');
  
  // Get current preferences
  const prefsResult = await makeRequest(`${BASE_URL}/api/hr/notifications/preferences?userId=user-001`);
  if (prefsResult.status !== 200) {
    console.log('❌ Failed to get preferences');
    return false;
  }
  
  console.log(`📋 Current preferences - Email: ${prefsResult.data.email}, Push: ${prefsResult.data.push}, InApp: ${prefsResult.data.inApp}`);
  
  // Update preferences to disable push notifications
  const updatePrefs = {
    userId: 'user-001',
    email: true,
    push: false,
    inApp: true,
    types: {
      job_match: true,
      application_update: true,
      interview_scheduled: true,
      system: true,
      message: true
    }
  };
  
  const updateResult = await makeRequest(`${BASE_URL}/api/hr/notifications/preferences`, {
    method: 'POST',
    body: JSON.stringify(updatePrefs),
  });
  
  if (updateResult.status === 200) {
    console.log('✅ Preferences updated successfully');
    
    // Verify the update
    const verifyPrefs = await makeRequest(`${BASE_URL}/api/hr/notifications/preferences?userId=user-001`);
    if (verifyPrefs.status === 200 && verifyPrefs.data.push === false) {
      console.log('✅ Preferences verification successful');
    } else {
      console.log('❌ Preferences verification failed');
      return false;
    }
  } else {
    console.log('❌ Failed to update preferences');
    return false;
  }
  
  return true;
}

async function runEventTests() {
  console.log('🚀 Starting HR Notification Event Tests...');
  console.log('==========================================');
  
  const results = [];
  
  // Test core notification functionality with events
  results.push(await testNotificationCreationAndEvents());
  
  // Test filtering capabilities
  results.push(await testNotificationFiltering());
  
  // Test preferences integration
  results.push(await testPreferencesIntegration());
  
  // Summary
  console.log('\n📊 Event Test Results Summary');
  console.log('=============================');
  const passed = results.filter(r => r).length;
  const total = results.length;
  console.log(`✅ Passed: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 All event tests passed! HR Notification events are working correctly.');
    console.log('💡 The HRUnifiedLayout component should now receive real-time updates.');
  } else {
    console.log('⚠️ Some event tests failed. Please check the output above for details.');
  }
  
  return passed === total;
}

// Run the tests
runEventTests().catch(console.error);
