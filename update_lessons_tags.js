#!/usr/bin/env node

import fs from 'fs';

// File paths
const lessonTagsPath = 'data/apps/web/wise/eduwise/lesson_tags.json';
const lessonsPath = 'data/apps/web/wise/eduwise/lessons.json';
const lessonsBackupPath = 'data/apps/web/wise/eduwise/lessons_backup.json';

console.log('Starting lessons.json tag update process...');

try {
    // Read lesson_tags.json
    console.log('Reading lesson_tags.json...');
    const lessonTagsData = JSON.parse(fs.readFileSync(lessonTagsPath, 'utf8'));
    
    // Extract the actual data array from the PHPMyAdmin export format
    let lessonTagsArray = [];
    if (Array.isArray(lessonTagsData)) {
        // Find the table data
        const tableData = lessonTagsData.find(item => item.type === 'table' && item.name === 'lesson_tags');
        if (tableData && tableData.data) {
            lessonTagsArray = tableData.data;
        }
    }
    
    console.log(`Found ${lessonTagsArray.length} lesson-tag relationships`);
    
    // Create a mapping from lesson_id to array of tag_ids
    const lessonToTagsMap = {};
    lessonTagsArray.forEach(item => {
        const lessonId = parseInt(item.lesson_id);
        const tagId = item.course_tag_id;
        
        if (!lessonToTagsMap[lessonId]) {
            lessonToTagsMap[lessonId] = [];
        }
        lessonToTagsMap[lessonId].push(tagId);
    });
    
    console.log(`Created mapping for ${Object.keys(lessonToTagsMap).length} lessons`);
    
    // Read lessons.json
    console.log('Reading lessons.json...');
    const lessonsData = JSON.parse(fs.readFileSync(lessonsPath, 'utf8'));
    
    console.log(`Found ${lessonsData.length} lessons`);
    
    // Create backup
    console.log('Creating backup of original lessons.json...');
    fs.writeFileSync(lessonsBackupPath, JSON.stringify(lessonsData, null, 2));
    
    // Update lessons with tags
    console.log('Updating lessons with tags...');
    let updatedCount = 0;
    let noTagsCount = 0;
    
    lessonsData.forEach(lesson => {
        const lessonId = lesson.id;
        const tags = lessonToTagsMap[lessonId] || [];
        
        // Add tags array to lesson
        lesson.tags = tags;
        
        if (tags.length > 0) {
            updatedCount++;
            console.log(`Lesson ${lessonId}: Added ${tags.length} tags: [${tags.join(', ')}]`);
        } else {
            noTagsCount++;
            console.log(`Lesson ${lessonId}: No tags found`);
        }
    });
    
    // Write updated lessons.json
    console.log('Writing updated lessons.json...');
    fs.writeFileSync(lessonsPath, JSON.stringify(lessonsData, null, 2));
    
    console.log('\n=== UPDATE COMPLETE ===');
    console.log(`Total lessons processed: ${lessonsData.length}`);
    console.log(`Lessons with tags added: ${updatedCount}`);
    console.log(`Lessons with no tags: ${noTagsCount}`);
    console.log(`Backup saved to: ${lessonsBackupPath}`);
    console.log(`Updated file: ${lessonsPath}`);
    
} catch (error) {
    console.error('Error updating lessons:', error);
    process.exit(1);
}
