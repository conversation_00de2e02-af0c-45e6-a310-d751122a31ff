'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { HunterAuthWrapper } from '../components/HRAuthWrapper';
import HRUnifiedLayout from '../shared/components/HRUnifiedLayout';

export default function TopCVHomePage() {
  const [jobs, setJobs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Fetch jobs data
    const fetchJobs = async () => {
      try {
        const response = await fetch('/data/apps/hunter/jobs.json');
        const data = await response.json();
        // Get only featured jobs
        const featuredJobs = data.filter(job => job.featured);
        setJobs(featuredJobs.slice(0, 4));
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching jobs:', error);
        setIsLoading(false);
      }
    };

    fetchJobs();
  }, []);

  return (
    <HunterAuthWrapper>
      <HRUnifiedLayout 
        title="Job Seekers Portal"
        subtitle="Find your dream job and advance your career"
      >
        <div className="bg-gray-50">
      {/* Hero Section */}
      <div className="bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Find Your Dream Job in Vietnam
              </h1>
              <p className="text-xl mb-8">
                Connect with top employers and opportunities across Vietnam
              </p>
              <div className="bg-white rounded-lg p-4 shadow-md">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <input 
                      type="text" 
                      placeholder="Job title, keywords, or company" 
                      className="w-full p-3 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500" 
                    />
                  </div>
                  <div className="md:w-1/3">
                    <select className="w-full p-3 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option value="">All Locations</option>
                      <option value="hanoi">Hanoi</option>
                      <option value="ho-chi-minh-city">Ho Chi Minh City</option>
                      <option value="da-nang">Da Nang</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <button className="bg-blue-700 hover:bg-blue-800 text-white font-medium py-3 px-6 rounded-md transition-colors">
                    Search
                  </button>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="relative h-80 w-full">
                {/* Replace with actual image */}
                <div className="w-full h-full bg-blue-500 rounded-lg flex items-center justify-center text-white text-xl font-medium">
                  Job Search Image
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Job Categories */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Popular Job Categories
            </h2>
            <p className="text-lg text-gray-600">
              Explore opportunities in these in-demand fields
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { name: 'Information Technology', icon: '💻', count: 1523 },
              { name: 'Marketing', icon: '📊', count: 872 },
              { name: 'Finance', icon: '💰', count: 654 },
              { name: 'Engineering', icon: '⚙️', count: 432 },
              { name: 'Healthcare', icon: '🏥', count: 321 },
              { name: 'Education', icon: '🎓', count: 287 },
              { name: 'Sales', icon: '📈', count: 542 },
              { name: 'Customer Service', icon: '🤝', count: 389 }
            ].map((category, index) => (
              <Link 
                href={`/hr/hunter/jobs?category=${category.name.toLowerCase().replace(' ', '-')}`} 
                key={index}
                className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
              >
                <div className="text-4xl mb-3">{category.icon}</div>
                <h3 className="font-medium text-gray-900 mb-1">{category.name}</h3>
                <p className="text-sm text-gray-500">{category.count} jobs</p>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Featured Jobs */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-10">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Featured Jobs
              </h2>
              <p className="text-lg text-gray-600">
                Handpicked opportunities from top employers
              </p>
            </div>
            <Link 
              href="/hr/hunter/jobs" 
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              View All Jobs →
            </Link>
          </div>
          
          {isLoading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {jobs.map((job, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start">
                    <div className="mr-4 w-12 h-12 flex-shrink-0 bg-gray-200 rounded-md flex items-center justify-center">
                      {/* Company logo placeholder */}
                      <span className="text-gray-500 text-xs">{job.company.substring(0, 2)}</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-lg text-gray-900 mb-1">
                        <Link href={`/hr/hunter/jobs/${job.id}`} className="hover:text-blue-600">
                          {job.title}
                        </Link>
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">{job.company}</p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {job.jobType}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {job.location}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">{job.salary}</span>
                        <span className="text-xs text-gray-500">Posted: {new Date(job.postedDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Vietnam's Leading Job Platform
            </h2>
            <p className="text-lg text-gray-600">
              Join thousands of job seekers and employers on Hunter
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            {[
              { label: 'Active Job Listings', value: '15,000+' },
              { label: 'Registered Companies', value: '10,000+' },
              { label: 'Job Seekers', value: '2M+' },
              { label: 'Successful Placements', value: '500,000+' },
            ].map((stat, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Testimonials */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Success Stories
            </h2>
            <p className="text-lg text-gray-600">
              Hear from professionals who found their dream jobs
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: 'Nguyen Thi Mai',
                position: 'UX Designer at TechViet',
                quote: 'Hunter helped me find a job that perfectly matches my skills and career aspirations. The platform is intuitive and the job recommendations were spot on.',
                image: '/images/testimonial1.jpg'
              },
              {
                name: 'Tran Van Minh',
                position: 'Software Engineer at GlobalTech',
                quote: 'After updating my CV on Hunter, I received multiple interview requests from top companies. Within a month, I secured a position with a 30% salary increase.',
                image: '/images/testimonial2.jpg'
              },
              {
                name: 'Le Thanh Huong',
                position: 'Marketing Manager at BrandX',
                quote: 'As someone returning to the workforce after a break, Hunter made the job search process seamless. The career resources were particularly helpful.',
                image: '/images/testimonial3.jpg'
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-gray-300 mr-4"></div>
                  <div>
                    <h3 className="font-medium text-gray-900">{testimonial.name}</h3>
                    <p className="text-sm text-gray-600">{testimonial.position}</p>
                  </div>
                </div>
                <p className="text-gray-700 italic">"{testimonial.quote}"</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Take the Next Step in Your Career?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Join Hunter today to explore thousands of opportunities and connect with top employers across Vietnam.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/hr/hunter/signup" 
              className="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-md transition-colors"
            >
              Create Your Profile
            </Link>
            <Link 
              href="/hr/hunter/jobs" 
              className="bg-blue-700 hover:bg-blue-800 text-white font-medium py-3 px-8 rounded-md border border-blue-400 transition-colors"
            >
              Browse Jobs
            </Link>
          </div>
        </div>
        </div>
        </div>
        </HRUnifiedLayout>
    </HunterAuthWrapper>
  );
}