'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import {
  ArrowLeft,
  Plus,
  Minus,
  Upload,
  Calendar,
  DollarSign,
  FileText,
  Building,
  Target,
  Users,
  CheckCircle,
  AlertCircle,
  Save,
  Send,
  BookOpen,
  ExternalLink
} from 'lucide-react';

export default function CreateRFPPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const templateId = searchParams?.get('template');

  const [currentStep, setCurrentStep] = useState(1);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
  const [rfpForm, setRfpForm] = useState({
    // Basic Information
    title: '',
    description: '',
    category: '',
    subcategory: '',
    sector: '',
    
    // Organization Details
    organizationName: '',
    organizationType: '',
    location: '',
    contactEmail: '',
    contactPhone: '',
    
    // Budget & Timeline
    budgetType: 'fixed',
    budgetAmount: '',
    budgetRange: '',
    currency: 'USD',
    submissionDeadline: '',
    projectStart: '',
    projectEnd: '',
    
    // Requirements & Scope
    requirements: [''],
    scope: [''],
    technicalRequirements: [''],
    
    // Evaluation Criteria
    evaluationCriteria: [
      { criterion: '', weight: '', description: '' }
    ],
    
    // Additional Settings
    priority: 'medium',
    featured: false,
    tags: [''],

    // Documents
    documents: [{ name: '', url: '', type: 'external', description: '' }]
  });

  const steps = [
    { id: 1, name: 'Basic Information', icon: FileText },
    { id: 2, name: 'Organization Details', icon: Building },
    { id: 3, name: 'Budget & Timeline', icon: DollarSign },
    { id: 4, name: 'Requirements & Scope', icon: Target },
    { id: 5, name: 'Evaluation Criteria', icon: Users },
    { id: 6, name: 'Documents & Links', icon: Upload },
    { id: 7, name: 'Review & Publish', icon: CheckCircle }
  ];

  const categories = [
    'Information Technology',
    'Construction & Infrastructure',
    'Marketing & Advertising',
    'Consultancy Services',
    'Healthcare & Medical',
    'Education & Training',
    'Manufacturing',
    'Energy & Utilities',
    'Transportation',
    'Financial Services'
  ];

  const organizationTypes = [
    'Government Agency',
    'Private Company',
    'Non-Profit Organization',
    'Educational Institution',
    'Healthcare Organization',
    'Startup',
    'Multinational Corporation',
    'Small Business',
    'Other'
  ];

  // Load template if templateId is provided
  useEffect(() => {
    const loadTemplate = async () => {
      if (templateId) {
        setIsLoadingTemplate(true);
        try {
          const storedTemplate = localStorage.getItem('selectedRFPTemplate');
          if (storedTemplate) {
            const template = JSON.parse(storedTemplate);
            if (template.id === templateId) {
              // Populate form with template data
              setRfpForm(prev => ({
                ...prev,
                title: template.template.title,
                description: template.template.description,
                category: template.category,
                subcategory: template.subcategory,
                requirements: template.template.requirements,
                scope: template.template.scope,
                technicalRequirements: template.template.technicalRequirements,
                evaluationCriteria: template.template.evaluationCriteria,
                priority: template.template.priority,
                budgetRange: template.template.budgetRange
              }));
            }
          }
        } catch (error) {
          console.error('Error loading template:', error);
        } finally {
          setIsLoadingTemplate(false);
        }
      }
    };

    loadTemplate();
  }, [templateId]);

  // Load selected requirements from requirements bank
  useEffect(() => {
    const loadSelectedRequirements = () => {
      const storedRequirements = localStorage.getItem('selectedRequirements');
      if (storedRequirements) {
        try {
          const { type, items } = JSON.parse(storedRequirements);
          if (type && items && items.length > 0) {
            const texts = items.map((item: any) => item.text);

            setRfpForm(prev => {
              switch (type) {
                case 'requirements':
                  return { ...prev, requirements: [...prev.requirements, ...texts] };
                case 'scope':
                  return { ...prev, scope: [...prev.scope, ...texts] };
                case 'technical':
                  return { ...prev, technicalRequirements: [...prev.technicalRequirements, ...texts] };
                case 'evaluation':
                  return {
                    ...prev,
                    evaluationCriteria: [
                      ...prev.evaluationCriteria,
                      ...items.map((item: any) => ({
                        criterion: item.text,
                        weight: item.defaultWeight?.toString() || '',
                        description: item.description || ''
                      }))
                    ]
                  };
                default:
                  return prev;
              }
            });

            // Clear the stored requirements after loading
            localStorage.removeItem('selectedRequirements');
          }
        } catch (error) {
          console.error('Error loading selected requirements:', error);
        }
      }
    };

    loadSelectedRequirements();
  }, []);

  const handleInputChange = (field, value) => {
    setRfpForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayChange = (field, index, value) => {
    setRfpForm(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setRfpForm(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setRfpForm(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleEvaluationCriteriaChange = (index, field, value) => {
    setRfpForm(prev => ({
      ...prev,
      evaluationCriteria: prev.evaluationCriteria.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const addEvaluationCriterion = () => {
    setRfpForm(prev => ({
      ...prev,
      evaluationCriteria: [...prev.evaluationCriteria, { criterion: '', weight: '', description: '' }]
    }));
  };

  const removeEvaluationCriterion = (index) => {
    setRfpForm(prev => ({
      ...prev,
      evaluationCriteria: prev.evaluationCriteria.filter((_, i) => i !== index)
    }));
  };

  const handleDocumentChange = (index, field, value) => {
    setRfpForm(prev => ({
      ...prev,
      documents: prev.documents.map((doc, i) =>
        i === index ? { ...doc, [field]: value } : doc
      )
    }));
  };

  const addDocument = () => {
    setRfpForm(prev => ({
      ...prev,
      documents: [...prev.documents, { name: '', url: '', type: 'external', description: '' }]
    }));
  };

  const removeDocument = (index) => {
    setRfpForm(prev => ({
      ...prev,
      documents: prev.documents.filter((_, i) => i !== index)
    }));
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (action: 'draft' | 'publish') => {
    try {
      // Prepare the RFP data
      const rfpData = {
        title: rfpForm.title,
        description: rfpForm.description,
        category: rfpForm.category,
        subcategory: rfpForm.subcategory,
        sector: rfpForm.sector,
        organization: {
          name: rfpForm.organizationName,
          type: rfpForm.organizationType,
          location: rfpForm.location,
          contact: {
            email: rfpForm.contactEmail,
            phone: rfpForm.contactPhone
          }
        },
        budget: {
          type: rfpForm.budgetType,
          amount: parseInt(rfpForm.budgetAmount),
          currency: rfpForm.currency,
          range: rfpForm.budgetRange
        },
        timeline: {
          submissionDeadline: rfpForm.submissionDeadline,
          projectStart: rfpForm.projectStart,
          projectEnd: rfpForm.projectEnd,
          duration: calculateDuration(rfpForm.projectStart, rfpForm.projectEnd)
        },
        requirements: rfpForm.requirements.filter(req => req.trim() !== ''),
        scope: rfpForm.scope.filter(scope => scope.trim() !== ''),
        technicalRequirements: rfpForm.technicalRequirements.filter(tech => tech.trim() !== ''),
        evaluationCriteria: rfpForm.evaluationCriteria.filter(criteria =>
          criteria.criterion.trim() !== '' && criteria.weight.trim() !== ''
        ),
        priority: rfpForm.priority,
        featured: rfpForm.featured,
        tags: rfpForm.tags.filter(tag => tag.trim() !== ''),
        status: action === 'draft' ? 'draft' : 'open',
        documents: [] // Will be implemented later for file uploads
      };

      const response = await fetch('/api/rfps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(rfpData),
      });

      if (response.ok) {
        const newRFP = await response.json();
        alert(`RFP ${action === 'draft' ? 'saved as draft' : 'published'} successfully!`);
        // Redirect to the new RFP page
        router.push(`/hr/hunter/abnrfp/rfps/${newRFP.id}`);
      } else {
        throw new Error('Failed to save RFP');
      }
    } catch (error) {
      console.error('Error submitting RFP:', error);
      alert('Failed to save RFP. Please try again.');
    }
  };

  const calculateDuration = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return '';
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const months = Math.floor(diffDays / 30);
    const days = diffDays % 30;

    if (months > 0) {
      return days > 0 ? `${months} months ${days} days` : `${months} months`;
    }
    return `${diffDays} days`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/hr/hunter/abnrfp"
              className="flex items-center text-gray-600 hover:text-gray-900 transition"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to ABN RFP
            </Link>
            
            <div className="flex items-center space-x-4">
              <button 
                onClick={() => handleSubmit('draft')}
                className="flex items-center text-gray-600 hover:text-gray-900 border border-gray-300 px-4 py-2 rounded-lg"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'border-gray-300 text-gray-500'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <step.icon className="h-5 w-5" />
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <div className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`hidden sm:block w-16 h-0.5 ml-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Basic Information</h2>
                <p className="text-gray-600 mb-6">Provide the fundamental details about your RFP.</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  RFP Title *
                </label>
                <input
                  type="text"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter a clear, descriptive title for your RFP"
                  value={rfpForm.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  required
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Provide a comprehensive description of your project requirements, objectives, and expected outcomes..."
                  value={rfpForm.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={rfpForm.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subcategory
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Software Development, Web Design"
                    value={rfpForm.subcategory}
                    onChange={(e) => handleInputChange('subcategory', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <div className="space-y-2">
                  {rfpForm.tags.map((tag, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter a tag (e.g., web development, mobile app)"
                        value={tag}
                        onChange={(e) => handleArrayChange('tags', index, e.target.value)}
                      />
                      {rfpForm.tags.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeArrayItem('tags', index)}
                          className="p-2 text-red-600 hover:text-red-800"
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => addArrayItem('tags')}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Tag
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Organization Details */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Organization Details</h2>
                <p className="text-gray-600 mb-6">Tell bidders about your organization.</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Organization Name *
                  </label>
                  <input
                    type="text"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Your organization name"
                    value={rfpForm.organizationName}
                    onChange={(e) => handleInputChange('organizationName', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Organization Type *
                  </label>
                  <select
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={rfpForm.organizationType}
                    onChange={(e) => handleInputChange('organizationType', e.target.value)}
                  >
                    <option value="">Select type</option>
                    {organizationTypes.map((type) => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location *
                </label>
                <input
                  type="text"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="City, Country"
                  value={rfpForm.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Email *
                  </label>
                  <input
                    type="email"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                    value={rfpForm.contactEmail}
                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Phone
                  </label>
                  <input
                    type="tel"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="+****************"
                    value={rfpForm.contactPhone}
                    onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Budget & Timeline */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Budget & Timeline</h2>
                <p className="text-gray-600 mb-6">Define the financial and time parameters for your project.</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Budget Type *
                  </label>
                  <select
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={rfpForm.budgetType}
                    onChange={(e) => handleInputChange('budgetType', e.target.value)}
                  >
                    <option value="fixed">Fixed Budget</option>
                    <option value="range">Budget Range</option>
                    <option value="hourly">Hourly Rate</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Currency *
                  </label>
                  <select
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={rfpForm.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                  >
                    <option value="USD">USD</option>
                    <option value="VND">VND</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Budget Amount *
                  </label>
                  <input
                    type="number"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter amount"
                    value={rfpForm.budgetAmount}
                    onChange={(e) => handleInputChange('budgetAmount', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Budget Range (Optional)
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 50000-75000"
                    value={rfpForm.budgetRange}
                    onChange={(e) => handleInputChange('budgetRange', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Submission Deadline *
                  </label>
                  <input
                    type="datetime-local"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={rfpForm.submissionDeadline}
                    onChange={(e) => handleInputChange('submissionDeadline', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Project Start Date *
                  </label>
                  <input
                    type="date"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={rfpForm.projectStart}
                    onChange={(e) => handleInputChange('projectStart', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Project End Date *
                  </label>
                  <input
                    type="date"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={rfpForm.projectEnd}
                    onChange={(e) => handleInputChange('projectEnd', e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Requirements & Scope */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Requirements & Scope</h2>
                <p className="text-gray-600 mb-6">Define what you need and the scope of work.</p>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Project Requirements *
                  </label>
                  <Link
                    href="/hr/hunter/abnrfp/requirements-bank"
                    target="_blank"
                    className="flex items-center text-blue-600 hover:text-blue-500 text-sm"
                  >
                    <BookOpen className="h-4 w-4 mr-1" />
                    Browse Requirements Bank
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Link>
                </div>
                <div className="space-y-2">
                  {rfpForm.requirements.map((requirement, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter a requirement"
                        value={requirement}
                        onChange={(e) => handleArrayChange('requirements', index, e.target.value)}
                      />
                      {rfpForm.requirements.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeArrayItem('requirements', index)}
                          className="p-2 text-red-600 hover:text-red-800"
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => addArrayItem('requirements')}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Requirement
                  </button>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Scope of Work *
                  </label>
                  <Link
                    href="/hr/hunter/abnrfp/requirements-bank"
                    target="_blank"
                    className="flex items-center text-blue-600 hover:text-blue-500 text-sm"
                  >
                    <Target className="h-4 w-4 mr-1" />
                    Browse Scope Items
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Link>
                </div>
                <div className="space-y-2">
                  {rfpForm.scope.map((scopeItem, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter scope item"
                        value={scopeItem}
                        onChange={(e) => handleArrayChange('scope', index, e.target.value)}
                      />
                      {rfpForm.scope.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeArrayItem('scope', index)}
                          className="p-2 text-red-600 hover:text-red-800"
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => addArrayItem('scope')}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Scope Item
                  </button>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Technical Requirements
                  </label>
                  <Link
                    href="/hr/hunter/abnrfp/requirements-bank"
                    target="_blank"
                    className="flex items-center text-blue-600 hover:text-blue-500 text-sm"
                  >
                    <FileText className="h-4 w-4 mr-1" />
                    Browse Technical Requirements
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Link>
                </div>
                <div className="space-y-2">
                  {rfpForm.technicalRequirements.map((techReq, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter technical requirement"
                        value={techReq}
                        onChange={(e) => handleArrayChange('technicalRequirements', index, e.target.value)}
                      />
                      {rfpForm.technicalRequirements.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeArrayItem('technicalRequirements', index)}
                          className="p-2 text-red-600 hover:text-red-800"
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => addArrayItem('technicalRequirements')}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Technical Requirement
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Step 5: Evaluation Criteria */}
          {currentStep === 5 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Evaluation Criteria</h2>
                <p className="text-gray-600 mb-6">Define how proposals will be evaluated.</p>
              </div>

              <div className="space-y-4">
                {rfpForm.evaluationCriteria.map((criteria, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Criterion *
                        </label>
                        <input
                          type="text"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="e.g., Technical Approach"
                          value={criteria.criterion}
                          onChange={(e) => handleEvaluationCriteriaChange(index, 'criterion', e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Weight (%) *
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="30"
                          value={criteria.weight}
                          onChange={(e) => handleEvaluationCriteriaChange(index, 'weight', e.target.value)}
                        />
                      </div>
                      <div className="flex items-end">
                        {rfpForm.evaluationCriteria.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeEvaluationCriterion(index)}
                            className="w-full px-3 py-2 text-red-600 border border-red-300 rounded-lg hover:bg-red-50"
                          >
                            Remove
                          </button>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Describe what this criterion evaluates"
                        value={criteria.description}
                        onChange={(e) => handleEvaluationCriteriaChange(index, 'description', e.target.value)}
                      />
                    </div>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addEvaluationCriterion}
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Evaluation Criterion
                </button>
              </div>
            </div>
          )}

          {/* Step 6: Documents & Links */}
          {currentStep === 6 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Documents & Links</h2>
                <p className="text-gray-600 mb-6">Add supporting documents and external links for your RFP.</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  RFP Documents and External Links
                </label>
                <div className="space-y-4">
                  {rfpForm.documents.map((document, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Document Name *
                          </label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="e.g., Technical Specifications"
                            value={document.name}
                            onChange={(e) => handleDocumentChange(index, 'name', e.target.value)}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Document Type
                          </label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={document.type}
                            onChange={(e) => handleDocumentChange(index, 'type', e.target.value)}
                          >
                            <option value="external">External Link</option>
                            <option value="pdf">PDF Document</option>
                            <option value="docx">Word Document</option>
                            <option value="xlsx">Excel Spreadsheet</option>
                            <option value="other">Other</option>
                          </select>
                        </div>
                      </div>
                      <div className="mb-3">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          URL or File Path *
                        </label>
                        <input
                          type="url"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="https://example.com/document.pdf"
                          value={document.url}
                          onChange={(e) => handleDocumentChange(index, 'url', e.target.value)}
                        />
                      </div>
                      <div className="mb-3">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <textarea
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Brief description of the document"
                          value={document.description}
                          onChange={(e) => handleDocumentChange(index, 'description', e.target.value)}
                        />
                      </div>
                      {rfpForm.documents.length > 1 && (
                        <div className="flex justify-end">
                          <button
                            type="button"
                            onClick={() => removeDocument(index)}
                            className="text-red-600 hover:text-red-800 text-sm font-medium"
                          >
                            Remove Document
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addDocument}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Document
                  </button>
                </div>
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Document Guidelines</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• External links should be publicly accessible or provide access instructions</li>
                  <li>• Include detailed technical specifications, requirements documents, and reference materials</li>
                  <li>• Ensure all linked documents are current and will remain accessible during the RFP period</li>
                  <li>• Consider providing both summary information in the RFP and detailed documents via links</li>
                </ul>
              </div>
            </div>
          )}

          {/* Step 7: Review & Publish */}
          {currentStep === 7 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Review & Publish</h2>
                <p className="text-gray-600 mb-6">Review your RFP details before publishing.</p>
              </div>

              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Basic Information</h3>
                  <p><strong>Title:</strong> {rfpForm.title}</p>
                  <p><strong>Category:</strong> {rfpForm.category}</p>
                  <p><strong>Budget:</strong> {rfpForm.budgetAmount} {rfpForm.currency}</p>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Organization</h3>
                  <p><strong>Name:</strong> {rfpForm.organizationName}</p>
                  <p><strong>Type:</strong> {rfpForm.organizationType}</p>
                  <p><strong>Location:</strong> {rfpForm.location}</p>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Timeline</h3>
                  <p><strong>Submission Deadline:</strong> {rfpForm.submissionDeadline}</p>
                  <p><strong>Project Duration:</strong> {rfpForm.projectStart} to {rfpForm.projectEnd}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority Level
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={rfpForm.priority}
                      onChange={(e) => handleInputChange('priority', e.target.value)}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="featured"
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      checked={rfpForm.featured}
                      onChange={(e) => handleInputChange('featured', e.target.checked)}
                    />
                    <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
                      Feature this RFP (additional visibility)
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-8 border-t">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className={`px-6 py-2 rounded-lg font-medium ${
                currentStep === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>

            {currentStep === steps.length ? (
              <div className="space-x-4">
                <button
                  onClick={() => handleSubmit('draft')}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50"
                >
                  Save as Draft
                </button>
                <button
                  onClick={() => handleSubmit('publish')}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 flex items-center"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Publish RFP
                </button>
              </div>
            ) : (
              <button
                onClick={nextStep}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700"
              >
                Next
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
