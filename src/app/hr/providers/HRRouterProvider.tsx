'use client';

import React, { createContext, useContext, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { HRRouter } from '../utils/hrRouter';

interface HRRouterContextProps {
  children: React.ReactNode;
}

const HRRouterContext = createContext<null>(null);

export function HRRouterProvider({ children }: HRRouterContextProps) {
  const router = useRouter();

  useEffect(() => {
    // Initialize the HR router with the Next.js router instance
    HRRouter.init(router);
  }, [router]);

  return (
    <HRRouterContext.Provider value={null}>
      {children}
    </HRRouterContext.Provider>
  );
}

export const useHRRouter = () => {
  return HRRouter;
};
