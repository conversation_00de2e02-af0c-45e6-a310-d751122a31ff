'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Users,
  Briefcase,
  FileText,
  Scale,
  Brain,
  Building2,
  UserCheck,
  TrendingUp,
  Search,
  Globe,
  Shield,
  Clock,
  Star,
  ArrowRight,
  CheckCircle,
  LogOut,
  Heart,
  User,
  ShoppingCart,
  Package,
  Leaf,
  Menu,
  BarChart2,
  Activity,
  Calendar,
  Map,
  Lock,
  Newspaper,
  Folder,
  CheckSquare,
  Clipboard,
  DollarSign,
  Database,
  Code,
  Monitor,
  Coffee,
  CreditCard
} from 'lucide-react';
import HRSuiteStatus from './components/HRSuiteStatus';
import HRUnifiedLayout from './shared/components/HRUnifiedLayout';
import { useHR, useModule } from './shared/context/HRContext';
import { useHRAuthGuard } from './hooks/useHRAuth';

// Import portal components
import { desktopTheme } from '../portal/styles/theme';
import AppContainer from '../portal/appcontainer/AppContainer';
import CategoryExpansion from '../portal/components/CategoryExpansion';
import { usePortalData } from '../portal/hooks/usePortalData';
import PortalAnalytics from '../portal/components/PortalAnalytics';
import AdvancedSearch from '../portal/components/AdvancedSearch';
import SearchResults from '../portal/components/SearchResults';
import IndustrySelector from '../portal/components/IndustrySelector';
import { useMultiIndustryPortal } from '../portal/hooks/useMultiIndustryPortal';
import { App } from '../portal/appregistry/types';

// Extend the App type for HR portal
interface ExtendedApp extends App {
  rating?: number;
  isSale?: boolean;
}

// Popular searches for HR
const popularSearches = [
  "Job Search", "Recruitment", "Onboarding", "HR Management", "Talent", "Freelancing", "Payroll"
];

// Helper function to get appropriate icon for each HR app
const getAppIcon = (app: any) => {
  const appNameLower = app.name.toLowerCase();
  if (appNameLower.includes('hunter')) return Search;
  if (appNameLower.includes('headhunter')) return Users;
  if (appNameLower.includes('upwork') || appNameLower.includes('freelanc')) return Briefcase;
  if (appNameLower.includes('rfp')) return FileText;
  if (appNameLower.includes('referee') || appNameLower.includes('dispute')) return Scale;
  if (appNameLower.includes('onboarding') || appNameLower.includes('ai')) return Brain;
  if (appNameLower.includes('hrm') || appNameLower.includes('management')) return UserCheck;
  if (appNameLower.includes('recruitment')) return TrendingUp;
  if (appNameLower.includes('talent')) return Star;
  if (appNameLower.includes('worklink') || appNameLower.includes('network')) return Globe;
  if (appNameLower.includes('jobseeker') || appNameLower.includes('dashboard')) return Monitor;

  // Default by category
  const primaryCategory = app.category[0]?.toLowerCase() || '';
  if (primaryCategory.includes('job seekers')) return Search;
  if (primaryCategory.includes('recruiters')) return Users;
  if (primaryCategory.includes('freelancing')) return Briefcase;
  if (primaryCategory.includes('hr management')) return UserCheck;
  if (primaryCategory.includes('onboarding')) return Brain;
  if (primaryCategory.includes('talent')) return Star;
  if (primaryCategory.includes('procurement')) return FileText;
  if (primaryCategory.includes('legal')) return Scale;
  if (primaryCategory.includes('networking')) return Globe;

  return Package;
};

// Helper function to get color for the icon background based on category
const getIconBackground = (category: string): string => {
  const categoryLower = category.toLowerCase();
  if (categoryLower.includes('job seekers')) return '#e3f2fd';
  if (categoryLower.includes('recruiters')) return '#e8f5e9';
  if (categoryLower.includes('freelancing')) return '#fff8e1';
  if (categoryLower.includes('hr management')) return '#ede7f6';
  if (categoryLower.includes('onboarding')) return '#e1f5fe';
  if (categoryLower.includes('talent')) return '#fce4ec';
  if (categoryLower.includes('procurement')) return '#e0f2f1';
  if (categoryLower.includes('legal')) return '#fff3e0';
  if (categoryLower.includes('networking')) return '#f3e5f5';
  return '#f5f5f5';
};

export default function HRSuitePage() {
  // Use auth guard to protect this page
  const auth = useHRAuthGuard(['hr:access']);
  const { user, logout } = useHR();
  const { setModuleState } = useModule('dashboard');

  // Portal state
  const [searchQuery, setSearchQuery] = useState('');
  const [appsList, setAppsList] = useState<ExtendedApp[]>([]);
  const [selectedApp, setSelectedApp] = useState<ExtendedApp | null>(null);
  const [displayedAppsCount, setDisplayedAppsCount] = useState(20);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [currentSearchResults, setCurrentSearchResults] = useState<any[]>([]);
  const theme = desktopTheme;

  // Multi-industry portal hook - force HR industry
  const {
    currentIndustry,
    allIndustries,
    currentApps,
    currentCategories,
    setCurrentIndustry: setIndustry,
    getAppsByCategory: getAppsByCategoryFromIndustry,
    searchApps,
    isLoading: industryLoading,
    error: industryError
  } = useMultiIndustryPortal();

  // Use the portal data hook
  const {
    preferences,
    categories,
    loading: portalLoading,
    selectCategory,
    toggleCategoryExpansion,
    selectSubcategory,
    addToRecent,
    trackSearch,
    setViewMode,
    searchHistory,
    searchFilters,
    searchEngine,
    searchResults,
    availableTags,
    availableDevelopers,
    clearSearchHistory,
    updateSearchFilters,
    initializeSearchEngine,
    performSearch,
    getSearchSuggestions,
    selectedCategory,
    expandedCategories,
    viewMode
  } = usePortalData();

  // Set module state - ALWAYS call this effect
  useEffect(() => {
    if (!auth.loading && auth.hasAccess) {
      setModuleState({
        currentModule: 'dashboard',
        pageTitle: 'HR Suite Portal',
        breadcrumbs: []
      });
    }
  }, [setModuleState, auth.loading, auth.hasAccess]);

  // Force HR industry selection
  useEffect(() => {
    if (allIndustries.length > 0 && (!currentIndustry || currentIndustry.id !== 'hr')) {
      const hrIndustry = allIndustries.find(industry => industry.id === 'hr');
      if (hrIndustry) {
        setIndustry('hr');
      }
    }
  }, [allIndustries, currentIndustry, setIndustry]);

  // Update apps list when current industry changes
  useEffect(() => {
    if (currentApps.length > 0) {
      // Cast the apps to ExtendedApp type and add default ratings
      const apps = currentApps.map(app => ({
        ...app,
        rating: 4.5 + Math.random() * 0.5, // Generate a random rating between 4.5-5.0
        isSale: app.isHot // For demo, make hot apps also on sale
      }));
      setAppsList(apps);

      // Initialize search engine with apps data
      initializeSearchEngine(apps);
    }
  }, [currentApps, initializeSearchEngine]);

  // Handle search with tracking
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      trackSearch(query);
    }
  };

  // Handle app selection
  const handleAppSelect = (app: ExtendedApp) => {
    addToRecent(app.id);
    setSelectedApp(app);
  };

  // Enhanced search handlers
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);

    if (query.trim()) {
      setShowSearchResults(true);
      const results = performSearch(query, searchFilters);
      setCurrentSearchResults(results);
      trackSearch(query);
    } else {
      setShowSearchResults(false);
      setCurrentSearchResults([]);
    }
  };

  const handleSearchResults = (results: any[]) => {
    setCurrentSearchResults(results);
    setShowSearchResults(results.length > 0 || searchQuery.trim().length > 0);
  };

  const handleToggleFavorite = (appId: string) => {
    console.log('Toggle favorite for app:', appId);
  };

  // Show loading while authentication is being checked
  if (auth.loading || industryLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading HR Portal...</p>
        </div>
      </div>
    );
  }

  // Show error state if there's an error
  if (industryError) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-600 mb-2">Error Loading HR Portal</h1>
          <p className="text-gray-600 mb-4">{industryError}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  // Don't render if no access (auth guard will handle redirect)
  if (!auth.hasAccess) {
    return null;
  }

  // If an app is selected, render it using the AppContainer
  if (selectedApp) {
    // If the app has a component, use it directly in the AppContainer
    if (selectedApp.component) {
      return <AppContainer component={selectedApp.component} />;
    }
    // If path is available, use it for navigation
    else if (selectedApp.path) {
      return <AppContainer appPath={selectedApp.path} />;
    }
    // Otherwise fall back to using the app ID
    return <AppContainer appId={selectedApp.id} />;
  }
  // Group apps by category
  const getAppsByCategory = (): Record<string, ExtendedApp[]> => {
    const appsByCategory: Record<string, ExtendedApp[]> = {
      "All": [...appsList]
    };

    appsList.forEach(app => {
      if (Array.isArray(app.category)) {
        app.category.forEach(category => {
          if (!appsByCategory[category]) {
            appsByCategory[category] = [];
          }
          appsByCategory[category].push(app);
        });
      }
    });

    return appsByCategory;
  };

  // Get categories from current industry
  const getIndustryCategories = (): string[] => {
    if (!currentIndustry) return ["All"];
    return currentIndustry.categories.map(cat => cat.name);
  };

  // Get industry category objects for enhanced display
  const getIndustryCategoryObjects = () => {
    if (!currentIndustry) return [];
    return currentIndustry.categories;
  };

  // Get unique categories from the registered apps (fallback)
  const getUniqueCategories = (): string[] => {
    const categorySet = new Set<string>(["All"]);
    appsList.forEach(app => {
      app.category.forEach(cat => categorySet.add(cat));
    });
    return Array.from(categorySet).sort();
  };

  const appsByCategory = getAppsByCategory();
  const industryCategories = getIndustryCategories();
  const industryCategoryObjects = getIndustryCategoryObjects();
  const legacyCategories = industryCategories.length > 1 ? industryCategories : getUniqueCategories();
  const featuredApps = appsList.filter(app => app.isHot || app.isNew).slice(0, 6);
  const saleApps = appsList.filter(app => app.isSale).slice(0, 4);

  // Enhanced filtering with advanced search filters
  const getFilteredApps = (): ExtendedApp[] => {
    let filtered = appsList;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(app =>
        app.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.category.some(cat => cat.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply category filter from search filters
    if (searchFilters.categories.length > 0) {
      filtered = filtered.filter(app =>
        app.category.some(cat =>
          searchFilters.categories.some(filterCat =>
            cat.toLowerCase() === filterCat.toLowerCase()
          )
        )
      );
    }

    // Apply selected category filter (from category navigation)
    if (selectedCategory && selectedCategory !== 'All') {
      // Handle subcategory selection (format: "categoryId:subcategoryId")
      if (selectedCategory.includes(':')) {
        const [categoryId, subcategoryId] = selectedCategory.split(':');
        filtered = filtered.filter(app => {
          const belongsToCategory = app.category.some(cat =>
            cat.toLowerCase() === categoryId.toLowerCase()
          );
          if (!belongsToCategory) return false;

          // Simple subcategory matching logic
          const appName = app.name.toLowerCase();
          const appDescription = app.description.toLowerCase();
          const subcategoryName = subcategoryId.toLowerCase().replace('-', ' ');

          return appName.includes(subcategoryName) ||
                 appDescription.includes(subcategoryName) ||
                 app.category.some(cat => cat.toLowerCase().includes(subcategoryName));
        });
      } else {
        // Regular category filter
        filtered = filtered.filter(app =>
          app.category.some(cat => cat.toLowerCase() === selectedCategory.toLowerCase())
        );
      }
    }

    // Apply feature filters
    if (searchFilters.features.length > 0) {
      filtered = filtered.filter(app =>
        searchFilters.features.some(feature => app[feature])
      );
    }

    // Apply hidden filter
    if (!searchFilters.showHidden) {
      filtered = filtered.filter(app => !(app as any).isHidden);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (searchFilters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'category':
          comparison = (a.category[0] || '').localeCompare(b.category[0] || '');
          break;
        case 'rating':
          comparison = (a.rating || 0) - (b.rating || 0);
          break;
        case 'recent':
          if (a.isNew && !b.isNew) comparison = -1;
          else if (!a.isNew && b.isNew) comparison = 1;
          else comparison = a.name.localeCompare(b.name);
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      return searchFilters.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  };

  const filteredApps = getFilteredApps();
  const displayedApps = filteredApps.slice(0, displayedAppsCount);
  const hasMoreApps = filteredApps.length > displayedAppsCount;

  return (
    <PortalAnalytics userId={user?.id || "default"} enabled={true}>
      <div
        className="min-h-screen bg-white"
        style={{
          fontFamily: theme.fontFamily,
          color: theme.colors.text
        }}
      >
      {/* Top Navigation Bar */}
      <header className="border-b border-gray-200">
        {/* Top Bar */}
        <div className="bg-gray-100 text-xs py-1 px-6">
          <div className="w-full flex justify-between items-center">
            <div className="flex space-x-4">
              <span>HR Help Center</span>
              <span>About HR Suite</span>
            </div>
            <div className="flex items-center space-x-4">
              <span>English</span>
              <span>VND ₫</span>
              {user && (
                <div className="flex items-center space-x-2">
                  <Users className="w-3 h-3 text-blue-600" />
                  <span className="text-blue-600">{user.name}</span>
                  <button
                    onClick={() => logout()}
                    className="text-red-600 hover:text-red-700 ml-2"
                    title="Sign Out"
                  >
                    <LogOut className="w-3 h-3" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Header */}
        <div className="w-full py-4 px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link href="/hr" className="text-2xl font-bold text-blue-600">
                HR Portal
              </Link>

              {/* Industry Selector - Show all industries but highlight HR */}
              <IndustrySelector
                industries={allIndustries}
                currentIndustry={currentIndustry}
                onIndustryChange={setIndustry}
                className="hidden lg:block"
              />

              <div className="hidden lg:flex space-x-6 text-sm">
                {(industryCategoryObjects.length > 0 ? industryCategoryObjects : legacyCategories.map(cat => ({ id: cat.toLowerCase(), name: cat, color: '#3b82f6' }))).map((categoryObj, index) => {
                  const categoryName = typeof categoryObj === 'string' ? categoryObj : categoryObj.name;
                  const categoryColor = typeof categoryObj === 'string' ? '#3b82f6' : categoryObj.color;

                  return (
                    <button
                      key={index}
                      onClick={() => selectCategory(categoryName)}
                      className={`transition-colors duration-200 ${
                        selectedCategory === categoryName
                          ? 'font-semibold'
                          : 'hover:opacity-80'
                      }`}
                      style={{
                        color: selectedCategory === categoryName ? categoryColor : '#6b7280'
                      }}
                    >
                      {categoryName}
                    </button>
                  );
                })}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="w-96">
                <AdvancedSearch
                  searchQuery={searchQuery}
                  onSearchChange={handleSearchChange}
                  filters={searchFilters}
                  onFiltersChange={updateSearchFilters}
                  availableCategories={industryCategories.length > 0 ? industryCategories : legacyCategories}
                  availableTags={availableTags}
                  availableDevelopers={availableDevelopers}
                  onTrackSearch={trackSearch}
                  searchHistory={searchHistory}
                  onClearHistory={clearSearchHistory}
                  searchEngine={searchEngine}
                  onSearchResults={handleSearchResults}
                  placeholder="Search HR apps, jobs, recruitment tools..."
                  showAdvancedFilters={true}
                />
              </div>

              <Link href="#" className="p-2 text-gray-600 hover:text-blue-600">
                <User className="w-5 h-5" />
              </Link>

              <Link href="#" className="p-2 text-gray-600 hover:text-blue-600">
                <Heart className="w-5 h-5" />
              </Link>

              <Link href="#" className="p-2 text-gray-600 hover:text-blue-600">
                <ShoppingCart className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>

        {/* Category Nav for mobile/tablet */}
        <div className="lg:hidden border-t border-gray-200 overflow-x-auto whitespace-nowrap px-6 py-2">
          <div className="flex space-x-6">
            {(industryCategoryObjects.length > 0 ? industryCategoryObjects : legacyCategories.map(cat => ({ id: cat.toLowerCase(), name: cat, color: '#3b82f6' }))).map((categoryObj, index) => {
              const categoryName = typeof categoryObj === 'string' ? categoryObj : categoryObj.name;
              const categoryColor = typeof categoryObj === 'string' ? '#3b82f6' : categoryObj.color;

              return (
                <button
                  key={index}
                  onClick={() => selectCategory(categoryName)}
                  className={`text-sm transition-colors whitespace-nowrap ${
                    selectedCategory === categoryName
                      ? 'font-semibold'
                      : 'hover:opacity-80'
                  }`}
                  style={{
                    color: selectedCategory === categoryName ? categoryColor : '#6b7280'
                  }}
                >
                  {categoryName}
                </button>
              );
            })}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="w-full py-6 px-6">
        {/* Hero Banner */}
        <section className="mb-8">
          <div
            className="bg-blue-50 rounded-lg p-8 flex flex-col md:flex-row items-center justify-between"
            style={{ borderRadius: theme.borderRadius }}
          >
            <div className="md:w-1/2 mb-6 md:mb-0">
              <h1 className="text-3xl font-bold mb-4 text-blue-600">
                Explore HR Applications
              </h1>
              <p className="text-gray-600 mb-6">
                Discover our comprehensive suite of HR applications designed for recruitment,
                talent management, and workforce optimization. From job seekers to headhunters,
                we have the right tools for every HR need.
              </p>
              <button
                onClick={() => {
                  const appsSection = document.getElementById('apps-section');
                  if (appsSection) {
                    appsSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="px-6 py-3 text-white font-medium rounded-full shadow-md hover:shadow-lg transition-shadow bg-blue-600"
                style={{ borderRadius: theme.borderRadius }}
              >
                Get Started
              </button>
            </div>
            <div className="md:w-2/5 flex justify-center">
              <div className="relative w-64 h-64 bg-white rounded-full shadow-xl flex items-center justify-center">
                <Users className="w-24 h-24 text-blue-600" />
                <div className="absolute -top-4 -right-4 bg-red-500 text-white text-xs px-3 py-1 rounded-full">
                  HR
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Popular Searches */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Popular HR Searches</h2>
          </div>
          <div className="flex flex-wrap gap-2">
            {popularSearches.map((term, index) => (
              <button
                key={index}
                onClick={() => handleSearch(term)}
                className="px-3 py-1 text-sm border hover:bg-gray-50"
                style={{
                  borderColor: theme.colors.border,
                  borderRadius: theme.borderRadius
                }}
              >
                {term}
              </button>
            ))}
          </div>
        </section>

        {/* Search Results */}
        {showSearchResults && (
          <section className="mb-12">
            <SearchResults
              results={currentSearchResults}
              query={searchQuery}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              onAppSelect={handleAppSelect}
              onToggleFavorite={handleToggleFavorite}
              favoriteApps={preferences.favoriteApps}
              isLoading={false}
              totalResults={currentSearchResults.length}
            />
          </section>
        )}

        {/* Featured Apps */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Featured HR Applications</h2>
            <button
              onClick={() => {
                const appsSection = document.getElementById('apps-section');
                if (appsSection) {
                  appsSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="text-sm flex items-center hover:underline text-blue-600"
            >
              View all <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            {featuredApps.map((app, index) => (
              <div
                key={index}
                onClick={() => handleAppSelect(app)}
                className="group cursor-pointer"
              >
                <div
                  className="bg-gray-50 rounded overflow-hidden mb-2 aspect-[4/5] relative flex items-center justify-center group-hover:shadow-md transition-shadow"
                  style={{
                    borderRadius: theme.borderRadius,
                    backgroundColor: getIconBackground(app.category[0] || '')
                  }}
                >
                  {React.createElement(getAppIcon(app), {
                    className: "w-16 h-16 group-hover:scale-110 transition-transform",
                    style: { color: '#3b82f6' }
                  })}

                  {/* Badges */}
                  {app.isHot && (
                    <div className="absolute top-2 left-2 text-white text-xs px-2 py-0.5 rounded bg-red-500">
                      HOT
                    </div>
                  )}
                  {app.isNew && (
                    <div className="absolute top-2 left-2 text-white text-xs px-2 py-0.5 rounded bg-green-500">
                      NEW
                    </div>
                  )}
                </div>

                <h3 className="text-sm font-medium mb-1 line-clamp-1 group-hover:text-blue-600 transition-colors">{app.name}</h3>

                {/* Rating */}
                <div className="flex items-center mb-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-3 h-3"
                        fill={i < Math.floor(app.rating || 0) ? '#3b82f6' : 'transparent'}
                        stroke="#3b82f6"
                      />
                    ))}
                  </div>
                  <span className="text-xs text-gray-500 ml-1">{app.rating?.toFixed(1)}</span>
                </div>

                <div className="text-sm font-medium text-blue-600">
                  View App
                </div>
              </div>
            ))}
          </div>
        </section>


        {/* Categories Section with Industry-Specific Categories */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-lg font-semibold">Browse by Category</h2>
              {currentIndustry && (
                <p className="text-sm text-gray-600 mt-1">
                  {currentIndustry.description}
                </p>
              )}
            </div>
            <button
              onClick={() => {
                const appsSection = document.getElementById('apps-section');
                if (appsSection) {
                  appsSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="text-sm flex items-center hover:underline text-blue-600"
            >
              View all <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>

          {!portalLoading && categories.length > 0 ? (
            <CategoryExpansion
              categories={categories}
              apps={appsList}
              selectedCategory={selectedCategory}
              onCategorySelect={selectCategory}
              onSubcategorySelect={selectSubcategory}
              expandedCategories={expandedCategories}
              onCategoryExpand={toggleCategoryExpansion}
            />
          ) : industryCategoryObjects.length > 0 ? (
            // Industry-specific category grid
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {industryCategoryObjects.filter(cat => cat.id !== 'all').map((categoryObj, index) => {
                const categoryApps = getAppsByCategoryFromIndustry(categoryObj.id);
                const Icon = getAppIcon({
                  id: `category-${categoryObj.id}`,
                  name: categoryObj.name,
                  path: '',
                  description: categoryObj.description || '',
                  category: [categoryObj.name],
                  isHot: false,
                  isNew: false
                });

                return (
                  <div
                    key={index}
                    onClick={() => selectCategory(categoryObj.name)}
                    className={`
                      group border p-4 hover:shadow-md transition-all cursor-pointer rounded-lg
                      ${selectedCategory === categoryObj.name ? 'ring-2' : ''}
                    `}
                    style={{
                      borderColor: selectedCategory === categoryObj.name ? categoryObj.color : '#e5e7eb'
                    }}
                  >
                    <div className="flex flex-col items-center text-center">
                      <div
                        className="w-12 h-12 rounded-full flex items-center justify-center mb-3 group-hover:scale-110 transition-transform"
                        style={{ backgroundColor: `${categoryObj.color}20`, color: categoryObj.color }}
                      >
                        <Icon className="w-6 h-6" />
                      </div>
                      <h3 className="font-medium text-sm mb-1">{categoryObj.name}</h3>
                      <p className="text-xs text-gray-500 mb-2 line-clamp-2">
                        {categoryObj.description}
                      </p>
                      <span
                        className="text-xs px-2 py-1 rounded-full"
                        style={{
                          backgroundColor: `${categoryObj.color}15`,
                          color: categoryObj.color
                        }}
                      >
                        {categoryApps.length} apps
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            // Fallback to legacy grid view
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {legacyCategories.filter(cat => cat !== 'All').map((category, index) => {
                const categoryApps = appsByCategory[category] || [];
                const Icon = getAppIcon({
                  id: `category-${category.toLowerCase()}`,
                  name: category,
                  path: '',
                  description: '',
                  category: [category],
                  isHot: false,
                  isNew: false
                });

                return (
                  <div
                    key={index}
                    onClick={() => selectCategory(category)}
                    className={`
                      group border p-4 hover:shadow-md transition-all cursor-pointer
                      ${selectedCategory === category ? 'ring-2 ring-blue-500' : ''}
                    `}
                    style={{
                      borderColor: theme.colors.border,
                      borderRadius: theme.borderRadius
                    }}
                  >
                    <div
                      className="flex items-center justify-center mb-3 h-24 rounded"
                      style={{
                        borderRadius: theme.borderRadius,
                        backgroundColor: getIconBackground(category)
                      }}
                    >
                      <Icon className="w-10 h-10 group-hover:scale-110 transition-transform text-blue-600" />
                    </div>
                    <h3 className="text-sm font-medium text-center group-hover:text-blue-600 transition-colors">{category}</h3>
                    <p className="text-xs text-gray-500 text-center mt-1">{categoryApps.length} Applications</p>
                  </div>
                );
              })}
            </div>
          )}
        </section>

        {/* All Applications Grid/List */}
        <section id="apps-section" className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">All HR Applications ({filteredApps.length})</h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'text-blue-600' : 'text-gray-500'}`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'text-blue-600' : 'text-gray-500'}`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>

          {viewMode === 'grid' ? (
            // Grid view
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 xl:grid-cols-8 gap-4">
              {displayedApps.map((app, index) => (
                <div
                  key={index}
                  onClick={() => handleAppSelect(app)}
                  className="group cursor-pointer"
                >
                  <div
                    className="bg-gray-50 rounded overflow-hidden mb-2 aspect-[4/5] relative flex items-center justify-center group-hover:shadow-md transition-shadow"
                    style={{
                      borderRadius: theme.borderRadius,
                      backgroundColor: getIconBackground(app.category[0] || '')
                    }}
                  >
                    {React.createElement(getAppIcon(app), {
                      className: "w-16 h-16 group-hover:scale-110 transition-transform",
                      style: { color: '#3b82f6' }
                    })}

                    {/* Badges */}
                    {app.isHot && (
                      <div className="absolute top-2 left-2 text-white text-xs px-2 py-0.5 rounded bg-red-500">
                        HOT
                      </div>
                    )}
                    {app.isNew && (
                      <div className="absolute top-2 left-2 text-white text-xs px-2 py-0.5 rounded bg-green-500">
                        NEW
                      </div>
                    )}
                    {app.isSale && (
                      <div className="absolute top-2 left-2 text-white text-xs px-2 py-0.5 rounded bg-orange-500">
                        SALE
                      </div>
                    )}
                  </div>

                  <h3 className="text-sm font-medium mb-1 line-clamp-1 group-hover:text-blue-600 transition-colors">{app.name}</h3>

                  {/* Rating */}
                  <div className="flex items-center mb-1">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className="w-3 h-3"
                          fill={i < Math.floor(app.rating || 0) ? '#3b82f6' : 'transparent'}
                          stroke="#3b82f6"
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 ml-1">{app.rating?.toFixed(1)}</span>
                  </div>

                  <div className="text-sm font-medium text-blue-600">
                    View App
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // List view
            <div className="border rounded-lg overflow-hidden" style={{ borderColor: theme.colors.border }}>
              {displayedApps.map((app, index) => (
                <div
                  key={index}
                  onClick={() => handleAppSelect(app)}
                  className={`
                    group flex items-center p-4 hover:bg-gray-50 transition-colors cursor-pointer
                    ${index !== displayedApps.length - 1 ? 'border-b' : ''}
                  `}
                  style={{ borderColor: theme.colors.border }}
                >
                  <div
                    className="w-12 h-12 bg-gray-50 rounded-lg flex items-center justify-center mr-4 relative"
                    style={{
                      borderRadius: theme.borderRadius,
                      backgroundColor: getIconBackground(app.category[0] || '')
                    }}
                  >
                    {React.createElement(getAppIcon(app), {
                      className: "w-6 h-6",
                      style: { color: '#3b82f6' }
                    })}

                    {/* Badges */}
                    {app.isHot && (
                      <div className="absolute -top-1 -right-1 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full bg-red-500">
                        !
                      </div>
                    )}
                    {app.isNew && (
                      <div className="absolute -top-1 -right-1 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full bg-green-500">
                        N
                      </div>
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center">
                      <h3 className="text-sm font-medium line-clamp-1 group-hover:text-blue-600 transition-colors">{app.name}</h3>
                      <div className="flex items-center ml-2">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className="w-3 h-3"
                              fill={i < Math.floor(app.rating || 0) ? '#3b82f6' : 'transparent'}
                              stroke="#3b82f6"
                            />
                          ))}
                        </div>
                        <span className="text-xs text-gray-500 ml-1">{app.rating?.toFixed(1)}</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 line-clamp-1">{app.description}</p>
                  </div>

                  <div className="flex items-center ml-4">
                    {app.category.slice(0, 1).map((cat, idx) => (
                      <span
                        key={idx}
                        className="text-xs px-2 py-0.5 rounded mr-2 bg-blue-50 text-blue-600"
                        style={{ borderRadius: theme.borderRadius }}
                      >
                        {cat}
                      </span>
                    ))}
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
              ))}
            </div>
          )}

          {hasMoreApps && (
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => setDisplayedAppsCount(prev => prev + 20)}
                className="px-8 py-2 border font-medium hover:bg-gray-50 transition-colors text-blue-600"
                style={{
                  borderColor: theme.colors.border,
                  borderRadius: theme.borderRadius
                }}
              >
                Load More ({filteredApps.length - displayedAppsCount} remaining)
              </button>
            </div>
          )}
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 py-12 border-t" style={{ borderColor: theme.colors.border }}>
        <div className="w-full px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="font-semibold mb-4">HR Portal</h3>
              <p className="text-sm text-gray-600 mb-4">
                Comprehensive HR solutions for recruitment, talent management, and workforce optimization.
              </p>
              <div className="flex space-x-3">
                <a href="#" className="text-gray-400 hover:text-blue-600">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd"></path>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-blue-600">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-blue-600">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd"></path>
                  </svg>
                </a>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Categories</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                {legacyCategories.map((category, index) => (
                  <li key={index}>
                    <button
                      onClick={() => selectCategory(category)}
                      className="hover:text-blue-600 text-left"
                    >
                      {category}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Help & Support</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li><a href="#" className="hover:text-blue-600">FAQs</a></li>
                <li><a href="#" className="hover:text-blue-600">Contact Us</a></li>
                <li><a href="#" className="hover:text-blue-600">Documentation</a></li>
                <li><a href="#" className="hover:text-blue-600">API Docs</a></li>
                <li><a href="#" className="hover:text-blue-600">Support Center</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li><a href="#" className="hover:text-blue-600">About Us</a></li>
                <li><a href="#" className="hover:text-blue-600">Careers</a></li>
                <li><a href="#" className="hover:text-blue-600">Press</a></li>
                <li><a href="#" className="hover:text-blue-600">Partners</a></li>
                <li><a href="#" className="hover:text-blue-600">Privacy Policy</a></li>
              </ul>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-200 text-center">
            <p className="text-sm text-gray-500">
              © 2024 ABN Green HR Portal. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
    </PortalAnalytics>
  );
}
