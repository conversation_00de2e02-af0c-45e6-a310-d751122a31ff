'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Briefcase, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Star, 
  Clock, 
  Search, 
  Filter, 
  ChevronRight,
  Brain,
  MessageSquare,
  Target,
  Settings,
  ThumbsUp,
  ThumbsDown,
  Eye,
  MapPin,
  Building
} from 'lucide-react';
import HRUnifiedLayout from '../../shared/components/HRUnifiedLayout';

interface JobMatch {
  job: {
    id: string;
    title: string;
    company: {
      name: string;
      size: string;
      industry: string;
      location: string;
      logo?: string;
    };
    description: string;
    salary: {
      currency: string;
      min: number;
      max: number;
      period: string;
      negotiable: boolean;
    };
    jobLevel: string;
    jobType: string;
    location: string;
    remoteWork: boolean;
    skills: Array<{
      name: string;
      level: string;
      required: boolean;
    }>;
    placementFee: {
      percentage: number;
      currency: string;
      guaranteePeriod: number;
    };
    urgency: string;
    status: string;
    tags: string[];
    postedAt: string;
  };
  matchScore: number;
  matchReasons: string[];
  concerns: string[];
  recommendationLevel: 'high' | 'medium' | 'low';
}

interface HeadHunterProfile {
  id: string;
  name: string;
  specializations: Array<{
    industry: string;
    subIndustries: string[];
    yearsExperience: number;
    placementCount: number;
  }>;
  focusAreas: {
    jobLevels: string[];
    jobTypes: string[];
    salaryRanges: Array<{
      currency: string;
      minSalary: number;
      maxSalary: number;
      period: string;
    }>;
    locations: string[];
    remoteWork: boolean;
    willingToRelocate: boolean;
  };
  performance: {
    totalPlacements: number;
    successRate: number;
    averageTimeToHire: number;
    clientSatisfactionScore: number;
    candidateSatisfactionScore: number;
    totalEarnings: number;
    currentYear: {
      placements: number;
      earnings: number;
      successRate: number;
    };
  };
}

export default function HeadhunterPortal() {
  const [activeTab, setActiveTab] = useState('recommendations');
  const [jobMatches, setJobMatches] = useState<JobMatch[]>([]);
  const [headhunterProfile, setHeadHunterProfile] = useState<HeadHunterProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [filterLevel, setFilterLevel] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [minScore, setMinScore] = useState(50);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchJobMatches();
  }, [filterLevel, minScore]);

  const fetchJobMatches = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        minScore: minScore.toString(),
        limit: '20'
      });
      
      if (filterLevel !== 'all') {
        params.append('recommendationLevel', filterLevel);
      }

      const response = await fetch(`/api/hr/headhunter/jobs/match?${params}`);
      if (response.ok) {
        const data = await response.json();
        setJobMatches(data.matches || []);
        setHeadHunterProfile(data.headhunter);
      } else {
        console.error('Failed to fetch job matches');
      }
    } catch (error) {
      console.error('Error fetching job matches:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleJobFeedback = async (jobId: string, feedback: string, rating?: number) => {
    try {
      await fetch('/api/hr/headhunter/jobs/match/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobId, feedback, rating })
      });
      // Optionally refresh matches or show feedback confirmation
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };

  const formatSalary = (salary: JobMatch['job']['salary']) => {
    const { currency, min, max, period } = salary;
    const symbol = currency === 'USD' ? '$' : currency;
    return `${symbol}${min.toLocaleString()} - ${symbol}${max.toLocaleString()}/${period}`;
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    }
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const getMatchColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-green-50 text-green-700 border-green-200';
      case 'medium': return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'low': return 'bg-gray-50 text-gray-700 border-gray-200';
      default: return 'bg-blue-50 text-blue-700 border-blue-200';
    }
  };

  const filteredMatches = jobMatches.filter(match => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      match.job.title.toLowerCase().includes(searchLower) ||
      match.job.company.name.toLowerCase().includes(searchLower) ||
      match.job.company.industry.toLowerCase().includes(searchLower) ||
      match.job.location.toLowerCase().includes(searchLower)
    );
  });

  return (
    <HRUnifiedLayout 
      title="Headhunter Portal"
      subtitle="AI-powered recruitment with intelligent job matching"
    >
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <p className="mt-2 text-lg text-gray-600">
            Welcome back! Here are your personalized job recommendations and performance metrics.
        </p>
      </div>

      {/* Profile Summary & Performance Dashboard */}
      {headhunterProfile && (
        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-800">{headhunterProfile.name}</h2>
              <div className="flex flex-wrap gap-2 mt-2">
                {headhunterProfile.specializations.map((spec, index) => (
                  <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {spec.industry} ({spec.placementCount} placements)
                  </span>
                ))}
              </div>
            </div>
            <Link
              href="/hr/headhunter/profile/settings"
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Settings className="h-4 w-4 mr-2" />
              Edit Profile
            </Link>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-600">Total Placements</span>
                <Users className="h-5 w-5 text-blue-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{headhunterProfile.performance.totalPlacements}</p>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-green-600">Total Earnings</span>
                <DollarSign className="h-5 w-5 text-green-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">${headhunterProfile.performance.totalEarnings.toLocaleString()}</p>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-purple-600">Success Rate</span>
                <TrendingUp className="h-5 w-5 text-purple-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{headhunterProfile.performance.successRate}%</p>
            </div>
            
            <div className="bg-yellow-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-yellow-600">Avg. Time to Hire</span>
                <Clock className="h-5 w-5 text-yellow-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{headhunterProfile.performance.averageTimeToHire} days</p>
            </div>
            
            <div className="bg-indigo-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-indigo-600">This Year</span>
                <Briefcase className="h-5 w-5 text-indigo-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{headhunterProfile.performance.currentYear.placements}</p>
            </div>
            
            <div className="bg-red-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-red-600">Client Satisfaction</span>
                <Star className="h-5 w-5 text-red-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{headhunterProfile.performance.clientSatisfactionScore}/5</p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex -mb-px">
          <button
            onClick={() => setActiveTab('recommendations')}
            className={`py-4 px-6 text-sm font-medium ${
              activeTab === 'recommendations'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            AI Recommendations
          </button>
          <button
            onClick={() => setActiveTab('submissions')}
            className={`py-4 px-6 text-sm font-medium ${
              activeTab === 'submissions'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            My Submissions
          </button>
          <button
            onClick={() => setActiveTab('candidates')}
            className={`py-4 px-6 text-sm font-medium ${
              activeTab === 'candidates'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            My Candidates
          </button>
        </nav>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Search jobs by title, company, industry, or location..."
              />
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => setFilterLevel(filterLevel === 'all' ? 'high' : 'all')}
                className={`inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                  filterLevel === 'all' 
                    ? 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50' 
                    : 'border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100'
                }`}
              >
                <Target className="h-5 w-5 mr-2" />
                {filterLevel === 'all' ? 'All Jobs' : 'Jobs That Fit My Focus'}
              </button>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2 items-center">
            <span className="text-sm text-gray-600">Filter by match level:</span>
            {(['all', 'high', 'medium', 'low'] as const).map((level) => (
              <button
                key={level}
                onClick={() => setFilterLevel(level)}
                className={`px-3 py-1 text-xs rounded-full border ${
                  filterLevel === level
                    ? level === 'high' 
                      ? 'bg-green-100 text-green-800 border-green-300'
                      : level === 'medium'
                      ? 'bg-yellow-100 text-yellow-800 border-yellow-300'
                      : level === 'low'
                      ? 'bg-gray-100 text-gray-800 border-gray-300'
                      : 'bg-blue-100 text-blue-800 border-blue-300'
                    : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {level === 'all' ? 'All Levels' : `${level.charAt(0).toUpperCase() + level.slice(1)} Match`}
              </button>
            ))}
            
            <div className="flex items-center gap-2 ml-4">
              <span className="text-sm text-gray-600">Min Score:</span>
              <input
                type="range"
                min="0"
                max="100"
                value={minScore}
                onChange={(e) => setMinScore(parseInt(e.target.value))}
                className="w-20"
              />
              <span className="text-sm font-medium text-gray-800">{minScore}%</span>
            </div>
          </div>
          
          {jobMatches.length > 0 && (
            <div className="text-sm text-gray-600">
              Found {filteredMatches.length} job{filteredMatches.length !== 1 ? 's' : ''} matching your criteria
            </div>
          )}
        </div>
      </div>

      {/* Job Recommendations */}
      {activeTab === 'recommendations' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-800">
              Intelligent Job Matching
              {headhunterProfile && (
                <span className="text-sm font-normal text-gray-600 ml-2">
                  Based on your {headhunterProfile.specializations.map(s => s.industry).join(', ')} expertise
                </span>
              )}
            </h2>
            <Link
              href="/hr/headhunter/jobs"
              className="text-sm font-medium text-blue-600 hover:text-blue-800 flex items-center"
            >
              Browse all jobs
              <ChevronRight className="h-4 w-4 ml-1" />
            </Link>
          </div>

          {loading ? (
            <div className="bg-white rounded-xl shadow-sm border p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading personalized job matches...</p>
            </div>
          ) : filteredMatches.length === 0 ? (
            <div className="bg-white rounded-xl shadow-sm border p-8 text-center">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No matching jobs found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || filterLevel !== 'all' || minScore > 0
                  ? 'Try adjusting your filters or search criteria.'
                  : 'Complete your headhunter profile to receive personalized job recommendations.'}
              </p>
              <Link
                href="/hr/headhunter/profile/settings"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Settings className="h-4 w-4 mr-2" />
                Complete Profile
              </Link>
            </div>
          ) : (
            filteredMatches.map((match) => (
              <div key={match.job.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{match.job.title}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Building className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">{match.job.company.name}</span>
                            <span className="text-gray-400">•</span>
                            <span className="text-gray-600">{match.job.company.industry}</span>
                          </div>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                            <div className="flex items-center">
                              <MapPin className="h-4 w-4 mr-1" />
                              {match.job.location}
                              {match.job.remoteWork && <span className="text-blue-600 ml-1">(Remote)</span>}
                            </div>
                            <div className="flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              {getTimeAgo(match.job.postedAt)}
                            </div>
                            <div className="flex items-center">
                              <DollarSign className="h-4 w-4 mr-1" />
                              {formatSalary(match.job.salary)}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <div className={`flex items-center px-3 py-1 rounded-full border ${getMatchColor(match.recommendationLevel)}`}>
                            <Star className="h-4 w-4 mr-1" fill="currentColor" />
                            <span className="text-sm font-medium">{match.matchScore}% Match</span>
                          </div>
                          {match.job.urgency === 'high' && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Urgent
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <p className="text-gray-700 text-sm mb-3 line-clamp-2">{match.job.description}</p>
                    
                    <div className="flex flex-wrap gap-2 mb-3">
                      {match.job.skills.slice(0, 5).map((skill, index) => (
                        <span 
                          key={index} 
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            skill.required 
                              ? 'bg-blue-100 text-blue-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {skill.name}
                          {skill.required && ' *'}
                        </span>
                      ))}
                      {match.job.skills.length > 5 && (
                        <span className="text-xs text-gray-500">+{match.job.skills.length - 5} more</span>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Job Level:</span>
                        <span className="ml-2 capitalize">{match.job.jobLevel}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Placement Fee:</span>
                        <span className="ml-2 text-green-600 font-medium">{match.job.placementFee.percentage}%</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Match Reasons */}
                  <div className="mb-4 p-3 bg-green-50 rounded-lg">
                    <div className="flex items-start">
                      <Brain className="h-5 w-5 text-green-600 mr-2 flex-shrink-0 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-green-800 mb-1">Why this job matches you:</p>
                        <ul className="text-sm text-green-700 space-y-1">
                          {match.matchReasons.slice(0, 3).map((reason, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-green-600 mr-1">•</span>
                              {reason}
                            </li>
                          ))}
                        </ul>
                        {match.matchReasons.length > 3 && (
                          <p className="text-xs text-green-600 mt-1">+{match.matchReasons.length - 3} more reasons</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Concerns */}
                  {match.concerns.length > 0 && (
                    <div className="mb-4 p-3 bg-yellow-50 rounded-lg">
                      <div className="flex items-start">
                        <MessageSquare className="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-yellow-800 mb-1">Consider these factors:</p>
                          <ul className="text-sm text-yellow-700 space-y-1">
                            {match.concerns.slice(0, 2).map((concern, index) => (
                              <li key={index} className="flex items-start">
                                <span className="text-yellow-600 mr-1">•</span>
                                {concern}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex flex-wrap gap-3">
                    <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                      Submit Candidate
                    </button>
                    <button className="flex-1 bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md border border-gray-300 transition-colors">
                      Save for Later
                    </button>
                    <div className="flex gap-1">
                      <button
                        onClick={() => handleJobFeedback(match.job.id, 'relevant')}
                        className="p-2 text-gray-400 hover:text-green-600 border border-gray-300 rounded-md hover:border-green-300 transition-colors"
                        title="Mark as relevant"
                      >
                        <ThumbsUp className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleJobFeedback(match.job.id, 'not-relevant')}
                        className="p-2 text-gray-400 hover:text-red-600 border border-gray-300 rounded-md hover:border-red-300 transition-colors"
                        title="Mark as not relevant"
                      >
                        <ThumbsDown className="h-4 w-4" />
                      </button>
                      <Link
                        href={`/hr/headhunter/jobs/${match.job.id}`}
                        className="p-2 text-gray-400 hover:text-blue-600 border border-gray-300 rounded-md hover:border-blue-300 transition-colors"
                        title="View details"
                      >
                        <Eye className="h-4 w-4" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Placeholder for other tabs */}
      {activeTab === 'submissions' && (
        <div className="bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">My Submissions</h2>
          <p className="text-gray-600">Track your candidate submissions and their progress here.</p>
          {/* Submissions content would go here */}
        </div>
      )}

      {activeTab === 'candidates' && (
        <div className="bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">My Candidates</h2>
          <p className="text-gray-600">Manage your candidate database and track their progress.</p>
          {/* Candidates content would go here */}
        </div>
      )}
      </div>
    </HRUnifiedLayout>
  );
} 