'use client';

import { useState, useEffect } from 'react';
import { ArrowLeft, Plus, X, Save, Target, MapPin, DollarSign, Building, Clock } from 'lucide-react';
import Link from 'next/link';

interface HeadHunterProfile {
  id: string;
  userId: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  title: string;
  yearsOfExperience: number;
  location: string;
  specializations: Array<{
    industry: string;
    subIndustries: string[];
    yearsExperience: number;
    placementCount: number;
  }>;
  focusAreas: {
    jobLevels: string[];
    jobTypes: string[];
    salaryRanges: Array<{
      currency: string;
      minSalary: number;
      maxSalary: number;
      period: string;
    }>;
    locations: string[];
    remoteWork: boolean;
    willingToRelocate: boolean;
  };
  preferences: {
    preferredIndustries: string[];
    avoidIndustries: string[];
    preferredCompanySizes: string[];
    workingHours: string;
    communicationStyle: string;
    candidateTypes: string[];
    placementFeeRange: {
      min: number;
      max: number;
      unit: string;
    };
  };
}

const industryOptions = [
  'Technology', 'Finance', 'Healthcare', 'Manufacturing', 'Retail',
  'Education', 'Real Estate', 'Media', 'Consulting', 'Legal',
  'Marketing', 'Sales', 'Operations', 'Human Resources', 'Logistics'
];

const jobLevelOptions = ['entry', 'junior', 'mid', 'senior', 'lead', 'executive'];
const jobTypeOptions = ['full-time', 'part-time', 'contract', 'freelance', 'internship'];
const companySizeOptions = ['startup', 'small', 'medium', 'large', 'enterprise'];
const currencyOptions = ['USD', 'VND', 'SGD', 'EUR'];

export default function HeadHunterProfileSettings() {
  const [profile, setProfile] = useState<HeadHunterProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    // Load existing profile or create default
    setProfile({
      id: 'hh_001',
      userId: 'user_hh_001',
      name: 'Sarah Chen',
      email: '<EMAIL>',
      phone: '+84 9123 456 789',
      company: 'Elite Talent Solutions',
      title: 'Senior Technical Headhunter',
      yearsOfExperience: 8,
      location: 'Ho Chi Minh City',
      specializations: [
        {
          industry: 'Technology',
          subIndustries: ['Software Development', 'AI/ML', 'Fintech'],
          yearsExperience: 8,
          placementCount: 45
        }
      ],
      focusAreas: {
        jobLevels: ['mid', 'senior', 'lead'],
        jobTypes: ['full-time', 'contract'],
        salaryRanges: [
          {
            currency: 'USD',
            minSalary: 3000,
            maxSalary: 15000,
            period: 'monthly'
          }
        ],
        locations: ['Ho Chi Minh City', 'Hanoi', 'Singapore', 'Remote'],
        remoteWork: true,
        willingToRelocate: true
      },
      preferences: {
        preferredIndustries: ['Technology', 'Finance'],
        avoidIndustries: ['Manufacturing'],
        preferredCompanySizes: ['startup', 'medium', 'large'],
        workingHours: 'flexible',
        communicationStyle: 'direct',
        candidateTypes: ['passive', 'active'],
        placementFeeRange: {
          min: 15,
          max: 30,
          unit: 'percentage'
        }
      }
    });
    setLoading(false);
  }, []);

  const handleSave = async () => {
    if (!profile) return;
    
    setSaving(true);
    try {
      // Here you would make an API call to save the profile
      console.log('Saving profile:', profile);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      alert('Profile saved successfully!');
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Error saving profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const addSpecialization = () => {
    if (!profile) return;
    setProfile({
      ...profile,
      specializations: [
        ...profile.specializations,
        {
          industry: '',
          subIndustries: [],
          yearsExperience: 0,
          placementCount: 0
        }
      ]
    });
  };

  const removeSpecialization = (index: number) => {
    if (!profile) return;
    setProfile({
      ...profile,
      specializations: profile.specializations.filter((_, i) => i !== index)
    });
  };

  const updateSpecialization = (index: number, field: string, value: any) => {
    if (!profile) return;
    const updated = [...profile.specializations];
    updated[index] = { ...updated[index], [field]: value };
    setProfile({ ...profile, specializations: updated });
  };

  const addSalaryRange = () => {
    if (!profile) return;
    setProfile({
      ...profile,
      focusAreas: {
        ...profile.focusAreas,
        salaryRanges: [
          ...profile.focusAreas.salaryRanges,
          {
            currency: 'USD',
            minSalary: 0,
            maxSalary: 0,
            period: 'monthly'
          }
        ]
      }
    });
  };

  const removeSalaryRange = (index: number) => {
    if (!profile) return;
    setProfile({
      ...profile,
      focusAreas: {
        ...profile.focusAreas,
        salaryRanges: profile.focusAreas.salaryRanges.filter((_, i) => i !== index)
      }
    });
  };

  const updateSalaryRange = (index: number, field: string, value: any) => {
    if (!profile) return;
    const updated = [...profile.focusAreas.salaryRanges];
    updated[index] = { ...updated[index], [field]: value };
    setProfile({
      ...profile,
      focusAreas: { ...profile.focusAreas, salaryRanges: updated }
    });
  };

  const toggleArrayValue = (array: string[], value: string): string[] => {
    return array.includes(value)
      ? array.filter(item => item !== value)
      : [...array, value];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!profile) return null;

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Link 
            href="/hr/headhunter/headhunter"
            className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back to Dashboard
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Headhunter Profile Settings</h1>
            <p className="text-gray-600">Configure your specializations and job matching preferences</p>
          </div>
        </div>
        
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <Save className="w-4 h-4 mr-2" />
          {saving ? 'Saving...' : 'Save Profile'}
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="flex -mb-px">
          {[
            { id: 'basic', label: 'Basic Info', icon: Target },
            { id: 'specializations', label: 'Specializations', icon: Building },
            { id: 'focus', label: 'Focus Areas', icon: MapPin },
            { id: 'preferences', label: 'Preferences', icon: Clock }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-6 text-sm font-medium flex items-center ${
                activeTab === tab.id
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Basic Info Tab */}
      {activeTab === 'basic' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Basic Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
              <input
                type="text"
                value={profile.name}
                onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input
                type="email"
                value={profile.email}
                onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
              <input
                type="tel"
                value={profile.phone}
                onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
              <input
                type="text"
                value={profile.company}
                onChange={(e) => setProfile({ ...profile, company: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
              <input
                type="text"
                value={profile.title}
                onChange={(e) => setProfile({ ...profile, title: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Years of Experience</label>
              <input
                type="number"
                value={profile.yearsOfExperience}
                onChange={(e) => setProfile({ ...profile, yearsOfExperience: parseInt(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
              <input
                type="text"
                value={profile.location}
                onChange={(e) => setProfile({ ...profile, location: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="City, Country"
              />
            </div>
          </div>
        </div>
      )}

      {/* Specializations Tab */}
      {activeTab === 'specializations' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">Industry Specializations</h2>
            <button
              onClick={addSpecialization}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Specialization
            </button>
          </div>
          
          <div className="space-y-6">
            {profile.specializations.map((spec, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-md font-medium text-gray-900">Specialization {index + 1}</h3>
                  <button
                    onClick={() => removeSpecialization(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Industry</label>
                    <select
                      value={spec.industry}
                      onChange={(e) => updateSpecialization(index, 'industry', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select Industry</option>
                      {industryOptions.map(industry => (
                        <option key={industry} value={industry}>{industry}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Years Experience</label>
                    <input
                      type="number"
                      value={spec.yearsExperience}
                      onChange={(e) => updateSpecialization(index, 'yearsExperience', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Placement Count</label>
                    <input
                      type="number"
                      value={spec.placementCount}
                      onChange={(e) => updateSpecialization(index, 'placementCount', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Focus Areas Tab */}
      {activeTab === 'focus' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Focus Areas</h2>
          
          <div className="space-y-8">
            {/* Job Levels */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Preferred Job Levels</label>
              <div className="flex flex-wrap gap-2">
                {jobLevelOptions.map(level => (
                  <button
                    key={level}
                    onClick={() => setProfile({
                      ...profile,
                      focusAreas: {
                        ...profile.focusAreas,
                        jobLevels: toggleArrayValue(profile.focusAreas.jobLevels, level)
                      }
                    })}
                    className={`px-3 py-2 rounded-lg border text-sm font-medium ${
                      profile.focusAreas.jobLevels.includes(level)
                        ? 'bg-blue-100 text-blue-800 border-blue-300'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {level.charAt(0).toUpperCase() + level.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Job Types */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Preferred Job Types</label>
              <div className="flex flex-wrap gap-2">
                {jobTypeOptions.map(type => (
                  <button
                    key={type}
                    onClick={() => setProfile({
                      ...profile,
                      focusAreas: {
                        ...profile.focusAreas,
                        jobTypes: toggleArrayValue(profile.focusAreas.jobTypes, type)
                      }
                    })}
                    className={`px-3 py-2 rounded-lg border text-sm font-medium ${
                      profile.focusAreas.jobTypes.includes(type)
                        ? 'bg-blue-100 text-blue-800 border-blue-300'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}
                  </button>
                ))}
              </div>
            </div>

            {/* Salary Ranges */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">Salary Ranges</label>
                <button
                  onClick={addSalaryRange}
                  className="inline-flex items-center px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                >
                  <Plus className="w-3 h-3 mr-1" />
                  Add Range
                </button>
              </div>
              
              <div className="space-y-3">
                {profile.focusAreas.salaryRanges.map((range, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
                    <select
                      value={range.currency}
                      onChange={(e) => updateSalaryRange(index, 'currency', e.target.value)}
                      className="px-2 py-1 border border-gray-300 rounded text-sm"
                    >
                      {currencyOptions.map(currency => (
                        <option key={currency} value={currency}>{currency}</option>
                      ))}
                    </select>
                    
                    <input
                      type="number"
                      value={range.minSalary}
                      onChange={(e) => updateSalaryRange(index, 'minSalary', parseInt(e.target.value) || 0)}
                      placeholder="Min"
                      className="w-24 px-2 py-1 border border-gray-300 rounded text-sm"
                    />
                    
                    <span className="text-gray-500">to</span>
                    
                    <input
                      type="number"
                      value={range.maxSalary}
                      onChange={(e) => updateSalaryRange(index, 'maxSalary', parseInt(e.target.value) || 0)}
                      placeholder="Max"
                      className="w-24 px-2 py-1 border border-gray-300 rounded text-sm"
                    />
                    
                    <select
                      value={range.period}
                      onChange={(e) => updateSalaryRange(index, 'period', e.target.value)}
                      className="px-2 py-1 border border-gray-300 rounded text-sm"
                    >
                      <option value="hourly">per hour</option>
                      <option value="monthly">per month</option>
                      <option value="yearly">per year</option>
                    </select>
                    
                    <button
                      onClick={() => removeSalaryRange(index)}
                      className="text-red-600 hover:text-red-800 p-1"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Location Preferences */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Preferred Locations</label>
              <textarea
                value={profile.focusAreas.locations.join(', ')}
                onChange={(e) => setProfile({
                  ...profile,
                  focusAreas: {
                    ...profile.focusAreas,
                    locations: e.target.value.split(',').map(loc => loc.trim()).filter(Boolean)
                  }
                })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter locations separated by commas (e.g., Singapore, Ho Chi Minh City, Remote)"
              />
            </div>

            {/* Work Preferences */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={profile.focusAreas.remoteWork}
                    onChange={(e) => setProfile({
                      ...profile,
                      focusAreas: { ...profile.focusAreas, remoteWork: e.target.checked }
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Accept remote work opportunities</span>
                </label>
              </div>
              
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={profile.focusAreas.willingToRelocate}
                    onChange={(e) => setProfile({
                      ...profile,
                      focusAreas: { ...profile.focusAreas, willingToRelocate: e.target.checked }
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Willing to help with relocation</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preferences Tab */}
      {activeTab === 'preferences' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Job Matching Preferences</h2>
          
          <div className="space-y-8">
            {/* Preferred Industries */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Preferred Industries</label>
              <div className="flex flex-wrap gap-2">
                {industryOptions.map(industry => (
                  <button
                    key={industry}
                    onClick={() => setProfile({
                      ...profile,
                      preferences: {
                        ...profile.preferences,
                        preferredIndustries: toggleArrayValue(profile.preferences.preferredIndustries, industry)
                      }
                    })}
                    className={`px-3 py-2 rounded-lg border text-sm font-medium ${
                      profile.preferences.preferredIndustries.includes(industry)
                        ? 'bg-green-100 text-green-800 border-green-300'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {industry}
                  </button>
                ))}
              </div>
            </div>

            {/* Avoid Industries */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Industries to Avoid</label>
              <div className="flex flex-wrap gap-2">
                {industryOptions.map(industry => (
                  <button
                    key={industry}
                    onClick={() => setProfile({
                      ...profile,
                      preferences: {
                        ...profile.preferences,
                        avoidIndustries: toggleArrayValue(profile.preferences.avoidIndustries, industry)
                      }
                    })}
                    className={`px-3 py-2 rounded-lg border text-sm font-medium ${
                      profile.preferences.avoidIndustries.includes(industry)
                        ? 'bg-red-100 text-red-800 border-red-300'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {industry}
                  </button>
                ))}
              </div>
            </div>

            {/* Company Sizes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Preferred Company Sizes</label>
              <div className="flex flex-wrap gap-2">
                {companySizeOptions.map(size => (
                  <button
                    key={size}
                    onClick={() => setProfile({
                      ...profile,
                      preferences: {
                        ...profile.preferences,
                        preferredCompanySizes: toggleArrayValue(profile.preferences.preferredCompanySizes, size)
                      }
                    })}
                    className={`px-3 py-2 rounded-lg border text-sm font-medium ${
                      profile.preferences.preferredCompanySizes.includes(size)
                        ? 'bg-blue-100 text-blue-800 border-blue-300'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {size.charAt(0).toUpperCase() + size.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Placement Fee Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Placement Fee Range (%)</label>
              <div className="flex items-center gap-4">
                <input
                  type="number"
                  value={profile.preferences.placementFeeRange.min}
                  onChange={(e) => setProfile({
                    ...profile,
                    preferences: {
                      ...profile.preferences,
                      placementFeeRange: {
                        ...profile.preferences.placementFeeRange,
                        min: parseInt(e.target.value) || 0
                      }
                    }
                  })}
                  className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Min"
                />
                <span className="text-gray-500">%</span>
                <span className="text-gray-500">to</span>
                <input
                  type="number"
                  value={profile.preferences.placementFeeRange.max}
                  onChange={(e) => setProfile({
                    ...profile,
                    preferences: {
                      ...profile.preferences,
                      placementFeeRange: {
                        ...profile.preferences.placementFeeRange,
                        max: parseInt(e.target.value) || 0
                      }
                    }
                  })}
                  className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Max"
                />
                <span className="text-gray-500">%</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}