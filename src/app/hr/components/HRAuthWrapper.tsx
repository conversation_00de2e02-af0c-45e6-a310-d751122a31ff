'use client';

import React, { ReactNode } from 'react';
import { useHRAuthGuard } from '../hooks/useHRAuth';
import { useHRRouter } from '../providers/HRRouterProvider';
import { Users, Shield, AlertTriangle } from 'lucide-react';

interface HRAuthWrapperProps {
  children: ReactNode;
  requiredPermissions?: string[];
  moduleName?: string;
  fallbackComponent?: ReactNode;
}

export default function HRAuthWrapper({ 
  children, 
  requiredPermissions = ['hr:access'],
  moduleName,
  fallbackComponent 
}: HRAuthWrapperProps) {
  const auth = useHRAuthGuard(requiredPermissions);
  const hrRouter = useHRRouter();

  // Show loading state
  if (auth.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6"></div>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">Loading HR {moduleName || 'Module'}...</h3>
            <p className="text-gray-600">Verifying OneID authentication</p>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied state if user doesn't have permission
  if (!auth.hasAccess) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl flex items-center justify-center">
                <Shield className="w-8 h-8 text-white" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p className="text-gray-600">You don't have permission to access this HR module</p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
            <div className="flex items-center gap-3 mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
              <AlertTriangle className="w-6 h-6 text-red-600" />
              <div>
                <h3 className="font-semibold text-red-800">Insufficient Permissions</h3>
                <p className="text-sm text-red-600">
                  Required: {requiredPermissions.join(', ')}
                </p>
              </div>
            </div>

            {moduleName && (
              <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-xl">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="w-4 h-4 text-gray-600" />
                  <span className="font-medium text-gray-700">Requested Module</span>
                </div>
                <p className="text-sm text-gray-600 capitalize">{moduleName}</p>
              </div>
            )}

            <div className="text-center space-y-4">
              <p className="text-gray-600 text-sm">
                Contact your HR administrator to request access to this module.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={() => hrRouter.toHome()}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Back to HR Suite
                </button>
                <button
                  onClick={() => auth.logout()}
                  className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                >
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // User has access, render children
  return <>{children}</>;
}

// Specialized wrappers for specific HR modules
export function HunterAuthWrapper({ children }: { children: ReactNode }) {
  return (
    <HRAuthWrapper 
      requiredPermissions={['hr:recruitment:access', 'hr:jobs:manage']}
      moduleName="Hunter Job Portal"
    >
      {children}
    </HRAuthWrapper>
  );
}

export function UpworkAuthWrapper({ children }: { children: ReactNode }) {
  return (
    <HRAuthWrapper 
      requiredPermissions={['hr:recruitment:access', 'hr:upwork:access']}
      moduleName="Upwork Clone"
    >
      {children}
    </HRAuthWrapper>
  );
}

export function RFPAuthWrapper({ children }: { children: ReactNode }) {
  return (
    <HRAuthWrapper 
      requiredPermissions={['hr:procurement:access', 'hr:manager:access']}
      moduleName="ABN RFP Platform"
    >
      {children}
    </HRAuthWrapper>
  );
}

export function RefereeAuthWrapper({ children }: { children: ReactNode }) {
  return (
    <HRAuthWrapper 
      requiredPermissions={['hr:legal:access', 'hr:disputes:manage']}
      moduleName="ABN Referee"
    >
      {children}
    </HRAuthWrapper>
  );
}

export function HRMAuthWrapper({ children }: { children: ReactNode }) {
  return (
    <HRAuthWrapper 
      requiredPermissions={['hr:employee:access', 'hr:hrm:access']}
      moduleName="HR Management"
    >
      {children}
    </HRAuthWrapper>
  );
}

export function HeadhunterAuthWrapper({ children }: { children: ReactNode }) {
  return (
    <HRAuthWrapper 
      requiredPermissions={['hr:recruitment:access', 'hr:ai:access']}
      moduleName="AI Headhunter"
    >
      {children}
    </HRAuthWrapper>
  );
}

export function OnboardingAuthWrapper({ children }: { children: ReactNode }) {
  return (
    <HRAuthWrapper 
      requiredPermissions={['hr:onboarding:access', 'hr:employee:access']}
      moduleName="Employee Onboarding"
    >
      {children}
    </HRAuthWrapper>
  );
}

export function TalentsAuthWrapper({ children }: { children: ReactNode }) {
  return (
    <HRAuthWrapper 
      requiredPermissions={['hr:global:access', 'hr:eor:access']}
      moduleName="Global Hiring & Talents"
    >
      {children}
    </HRAuthWrapper>
  );
}