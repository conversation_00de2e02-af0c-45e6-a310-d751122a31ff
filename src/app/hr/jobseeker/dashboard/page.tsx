'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, MapPin, Clock, DollarSign, Briefcase, User, Heart, Eye, Building, Calendar } from 'lucide-react';
import Link from 'next/link';

interface JobPosting {
  id: string;
  title: string;
  company: {
    name: string;
    logo?: string;
  };
  location: string;
  employmentType: string;
  experienceLevel: string;
  salaryMin?: number;
  salaryMax?: number;
  salaryType?: string;
  remoteWork: boolean;
  isUrgent: boolean;
  description: string;
  skills: Array<{
    name: string;
    level: string;
    isRequired: boolean;
  }>;
  createdAt: string;
  applicationDeadline?: string;
}

interface Application {
  id: string;
  jobPostingId: string;
  jobTitle: string;
  company: string;
  status: string;
  appliedAt: string;
}

const mockJobs: JobPosting[] = [
  {
    id: '1',
    title: 'Senior Software Engineer',
    company: { name: 'TechCorp Singapore', logo: null },
    location: 'Singapore',
    employmentType: 'full-time',
    experienceLevel: 'senior',
    salaryMin: 8000,
    salaryMax: 12000,
    salaryType: 'monthly',
    remoteWork: true,
    isUrgent: false,
    description: 'We are looking for a Senior Software Engineer to join our growing team...',
    skills: [
      { name: 'React', level: 'advanced', isRequired: true },
      { name: 'Node.js', level: 'intermediate', isRequired: true },
      { name: 'TypeScript', level: 'intermediate', isRequired: false }
    ],
    createdAt: '2025-01-15T10:00:00Z',
    applicationDeadline: '2025-02-15'
  },
  {
    id: '2',
    title: 'Product Manager',
    company: { name: 'Innovation Labs', logo: null },
    location: 'Remote',
    employmentType: 'full-time',
    experienceLevel: 'mid',
    salaryMin: 6000,
    salaryMax: 9000,
    salaryType: 'monthly',
    remoteWork: true,
    isUrgent: true,
    description: 'Join our product team to drive innovation and user experience...',
    skills: [
      { name: 'Product Strategy', level: 'advanced', isRequired: true },
      { name: 'Data Analysis', level: 'intermediate', isRequired: true }
    ],
    createdAt: '2025-01-10T14:30:00Z'
  }
];

const mockApplications: Application[] = [
  {
    id: '1',
    jobPostingId: '1',
    jobTitle: 'Frontend Developer',
    company: 'StartupXYZ',
    status: 'interview',
    appliedAt: '2025-01-12T09:00:00Z'
  },
  {
    id: '2',
    jobPostingId: '2',
    jobTitle: 'UX Designer',
    company: 'Design Studio',
    status: 'applied',
    appliedAt: '2025-01-10T16:45:00Z'
  }
];

const statusColors = {
  applied: 'bg-blue-100 text-blue-800',
  screening: 'bg-yellow-100 text-yellow-800',
  interview: 'bg-purple-100 text-purple-800',
  offer: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
  withdrawn: 'bg-gray-100 text-gray-800'
};

export default function JobSeekerDashboard() {
  const [activeTab, setActiveTab] = useState('jobs');
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [jobTypeFilter, setJobTypeFilter] = useState('');
  const [experienceFilter, setExperienceFilter] = useState('');
  const [remoteFilter, setRemoteFilter] = useState('');

  const filteredJobs = mockJobs.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesLocation = !locationFilter || job.location.toLowerCase().includes(locationFilter.toLowerCase());
    const matchesJobType = !jobTypeFilter || job.employmentType === jobTypeFilter;
    const matchesExperience = !experienceFilter || job.experienceLevel === experienceFilter;
    const matchesRemote = !remoteFilter || (remoteFilter === 'remote' ? job.remoteWork : !job.remoteWork);

    return matchesSearch && matchesLocation && matchesJobType && matchesExperience && matchesRemote;
  });

  const formatSalary = (min?: number, max?: number, type?: string) => {
    if (!min && !max) return 'Salary not specified';
    const currency = 'S$';
    const period = type === 'yearly' ? '/year' : type === 'monthly' ? '/month' : '/hour';
    
    if (min && max) {
      return `${currency}${min.toLocaleString()} - ${currency}${max.toLocaleString()} ${period}`;
    }
    return `${currency}${(min || max)?.toLocaleString()} ${period}`;
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    }
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Job Seeker Dashboard</h1>
              <p className="text-gray-600">Find your next opportunity</p>
            </div>
            <div className="flex items-center gap-4">
              <Link
                href="/hr/jobseeker/profile"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                <User className="w-4 h-4 mr-2" />
                My Profile
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm text-gray-600">Active Applications</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockApplications.filter(app => !['rejected', 'withdrawn'].includes(app.status)).length}
                </p>
              </div>
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Briefcase className="w-4 h-4 text-blue-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm text-gray-600">Interview Invites</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockApplications.filter(app => app.status === 'interview').length}
                </p>
              </div>
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Calendar className="w-4 h-4 text-purple-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm text-gray-600">Job Offers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockApplications.filter(app => app.status === 'offer').length}
                </p>
              </div>
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Heart className="w-4 h-4 text-green-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm text-gray-600">Profile Views</p>
                <p className="text-2xl font-bold text-gray-900">24</p>
              </div>
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <Eye className="w-4 h-4 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('jobs')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'jobs'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Browse Jobs ({filteredJobs.length})
              </button>
              <button
                onClick={() => setActiveTab('applications')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'applications'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                My Applications ({mockApplications.length})
              </button>
            </nav>
          </div>

          {/* Jobs Tab */}
          {activeTab === 'jobs' && (
            <div className="p-6">
              {/* Search and Filters */}
              <div className="mb-6 space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search jobs by title, company, or keywords..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                    <Filter className="w-4 h-4 mr-2" />
                    Filters
                  </button>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <input
                    type="text"
                    placeholder="Location"
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <select
                    value={jobTypeFilter}
                    onChange={(e) => setJobTypeFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Job Types</option>
                    <option value="full-time">Full-time</option>
                    <option value="part-time">Part-time</option>
                    <option value="contract">Contract</option>
                    <option value="freelance">Freelance</option>
                    <option value="internship">Internship</option>
                  </select>
                  <select
                    value={experienceFilter}
                    onChange={(e) => setExperienceFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Levels</option>
                    <option value="entry">Entry Level</option>
                    <option value="junior">Junior</option>
                    <option value="mid">Mid Level</option>
                    <option value="senior">Senior</option>
                    <option value="lead">Lead</option>
                    <option value="executive">Executive</option>
                  </select>
                  <select
                    value={remoteFilter}
                    onChange={(e) => setRemoteFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Locations</option>
                    <option value="remote">Remote Only</option>
                    <option value="onsite">On-site Only</option>
                  </select>
                </div>
              </div>

              {/* Job Listings */}
              <div className="space-y-4">
                {filteredJobs.map((job) => (
                  <div key={job.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{job.title}</h3>
                          {job.isUrgent && (
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                              Urgent
                            </span>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                          <div className="flex items-center">
                            <Building className="w-4 h-4 mr-1" />
                            {job.company.name}
                          </div>
                          <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-1" />
                            {job.location}
                            {job.remoteWork && <span className="ml-1 text-blue-600">(Remote)</span>}
                          </div>
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {getTimeAgo(job.createdAt)}
                          </div>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                          <span className="capitalize">{job.employmentType.replace('-', ' ')}</span>
                          <span className="capitalize">{job.experienceLevel} Level</span>
                          <div className="flex items-center">
                            <DollarSign className="w-4 h-4 mr-1" />
                            {formatSalary(job.salaryMin, job.salaryMax, job.salaryType)}
                          </div>
                        </div>
                        <p className="text-gray-700 mb-4 line-clamp-2">{job.description}</p>
                        <div className="flex flex-wrap gap-2">
                          {job.skills.slice(0, 4).map((skill, index) => (
                            <span
                              key={index}
                              className={`inline-flex px-2 py-1 text-xs rounded-full ${
                                skill.isRequired
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-gray-100 text-gray-700'
                              }`}
                            >
                              {skill.name}
                              {skill.isRequired && ' *'}
                            </span>
                          ))}
                          {job.skills.length > 4 && (
                            <span className="text-xs text-gray-500">+{job.skills.length - 4} more</span>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-col gap-2 ml-6">
                        <Link
                          href={`/hr/jobseeker/jobs/${job.id}`}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-center"
                        >
                          View Details
                        </Link>
                        <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-center">
                          Save Job
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

                {filteredJobs.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-gray-500">
                      <p className="text-lg mb-2">No jobs found</p>
                      <p className="text-sm">Try adjusting your search criteria or filters.</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Applications Tab */}
          {activeTab === 'applications' && (
            <div className="p-6">
              <div className="space-y-4">
                {mockApplications.map((application) => (
                  <div key={application.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {application.jobTitle}
                        </h3>
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center">
                            <Building className="w-4 h-4 mr-1" />
                            {application.company}
                          </div>
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            Applied {getTimeAgo(application.appliedAt)}
                          </div>
                        </div>
                        <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${statusColors[application.status as keyof typeof statusColors]}`}>
                          {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                        </span>
                      </div>
                      <div className="flex gap-2 ml-6">
                        <Link
                          href={`/hr/jobseeker/applications/${application.id}`}
                          className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}

                {mockApplications.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-gray-500">
                      <p className="text-lg mb-2">No applications yet</p>
                      <p className="text-sm">Start applying to jobs to see your applications here.</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}