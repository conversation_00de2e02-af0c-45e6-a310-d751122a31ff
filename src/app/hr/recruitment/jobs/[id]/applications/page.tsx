'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { ArrowLeft, Search, Filter, Download, Mail, Phone, Calendar, Eye, User, MapPin, Clock, Star, MoreVertical } from 'lucide-react';
import Link from 'next/link';

interface Application {
  id: string;
  jobPostingId: string;
  jobSeekerProfile: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    location?: string;
    headline?: string;
    summary?: string;
    preferredSalary?: number;
    salaryType?: string;
    availableFrom?: string;
    workAuthorization?: string;
    resume?: string;
    profilePicture?: string;
  };
  coverLetter?: string;
  resume?: string;
  status: string;
  appliedAt: string;
  lastUpdated: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewerNotes?: string;
  salaryExpectation?: number;
  availableFrom?: string;
}

interface JobPosting {
  id: string;
  title: string;
  company: {
    name: string;
    logo?: string;
  };
  department: string;
  location: string;
  employmentType: string;
  status: string;
  applicationCount: number;
  createdAt: string;
}

const statusOptions = [
  { value: 'applied', label: 'Applied', color: 'bg-blue-100 text-blue-800' },
  { value: 'screening', label: 'Screening', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'interview', label: 'Interview', color: 'bg-purple-100 text-purple-800' },
  { value: 'offer', label: 'Offer', color: 'bg-green-100 text-green-800' },
  { value: 'hired', label: 'Hired', color: 'bg-green-100 text-green-800' },
  { value: 'rejected', label: 'Rejected', color: 'bg-red-100 text-red-800' },
  { value: 'withdrawn', label: 'Withdrawn', color: 'bg-gray-100 text-gray-800' }
];

const mockJob: JobPosting = {
  id: '1',
  title: 'Senior Software Engineer',
  company: { name: 'TechCorp Singapore' },
  department: 'Engineering',
  location: 'Singapore',
  employmentType: 'full-time',
  status: 'active',
  applicationCount: 45,
  createdAt: '2025-01-15T10:00:00Z'
};

const mockApplications: Application[] = [
  {
    id: 'app_1',
    jobPostingId: '1',
    jobSeekerProfile: {
      id: 'profile_1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+65 9123 4567',
      location: 'Singapore',
      headline: 'Senior Full-Stack Developer',
      summary: 'Experienced software engineer with 6+ years in web development...',
      preferredSalary: 10000,
      salaryType: 'monthly',
      availableFrom: '2025-02-01',
      workAuthorization: 'citizen',
      resume: '/resumes/john-doe-resume.pdf'
    },
    coverLetter: 'I am excited to apply for the Senior Software Engineer position...',
    status: 'interview',
    appliedAt: '2025-01-16T09:30:00Z',
    lastUpdated: '2025-01-18T14:20:00Z',
    reviewedAt: '2025-01-17T10:15:00Z',
    reviewedBy: 'HR Manager',
    reviewerNotes: 'Strong technical background, good cultural fit',
    salaryExpectation: 10000
  },
  {
    id: 'app_2',
    jobPostingId: '1',
    jobSeekerProfile: {
      id: 'profile_2',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+65 8765 4321',
      location: 'Malaysia (willing to relocate)',
      headline: 'Frontend Developer',
      summary: 'Passionate frontend developer with expertise in React and TypeScript...',
      preferredSalary: 8500,
      salaryType: 'monthly',
      availableFrom: '2025-03-01',
      workAuthorization: 'visa_required',
      resume: '/resumes/jane-smith-resume.pdf'
    },
    coverLetter: 'I am writing to express my strong interest in the Senior Software Engineer role...',
    status: 'screening',
    appliedAt: '2025-01-17T15:45:00Z',
    lastUpdated: '2025-01-17T15:45:00Z',
    salaryExpectation: 8500
  },
  {
    id: 'app_3',
    jobPostingId: '1',
    jobSeekerProfile: {
      id: 'profile_3',
      firstName: 'Alex',
      lastName: 'Chen',
      email: '<EMAIL>',
      phone: '+65 9876 5432',
      location: 'Singapore',
      headline: 'Full-Stack Engineer',
      summary: 'Versatile software engineer with backend and frontend expertise...',
      preferredSalary: 11000,
      salaryType: 'monthly',
      availableFrom: '2025-02-15',
      workAuthorization: 'permanent_resident',
      resume: '/resumes/alex-chen-resume.pdf'
    },
    status: 'applied',
    appliedAt: '2025-01-18T11:20:00Z',
    lastUpdated: '2025-01-18T11:20:00Z',
    salaryExpectation: 11000
  }
];

export default function JobApplications() {
  const params = useParams();
  const jobId = params.id as string;
  
  const [applications, setApplications] = useState<Application[]>(mockApplications);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);

  const filteredApplications = applications.filter(app => {
    const matchesSearch = 
      app.jobSeekerProfile.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.jobSeekerProfile.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.jobSeekerProfile.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.jobSeekerProfile.headline?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Sort applications
  const sortedApplications = [...filteredApplications].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.appliedAt).getTime() - new Date(a.appliedAt).getTime();
      case 'oldest':
        return new Date(a.appliedAt).getTime() - new Date(b.appliedAt).getTime();
      case 'name':
        return `${a.jobSeekerProfile.firstName} ${a.jobSeekerProfile.lastName}`.localeCompare(
          `${b.jobSeekerProfile.firstName} ${b.jobSeekerProfile.lastName}`
        );
      case 'status':
        return a.status.localeCompare(b.status);
      default:
        return 0;
    }
  });

  const handleStatusChange = async (applicationId: string, newStatus: string) => {
    setApplications(prev => 
      prev.map(app => 
        app.id === applicationId 
          ? { ...app, status: newStatus, lastUpdated: new Date().toISOString() }
          : app
      )
    );
    
    // Here you would make an API call to update the status
    console.log(`Updating application ${applicationId} status to ${newStatus}`);
  };

  const handleBulkAction = (action: string) => {
    console.log(`Performing bulk action: ${action} on applications:`, selectedApplications);
    // Implement bulk actions like reject, move to next stage, etc.
  };

  const getStatusColor = (status: string) => {
    return statusOptions.find(option => option.value === status)?.color || 'bg-gray-100 text-gray-800';
  };

  const formatSalary = (amount?: number, type?: string) => {
    if (!amount) return 'Not specified';
    const currency = 'S$';
    const period = type === 'yearly' ? '/year' : type === 'monthly' ? '/month' : '/hour';
    return `${currency}${amount.toLocaleString()} ${period}`;
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    }
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Link 
            href="/hr/recruitment"
            className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back to Jobs
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{mockJob.title}</h1>
            <p className="text-gray-600">{mockJob.company.name} • {mockJob.department}</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        {statusOptions.map(status => {
          const count = applications.filter(app => app.status === status.value).length;
          return (
            <div key={status.value} className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{count}</p>
                <p className="text-sm text-gray-600 capitalize">{status.label}</p>
              </div>
            </div>
          );
        })}
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search applicants by name, email, or headline..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="name">Name A-Z</option>
              <option value="status">Status</option>
            </select>
          </div>
        </div>

        {selectedApplications.length > 0 && (
          <div className="mt-4 flex items-center gap-2">
            <span className="text-sm text-gray-600">{selectedApplications.length} selected</span>
            <button
              onClick={() => handleBulkAction('reject')}
              className="px-3 py-1 text-sm border border-red-300 rounded text-red-700 hover:bg-red-50"
            >
              Reject
            </button>
            <button
              onClick={() => handleBulkAction('screen')}
              className="px-3 py-1 text-sm border border-blue-300 rounded text-blue-700 hover:bg-blue-50"
            >
              Move to Screening
            </button>
          </div>
        )}
      </div>

      {/* Applications List */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="space-y-0">
          {sortedApplications.map((application) => (
            <div key={application.id} className="border-b border-gray-200 p-6 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4 flex-1">
                  <input
                    type="checkbox"
                    checked={selectedApplications.includes(application.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedApplications(prev => [...prev, application.id]);
                      } else {
                        setSelectedApplications(prev => prev.filter(id => id !== application.id));
                      }
                    }}
                    className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    {application.jobSeekerProfile.profilePicture ? (
                      <img 
                        src={application.jobSeekerProfile.profilePicture} 
                        alt="Profile" 
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-6 h-6 text-gray-500" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {application.jobSeekerProfile.firstName} {application.jobSeekerProfile.lastName}
                      </h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(application.status)}`}>
                        {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{application.jobSeekerProfile.headline}</p>
                    
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-3">
                      <div className="flex items-center">
                        <Mail className="w-4 h-4 mr-1" />
                        {application.jobSeekerProfile.email}
                      </div>
                      {application.jobSeekerProfile.phone && (
                        <div className="flex items-center">
                          <Phone className="w-4 h-4 mr-1" />
                          {application.jobSeekerProfile.phone}
                        </div>
                      )}
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {application.jobSeekerProfile.location}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        Applied {getTimeAgo(application.appliedAt)}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-6 text-sm">
                      <div>
                        <span className="text-gray-500">Salary Expectation:</span>
                        <span className="ml-1 font-medium">
                          {formatSalary(application.salaryExpectation, application.jobSeekerProfile.salaryType)}
                        </span>
                      </div>
                      {application.jobSeekerProfile.availableFrom && (
                        <div>
                          <span className="text-gray-500">Available from:</span>
                          <span className="ml-1 font-medium">
                            {new Date(application.jobSeekerProfile.availableFrom).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                      <div>
                        <span className="text-gray-500">Work Auth:</span>
                        <span className="ml-1 font-medium capitalize">
                          {application.jobSeekerProfile.workAuthorization?.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                    
                    {application.reviewerNotes && (
                      <div className="mt-3 p-2 bg-blue-50 rounded border-l-4 border-blue-200">
                        <p className="text-sm text-blue-800">
                          <strong>Notes:</strong> {application.reviewerNotes}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-col gap-2 ml-4">
                  <select
                    value={application.status}
                    onChange={(e) => handleStatusChange(application.id, e.target.value)}
                    className="px-3 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {statusOptions.map(status => (
                      <option key={status.value} value={status.value}>{status.label}</option>
                    ))}
                  </select>
                  
                  <div className="flex gap-1">
                    <Link
                      href={`/hr/recruitment/applications/${application.id}`}
                      className="p-2 text-gray-400 hover:text-gray-600 border border-gray-300 rounded"
                      title="View Details"
                    >
                      <Eye className="w-4 h-4" />
                    </Link>
                    
                    {application.jobSeekerProfile.resume && (
                      <a
                        href={application.jobSeekerProfile.resume}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-400 hover:text-gray-600 border border-gray-300 rounded"
                        title="View Resume"
                      >
                        <Download className="w-4 h-4" />
                      </a>
                    )}
                    
                    <button className="p-2 text-gray-400 hover:text-gray-600 border border-gray-300 rounded">
                      <MoreVertical className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {sortedApplications.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <p className="text-lg mb-2">No applications found</p>
              <p className="text-sm">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Try adjusting your search criteria or filters.'
                  : 'No applications have been received for this job yet.'
                }
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}