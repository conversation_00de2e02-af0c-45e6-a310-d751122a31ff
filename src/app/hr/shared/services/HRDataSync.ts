'use client';

import { EventEmitter } from 'events';

// Types for cross-module data sharing
export interface HRUser {
  id: string;
  email: string;
  name: string;
  userType: 'jobseeker' | 'recruiter' | 'headhunter' | 'admin';
  profile?: any;
  permissions: string[];
  modules: string[];
}

export interface HRJob {
  id: string;
  title: string;
  company: {
    id: string;
    name: string;
    industry: string;
    size: string;
    location: string;
  };
  description: string;
  requirements: string;
  salary?: {
    min: number;
    max: number;
    currency: string;
    period: string;
  };
  location: string;
  jobType: string;
  jobLevel: string;
  status: 'active' | 'inactive' | 'filled' | 'cancelled';
  postedAt: string;
  updatedAt: string;
  source: 'recruitment' | 'headhunter' | 'external';
  tenantId: string;
}

export interface HRApplication {
  id: string;
  jobId: string;
  candidateId: string;
  status: 'pending' | 'reviewed' | 'interview' | 'offered' | 'hired' | 'rejected';
  appliedAt: string;
  updatedAt: string;
  source: string;
  tenantId: string;
}

export interface HRCandidate {
  id: string;
  userId: string;
  profile: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    location: string;
    experience: number;
    skills: string[];
    education: any[];
    workExperience: any[];
  };
  preferences: {
    jobTypes: string[];
    locations: string[];
    salaryRange?: {
      min: number;
      max: number;
      currency: string;
    };
    industries: string[];
  };
  status: 'active' | 'inactive' | 'hired';
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

export interface HRNotification {
  id: string;
  userId: string;
  type: 'job_match' | 'application_update' | 'interview_scheduled' | 'system' | 'message';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
  tenantId: string;
  module: string;
}

export interface HRMatch {
  id: string;
  jobId: string;
  candidateId: string;
  score: number;
  reasons: string[];
  concerns: string[];
  status: 'pending' | 'viewed' | 'contacted' | 'rejected';
  createdAt: string;
  tenantId: string;
  source: 'headhunter' | 'ai_match';
}

// Event types for cross-module communication
export interface HRDataSyncEvents {
  'user:updated': (user: HRUser) => void;
  'job:created': (job: HRJob) => void;
  'job:updated': (job: HRJob) => void;
  'job:deleted': (jobId: string) => void;
  'application:created': (application: HRApplication) => void;
  'application:updated': (application: HRApplication) => void;
  'candidate:created': (candidate: HRCandidate) => void;
  'candidate:updated': (candidate: HRCandidate) => void;
  'notification:created': (notification: HRNotification) => void;
  'notification:read': (notificationId: string) => void;
  'match:created': (match: HRMatch) => void;
  'match:updated': (match: HRMatch) => void;
  'cache:invalidate': (keys: string[]) => void;
}

// HR Data Synchronization Service
class HRDataSyncService extends EventEmitter {
  private static instance: HRDataSyncService;
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  private constructor() {
    super();
    this.setupCacheCleanup();
  }

  static getInstance(): HRDataSyncService {
    if (!HRDataSyncService.instance) {
      HRDataSyncService.instance = new HRDataSyncService();
    }
    return HRDataSyncService.instance;
  }

  // Cache management
  private setupCacheCleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, expiry] of this.cacheExpiry.entries()) {
        if (now > expiry) {
          this.cache.delete(key);
          this.cacheExpiry.delete(key);
        }
      }
    }, 60 * 1000); // Clean up every minute
  }

  private getCacheKey(type: string, id?: string, filters?: any): string {
    const filterStr = filters ? JSON.stringify(filters) : '';
    return `${type}:${id || 'all'}:${filterStr}`;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL);
  }

  private getCache(key: string): any | null {
    const expiry = this.cacheExpiry.get(key);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
      return null;
    }
    return this.cache.get(key) || null;
  }

  invalidateCache(keys: string[]): void {
    keys.forEach(key => {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
    });
    this.emit('cache:invalidate', keys);
  }

  // Generic API helper
  private async apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const response = await fetch(endpoint, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  }

  // User management
  async getUser(userId: string): Promise<HRUser | null> {
    const cacheKey = this.getCacheKey('user', userId);
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const user = await this.apiRequest<HRUser>(`/api/hr/users/${userId}`);
      this.setCache(cacheKey, user);
      return user;
    } catch (error) {
      console.error('Error fetching user:', error);
      return null;
    }
  }

  async updateUser(user: HRUser): Promise<void> {
    try {
      await this.apiRequest(`/api/hr/users/${user.id}`, {
        method: 'PUT',
        body: JSON.stringify(user),
      });

      const cacheKey = this.getCacheKey('user', user.id);
      this.setCache(cacheKey, user);
      this.emit('user:updated', user);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // Job management
  async getJobs(filters?: any): Promise<HRJob[]> {
    const cacheKey = this.getCacheKey('jobs', undefined, filters);
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const queryParams = filters ? new URLSearchParams(filters).toString() : '';
      const jobs = await this.apiRequest<HRJob[]>(`/api/hr/jobs${queryParams ? `?${queryParams}` : ''}`);
      this.setCache(cacheKey, jobs);
      return jobs;
    } catch (error) {
      console.error('Error fetching jobs:', error);
      return [];
    }
  }

  async getJob(jobId: string): Promise<HRJob | null> {
    const cacheKey = this.getCacheKey('job', jobId);
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const job = await this.apiRequest<HRJob>(`/api/hr/jobs/${jobId}`);
      this.setCache(cacheKey, job);
      return job;
    } catch (error) {
      console.error('Error fetching job:', error);
      return null;
    }
  }

  async createJob(job: Omit<HRJob, 'id' | 'postedAt' | 'updatedAt'>): Promise<HRJob> {
    try {
      const newJob = await this.apiRequest<HRJob>('/api/hr/jobs', {
        method: 'POST',
        body: JSON.stringify(job),
      });

      // Invalidate jobs cache
      this.invalidateCache(['jobs:all']);
      this.emit('job:created', newJob);
      return newJob;
    } catch (error) {
      console.error('Error creating job:', error);
      throw error;
    }
  }

  async updateJob(job: HRJob): Promise<void> {
    try {
      await this.apiRequest(`/api/hr/jobs/${job.id}`, {
        method: 'PUT',
        body: JSON.stringify(job),
      });

      const cacheKey = this.getCacheKey('job', job.id);
      this.setCache(cacheKey, job);
      this.invalidateCache(['jobs:all']);
      this.emit('job:updated', job);
    } catch (error) {
      console.error('Error updating job:', error);
      throw error;
    }
  }

  async deleteJob(jobId: string): Promise<void> {
    try {
      await this.apiRequest(`/api/hr/jobs/${jobId}`, {
        method: 'DELETE',
      });

      this.invalidateCache([`job:${jobId}`, 'jobs:all']);
      this.emit('job:deleted', jobId);
    } catch (error) {
      console.error('Error deleting job:', error);
      throw error;
    }
  }

  // Application management
  async getApplications(filters?: any): Promise<HRApplication[]> {
    const cacheKey = this.getCacheKey('applications', undefined, filters);
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const queryParams = filters ? new URLSearchParams(filters).toString() : '';
      const applications = await this.apiRequest<HRApplication[]>(`/api/hr/applications${queryParams ? `?${queryParams}` : ''}`);
      this.setCache(cacheKey, applications);
      return applications;
    } catch (error) {
      console.error('Error fetching applications:', error);
      return [];
    }
  }

  async createApplication(application: Omit<HRApplication, 'id' | 'appliedAt' | 'updatedAt'>): Promise<HRApplication> {
    try {
      const newApplication = await this.apiRequest<HRApplication>('/api/hr/applications', {
        method: 'POST',
        body: JSON.stringify(application),
      });

      this.invalidateCache(['applications:all']);
      this.emit('application:created', newApplication);
      return newApplication;
    } catch (error) {
      console.error('Error creating application:', error);
      throw error;
    }
  }

  async updateApplication(application: HRApplication): Promise<void> {
    try {
      await this.apiRequest(`/api/hr/applications/${application.id}`, {
        method: 'PUT',
        body: JSON.stringify(application),
      });

      this.invalidateCache(['applications:all']);
      this.emit('application:updated', application);
    } catch (error) {
      console.error('Error updating application:', error);
      throw error;
    }
  }

  // Candidate management
  async getCandidates(filters?: any): Promise<HRCandidate[]> {
    const cacheKey = this.getCacheKey('candidates', undefined, filters);
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const queryParams = filters ? new URLSearchParams(filters).toString() : '';
      const candidates = await this.apiRequest<HRCandidate[]>(`/api/hr/candidates${queryParams ? `?${queryParams}` : ''}`);
      this.setCache(cacheKey, candidates);
      return candidates;
    } catch (error) {
      console.error('Error fetching candidates:', error);
      return [];
    }
  }

  async createCandidate(candidate: Omit<HRCandidate, 'id' | 'createdAt' | 'updatedAt'>): Promise<HRCandidate> {
    try {
      const newCandidate = await this.apiRequest<HRCandidate>('/api/hr/candidates', {
        method: 'POST',
        body: JSON.stringify(candidate),
      });

      this.invalidateCache(['candidates:all']);
      this.emit('candidate:created', newCandidate);
      return newCandidate;
    } catch (error) {
      console.error('Error creating candidate:', error);
      throw error;
    }
  }

  async updateCandidate(candidate: HRCandidate): Promise<void> {
    try {
      await this.apiRequest(`/api/hr/candidates/${candidate.id}`, {
        method: 'PUT',
        body: JSON.stringify(candidate),
      });

      const cacheKey = this.getCacheKey('candidate', candidate.id);
      this.setCache(cacheKey, candidate);
      this.invalidateCache(['candidates:all']);
      this.emit('candidate:updated', candidate);
    } catch (error) {
      console.error('Error updating candidate:', error);
      throw error;
    }
  }

  // Notification management
  async getNotifications(userId: string): Promise<HRNotification[]> {
    const cacheKey = this.getCacheKey('notifications', userId);
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const notifications = await this.apiRequest<HRNotification[]>(`/api/hr/notifications?userId=${userId}`);
      this.setCache(cacheKey, notifications);
      return notifications;
    } catch (error: unknown) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  async createNotification(notification: Omit<HRNotification, 'id' | 'createdAt'>): Promise<HRNotification> {
    try {
      const newNotification = await this.apiRequest<HRNotification>('/api/hr/notifications', {
        method: 'POST',
        body: JSON.stringify(notification),
      });

      this.invalidateCache([`notifications:${notification.userId}`]);
      this.emit('notification:created', newNotification);
      return newNotification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  async markNotificationRead(notificationId: string): Promise<void> {
    try {
      await this.apiRequest(`/api/hr/notifications/${notificationId}/read`, {
        method: 'POST',
      });

      this.emit('notification:read', notificationId);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Match management
  async createMatch(match: Omit<HRMatch, 'id' | 'createdAt'>): Promise<HRMatch> {
    try {
      const newMatch = await this.apiRequest<HRMatch>('/api/hr/matches', {
        method: 'POST',
        body: JSON.stringify(match),
      });

      this.emit('match:created', newMatch);
      return newMatch;
    } catch (error) {
      console.error('Error creating match:', error);
      throw error;
    }
  }

  async updateMatch(match: HRMatch): Promise<void> {
    try {
      await this.apiRequest(`/api/hr/matches/${match.id}`, {
        method: 'PUT',
        body: JSON.stringify(match),
      });

      this.emit('match:updated', match);
    } catch (error) {
      console.error('Error updating match:', error);
      throw error;
    }
  }

  // Cross-module workflow helpers
  async triggerJobMatch(jobId: string): Promise<void> {
    try {
      await this.apiRequest('/api/hr/workflows/job-match', {
        method: 'POST',
        body: JSON.stringify({ jobId }),
      });
    } catch (error) {
      console.error('Error triggering job match:', error);
      throw error;
    }
  }

  async notifyHeadhunters(jobId: string, criteria?: any): Promise<void> {
    try {
      await this.apiRequest('/api/hr/workflows/notify-headhunters', {
        method: 'POST',
        body: JSON.stringify({ jobId, criteria }),
      });
    } catch (error) {
      console.error('Error notifying headhunters:', error);
      throw error;
    }
  }

  async syncCandidateToWorklink(candidateId: string): Promise<void> {
    try {
      await this.apiRequest('/api/hr/workflows/sync-to-worklink', {
        method: 'POST',
        body: JSON.stringify({ candidateId }),
      });
    } catch (error) {
      console.error('Error syncing candidate to WorkLink:', error);
      throw error;
    }
  }

  // Event subscription helpers
  onJobCreated(callback: (job: HRJob) => void): void {
    this.on('job:created', callback);
  }

  onJobUpdated(callback: (job: HRJob) => void): void {
    this.on('job:updated', callback);
  }

  onApplicationCreated(callback: (application: HRApplication) => void): void {
    this.on('application:created', callback);
  }

  onNotificationCreated(callback: (notification: HRNotification) => void): void {
    this.on('notification:created', callback);
  }

  onMatchCreated(callback: (match: HRMatch) => void): void {
    this.on('match:created', callback);
  }

  // Cleanup
  destroy(): void {
    this.removeAllListeners();
    this.cache.clear();
    this.cacheExpiry.clear();
  }
}

// Export singleton instance
export const hrDataSync = HRDataSyncService.getInstance();

// Export types
export type {
  HRDataSyncEvents,
};