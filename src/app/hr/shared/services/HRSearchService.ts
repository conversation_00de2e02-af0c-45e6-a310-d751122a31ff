'use client';

import { hrDataSync } from './HRDataSync';

export interface SearchResult {
  id: string;
  type: 'job' | 'candidate' | 'company' | 'application' | 'user' | 'headhunter';
  title: string;
  subtitle?: string;
  description: string;
  url: string;
  module: string;
  relevance: number;
  metadata?: Record<string, any>;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface SearchFilters {
  modules?: string[];
  types?: SearchResult['type'][];
  dateRange?: {
    start: string;
    end: string;
  };
  location?: string;
  salaryRange?: {
    min: number;
    max: number;
    currency: string;
  };
  tags?: string[];
  status?: string[];
}

export interface SearchOptions {
  limit?: number;
  offset?: number;
  sortBy?: 'relevance' | 'date' | 'title';
  sortOrder?: 'asc' | 'desc';
  includeExpired?: boolean;
}

class HRSearchService {
  private static instance: HRSearchService;
  private searchIndex: Map<string, SearchResult[]> = new Map();
  private lastIndexUpdate: number = 0;
  private readonly INDEX_TTL = 5 * 60 * 1000; // 5 minutes

  private constructor() {
    this.setupIndexing();
  }

  static getInstance(): HRSearchService {
    if (!HRSearchService.instance) {
      HRSearchService.instance = new HRSearchService();
    }
    return HRSearchService.instance;
  }

  private setupIndexing(): void {
    // Listen for data changes to update search index
    hrDataSync.on('job:created', () => this.invalidateIndex());
    hrDataSync.on('job:updated', () => this.invalidateIndex());
    hrDataSync.on('candidate:created', () => this.invalidateIndex());
    hrDataSync.on('candidate:updated', () => this.invalidateIndex());
    hrDataSync.on('application:created', () => this.invalidateIndex());
    hrDataSync.on('application:updated', () => this.invalidateIndex());
  }

  private invalidateIndex(): void {
    this.searchIndex.clear();
    this.lastIndexUpdate = 0;
  }

  private async ensureIndex(): Promise<void> {
    const now = Date.now();
    if (now - this.lastIndexUpdate < this.INDEX_TTL && this.searchIndex.size > 0) {
      return; // Index is still fresh
    }

    try {
      await this.buildSearchIndex();
      this.lastIndexUpdate = now;
    } catch (error) {
      console.error('Error building search index:', error);
    }
  }

  private async buildSearchIndex(): Promise<void> {
    const promises = [
      this.indexJobs(),
      this.indexCandidates(),
      this.indexApplications(),
      this.indexCompanies(),
      this.indexUsers()
    ];

    await Promise.all(promises);
  }

  private async indexJobs(): Promise<void> {
    try {
      const jobs = await hrDataSync.getJobs();
      const jobResults: SearchResult[] = jobs.map(job => ({
        id: job.id,
        type: 'job' as const,
        title: job.title,
        subtitle: job.company.name,
        description: job.description,
        url: `/hr/recruitment/jobs/${job.id}`,
        module: job.source || 'recruitment',
        relevance: this.calculateJobRelevance(job),
        metadata: {
          company: job.company,
          salary: job.salary,
          location: job.location,
          jobType: job.jobType,
          jobLevel: job.jobLevel,
          status: job.status
        },
        tags: [
          job.jobType,
          job.jobLevel,
          job.company.industry,
          job.location,
          ...(job.salary ? [`${job.salary.currency} ${job.salary.min}-${job.salary.max}`] : [])
        ],
        createdAt: job.postedAt,
        updatedAt: job.updatedAt
      }));

      this.addToIndex('jobs', jobResults);
    } catch (error) {
      console.error('Error indexing jobs:', error);
    }
  }

  private async indexCandidates(): Promise<void> {
    try {
      const candidates = await hrDataSync.getCandidates();
      const candidateResults: SearchResult[] = candidates.map(candidate => ({
        id: candidate.id,
        type: 'candidate' as const,
        title: `${candidate.profile.firstName} ${candidate.profile.lastName}`,
        subtitle: candidate.profile.location,
        description: `${candidate.profile.experience} years experience in ${candidate.profile.skills.slice(0, 3).join(', ')}`,
        url: `/hr/headhunter/candidates/${candidate.id}`,
        module: 'headhunter',
        relevance: this.calculateCandidateRelevance(candidate),
        metadata: {
          profile: candidate.profile,
          preferences: candidate.preferences,
          status: candidate.status,
          experience: candidate.profile.experience
        },
        tags: [
          ...candidate.profile.skills,
          candidate.profile.location,
          ...candidate.preferences.industries,
          `${candidate.profile.experience} years`,
          candidate.status
        ],
        createdAt: candidate.createdAt,
        updatedAt: candidate.updatedAt
      }));

      this.addToIndex('candidates', candidateResults);
    } catch (error) {
      console.error('Error indexing candidates:', error);
    }
  }

  private async indexApplications(): Promise<void> {
    try {
      const applications = await hrDataSync.getApplications();
      const applicationResults: SearchResult[] = await Promise.all(
        applications.map(async (application) => {
          const job = await hrDataSync.getJob(application.jobId);
          const candidate = await hrDataSync.getUser(application.candidateId);
          
          return {
            id: application.id,
            type: 'application' as const,
            title: `Application: ${job?.title || 'Unknown Job'}`,
            subtitle: candidate ? `${candidate.name}` : 'Unknown Candidate',
            description: `Application for ${job?.title} at ${job?.company.name}`,
            url: `/hr/recruitment/applications/${application.id}`,
            module: 'recruitment',
            relevance: this.calculateApplicationRelevance(application),
            metadata: {
              job,
              candidate,
              status: application.status,
              appliedAt: application.appliedAt
            },
            tags: [
              application.status,
              job?.company.name || '',
              job?.jobType || '',
              application.source
            ].filter(Boolean),
            createdAt: application.appliedAt,
            updatedAt: application.updatedAt
          };
        })
      );

      this.addToIndex('applications', applicationResults);
    } catch (error) {
      console.error('Error indexing applications:', error);
    }
  }

  private async indexCompanies(): Promise<void> {
    try {
      // Get unique companies from jobs
      const jobs = await hrDataSync.getJobs();
      const companiesMap = new Map();
      
      jobs.forEach(job => {
        if (!companiesMap.has(job.company.name)) {
          companiesMap.set(job.company.name, {
            ...job.company,
            jobCount: 1,
            lastJobPosted: job.postedAt
          });
        } else {
          const existing = companiesMap.get(job.company.name);
          existing.jobCount++;
          if (job.postedAt > existing.lastJobPosted) {
            existing.lastJobPosted = job.postedAt;
          }
        }
      });

      const companyResults: SearchResult[] = Array.from(companiesMap.values()).map(company => ({
        id: company.id || company.name,
        type: 'company' as const,
        title: company.name,
        subtitle: company.industry,
        description: `${company.size} company in ${company.industry}. ${company.jobCount} active job postings.`,
        url: `/hr/hunter/companies/${company.id || encodeURIComponent(company.name)}`,
        module: 'hunter',
        relevance: this.calculateCompanyRelevance(company),
        metadata: {
          industry: company.industry,
          size: company.size,
          location: company.location,
          jobCount: company.jobCount
        },
        tags: [
          company.industry,
          company.size,
          company.location,
          `${company.jobCount} jobs`
        ],
        createdAt: company.lastJobPosted,
        updatedAt: company.lastJobPosted
      }));

      this.addToIndex('companies', companyResults);
    } catch (error) {
      console.error('Error indexing companies:', error);
    }
  }

  private async indexUsers(): Promise<void> {
    try {
      // This would index public user profiles for WorkLink
      // Implementation depends on user privacy settings
      const users: SearchResult[] = [];
      this.addToIndex('users', users);
    } catch (error) {
      console.error('Error indexing users:', error);
    }
  }

  private addToIndex(category: string, results: SearchResult[]): void {
    this.searchIndex.set(category, results);
  }

  async search(
    query: string,
    filters: SearchFilters = {},
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    await this.ensureIndex();

    const {
      limit = 50,
      offset = 0,
      sortBy = 'relevance',
      sortOrder = 'desc',
      includeExpired = false
    } = options;

    // Get all results from index
    let allResults: SearchResult[] = [];
    for (const results of this.searchIndex.values()) {
      allResults = allResults.concat(results);
    }

    // Apply filters
    let filteredResults = allResults.filter(result => {
      // Module filter
      if (filters.modules && !filters.modules.includes(result.module)) {
        return false;
      }

      // Type filter
      if (filters.types && !filters.types.includes(result.type)) {
        return false;
      }

      // Date range filter
      if (filters.dateRange) {
        const resultDate = new Date(result.createdAt);
        const startDate = new Date(filters.dateRange.start);
        const endDate = new Date(filters.dateRange.end);
        if (resultDate < startDate || resultDate > endDate) {
          return false;
        }
      }

      // Location filter
      if (filters.location) {
        const location = filters.location.toLowerCase();
        if (!result.metadata?.location?.toLowerCase().includes(location) &&
            !result.tags?.some(tag => tag.toLowerCase().includes(location))) {
          return false;
        }
      }

      // Salary range filter
      if (filters.salaryRange && result.metadata?.salary) {
        const salary = result.metadata.salary;
        if (salary.currency === filters.salaryRange.currency) {
          if (salary.max < filters.salaryRange.min || salary.min > filters.salaryRange.max) {
            return false;
          }
        }
      }

      // Tags filter
      if (filters.tags && filters.tags.length > 0) {
        const resultTags = result.tags || [];
        if (!filters.tags.some(tag => resultTags.includes(tag))) {
          return false;
        }
      }

      // Status filter
      if (filters.status && filters.status.length > 0) {
        const status = result.metadata?.status;
        if (!status || !filters.status.includes(status)) {
          return false;
        }
      }

      return true;
    });

    // Apply text search
    if (query.trim()) {
      const searchTerms = query.toLowerCase().split(/\s+/);
      filteredResults = filteredResults.filter(result => {
        const searchText = [
          result.title,
          result.subtitle,
          result.description,
          ...(result.tags || [])
        ].join(' ').toLowerCase();

        return searchTerms.every(term => searchText.includes(term));
      });

      // Update relevance based on query match
      filteredResults.forEach(result => {
        result.relevance = this.calculateQueryRelevance(result, query);
      });
    }

    // Sort results
    filteredResults.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'relevance':
          comparison = b.relevance - a.relevance;
          break;
        case 'date':
          comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
      }
      return sortOrder === 'desc' ? comparison : -comparison;
    });

    // Apply pagination
    return filteredResults.slice(offset, offset + limit);
  }

  async getSearchSuggestions(query: string, limit: number = 10): Promise<string[]> {
    await this.ensureIndex();

    const suggestions = new Set<string>();
    const queryLower = query.toLowerCase();

    // Collect suggestions from titles, tags, and common terms
    for (const results of this.searchIndex.values()) {
      for (const result of results) {
        // Title suggestions
        if (result.title.toLowerCase().includes(queryLower)) {
          suggestions.add(result.title);
        }

        // Tag suggestions
        if (result.tags) {
          for (const tag of result.tags) {
            if (tag.toLowerCase().includes(queryLower)) {
              suggestions.add(tag);
            }
          }
        }

        if (suggestions.size >= limit) break;
      }
      if (suggestions.size >= limit) break;
    }

    return Array.from(suggestions).slice(0, limit);
  }

  async getPopularSearches(limit: number = 10): Promise<string[]> {
    // This would typically be based on search analytics
    return [
      'Software Engineer',
      'Marketing Manager',
      'Data Scientist',
      'Product Manager',
      'UX Designer',
      'Sales Representative',
      'HR Manager',
      'Business Analyst',
      'Project Manager',
      'DevOps Engineer'
    ].slice(0, limit);
  }

  async getSearchStats(): Promise<{
    totalJobs: number;
    totalCandidates: number;
    totalApplications: number;
    totalCompanies: number;
    lastIndexed: string;
  }> {
    await this.ensureIndex();

    return {
      totalJobs: this.searchIndex.get('jobs')?.length || 0,
      totalCandidates: this.searchIndex.get('candidates')?.length || 0,
      totalApplications: this.searchIndex.get('applications')?.length || 0,
      totalCompanies: this.searchIndex.get('companies')?.length || 0,
      lastIndexed: new Date(this.lastIndexUpdate).toISOString()
    };
  }

  private calculateJobRelevance(job: any): number {
    let score = 50; // Base score

    // Recent jobs score higher
    const daysOld = (Date.now() - new Date(job.postedAt).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 20 - daysOld); // Up to 20 points for recent jobs

    // Active jobs score higher
    if (job.status === 'active') score += 15;

    // Jobs with salary info score higher
    if (job.salary) score += 10;

    // Remote jobs score higher (popular filter)
    if (job.remoteWork) score += 5;

    return Math.min(100, score);
  }

  private calculateCandidateRelevance(candidate: any): number {
    let score = 50; // Base score

    // Active candidates score higher
    if (candidate.status === 'active') score += 20;

    // Recent profile updates score higher
    const daysOld = (Date.now() - new Date(candidate.updatedAt).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 15 - daysOld); // Up to 15 points for recent updates

    // Experience level affects score
    score += Math.min(15, candidate.profile.experience * 2);

    return Math.min(100, score);
  }

  private calculateApplicationRelevance(application: any): number {
    let score = 40; // Base score

    // Recent applications score higher
    const daysOld = (Date.now() - new Date(application.appliedAt).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 25 - daysOld); // Up to 25 points for recent applications

    // Status affects score
    switch (application.status) {
      case 'pending': score += 20; break;
      case 'interview': score += 25; break;
      case 'offered': score += 30; break;
      case 'hired': score += 15; break;
      default: score += 5;
    }

    return Math.min(100, score);
  }

  private calculateCompanyRelevance(company: any): number {
    let score = 45; // Base score

    // Companies with more jobs score higher
    score += Math.min(25, company.jobCount * 5);

    // Recent job postings score higher
    const daysOld = (Date.now() - new Date(company.lastJobPosted).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 20 - daysOld);

    // Larger companies score slightly higher
    if (company.size === 'Large') score += 10;
    else if (company.size === 'Medium') score += 5;

    return Math.min(100, score);
  }

  private calculateQueryRelevance(result: SearchResult, query: string): number {
    const queryLower = query.toLowerCase();
    let score = result.relevance;

    // Exact title match
    if (result.title.toLowerCase() === queryLower) {
      score += 30;
    } else if (result.title.toLowerCase().includes(queryLower)) {
      score += 20;
    }

    // Subtitle match
    if (result.subtitle?.toLowerCase().includes(queryLower)) {
      score += 15;
    }

    // Description match
    if (result.description.toLowerCase().includes(queryLower)) {
      score += 10;
    }

    // Tag matches
    const tagMatches = result.tags?.filter(tag => 
      tag.toLowerCase().includes(queryLower)
    ).length || 0;
    score += tagMatches * 5;

    return Math.min(100, score);
  }

  destroy(): void {
    this.searchIndex.clear();
    this.lastIndexUpdate = 0;
  }
}

// Export singleton instance
export const hrSearchService = HRSearchService.getInstance();