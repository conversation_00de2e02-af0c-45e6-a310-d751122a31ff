'use client';

import { EventEmitter } from 'events';
import { hrDataSync, HRNotification } from './HRDataSync';

export interface NotificationTemplate {
  type: HRNotification['type'];
  title: string;
  messageTemplate: string;
  module: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  expiresIn?: number; // hours
}

export interface NotificationPreferences {
  userId: string;
  email: boolean;
  push: boolean;
  inApp: boolean;
  types: {
    job_match: boolean;
    application_update: boolean;
    interview_scheduled: boolean;
    system: boolean;
    message: boolean;
  };
}

class HRNotificationService extends EventEmitter {
  private static instance: HRNotificationService;
  private templates: Map<string, NotificationTemplate> = new Map();
  private userPreferences: Map<string, NotificationPreferences> = new Map();

  private constructor() {
    super();
    this.initializeTemplates();
    this.setupEventListeners();
  }

  static getInstance(): HRNotificationService {
    if (!HRNotificationService.instance) {
      HRNotificationService.instance = new HRNotificationService();
    }
    return HRNotificationService.instance;
  }

  private initializeTemplates(): void {
    const templates: NotificationTemplate[] = [
      // Job Matching Templates
      {
        type: 'job_match',
        title: 'New Job Match Found',
        messageTemplate: 'We found a great job match for you: {{jobTitle}} at {{companyName}}',
        module: 'hunter',
        priority: 'medium',
        expiresIn: 168 // 7 days
      },
      {
        type: 'job_match',
        title: 'High-Priority Job Match',
        messageTemplate: 'Urgent: Perfect match found! {{jobTitle}} at {{companyName}} - {{matchScore}}% match',
        module: 'headhunter',
        priority: 'high',
        expiresIn: 72 // 3 days
      },

      // Application Templates
      {
        type: 'application_update',
        title: 'Application Status Update',
        messageTemplate: 'Your application for {{jobTitle}} at {{companyName}} has been {{status}}',
        module: 'hunter',
        priority: 'medium',
        expiresIn: 168 // 7 days
      },
      {
        type: 'application_update',
        title: 'New Application Received',
        messageTemplate: 'New application received for {{jobTitle}} from {{candidateName}}',
        module: 'recruitment',
        priority: 'medium',
        expiresIn: 72 // 3 days
      },

      // Interview Templates
      {
        type: 'interview_scheduled',
        title: 'Interview Scheduled',
        messageTemplate: 'Interview scheduled for {{jobTitle}} at {{companyName}} on {{dateTime}}',
        module: 'hunter',
        priority: 'high',
        expiresIn: 24 // 1 day
      },
      {
        type: 'interview_scheduled',
        title: 'Candidate Interview Scheduled',
        messageTemplate: 'Interview scheduled with {{candidateName}} for {{jobTitle}} on {{dateTime}}',
        module: 'recruitment',
        priority: 'high',
        expiresIn: 24 // 1 day
      },

      // System Templates
      {
        type: 'system',
        title: 'Profile Updated',
        messageTemplate: 'Your HR profile has been successfully updated',
        module: 'system',
        priority: 'low',
        expiresIn: 24 // 1 day
      },
      {
        type: 'system',
        title: 'Security Alert',
        messageTemplate: 'Security alert: {{alertMessage}}',
        module: 'system',
        priority: 'urgent',
        expiresIn: 72 // 3 days
      },

      // WorkLink Templates
      {
        type: 'message',
        title: 'New Professional Match',
        messageTemplate: 'You have a new professional match on WorkLink: {{matchName}}',
        module: 'worklink',
        priority: 'medium',
        expiresIn: 168 // 7 days
      },
      {
        type: 'message',
        title: 'New Connection Request',
        messageTemplate: '{{senderName}} wants to connect with you on WorkLink',
        module: 'worklink',
        priority: 'medium',
        expiresIn: 72 // 3 days
      },

      // Headhunter Templates
      {
        type: 'job_match',
        title: 'New Job Opportunity',
        messageTemplate: 'New job opportunity matching your criteria: {{jobTitle}} - {{placementFee}}',
        module: 'headhunter',
        priority: 'medium',
        expiresIn: 72 // 3 days
      }
    ];

    templates.forEach(template => {
      const key = `${template.type}:${template.module}`;
      this.templates.set(key, template);
    });
  }

  private setupEventListeners(): void {
    // Listen for data sync events and create notifications
    hrDataSync.onJobCreated((job) => {
      this.createJobNotifications(job);
    });

    hrDataSync.onApplicationCreated((application) => {
      this.createApplicationNotifications(application);
    });

    hrDataSync.onMatchCreated((match) => {
      this.createMatchNotifications(match);
    });
  }

  private async createJobNotifications(job: any): Promise<void> {
    // Notify headhunters about new jobs that match their criteria
    try {
      const headhunters = await this.getMatchingHeadhunters(job);
      
      for (const headhunter of headhunters) {
        await this.createNotification({
          userId: headhunter.id,
          type: 'job_match',
          module: 'headhunter',
          data: {
            jobId: job.id,
            jobTitle: job.title,
            companyName: job.company.name,
            placementFee: job.placementFee || 'TBD'
          }
        });
      }

      // Notify job seekers about relevant opportunities
      const jobSeekers = await this.getMatchingJobSeekers(job);
      
      for (const jobSeeker of jobSeekers) {
        await this.createNotification({
          userId: jobSeeker.id,
          type: 'job_match',
          module: 'hunter',
          data: {
            jobId: job.id,
            jobTitle: job.title,
            companyName: job.company.name,
            matchScore: jobSeeker.matchScore || 85
          }
        });
      }
    } catch (error) {
      console.error('Error creating job notifications:', error);
    }
  }

  private async createApplicationNotifications(application: any): Promise<void> {
    try {
      // Notify recruiter about new application
      const job = await hrDataSync.getJob(application.jobId);
      const candidate = await hrDataSync.getUser(application.candidateId);
      
      if (job && candidate) {
        // Notify company/recruiter
        await this.createNotification({
          userId: job.recruiterId || job.company.recruiterId,
          type: 'application_update',
          module: 'recruitment',
          data: {
            applicationId: application.id,
            jobTitle: job.title,
            candidateName: `${candidate.profile?.firstName} ${candidate.profile?.lastName}`,
            status: 'received'
          }
        });

        // Notify candidate about successful submission
        await this.createNotification({
          userId: application.candidateId,
          type: 'application_update',
          module: 'hunter',
          data: {
            applicationId: application.id,
            jobTitle: job.title,
            companyName: job.company.name,
            status: 'submitted'
          }
        });
      }
    } catch (error) {
      console.error('Error creating application notifications:', error);
    }
  }

  private async createMatchNotifications(match: any): Promise<void> {
    try {
      const job = await hrDataSync.getJob(match.jobId);
      const candidate = await hrDataSync.getUser(match.candidateId);
      
      if (job && candidate && match.score >= 80) {
        // High-score match notification
        await this.createNotification({
          userId: match.candidateId,
          type: 'job_match',
          module: 'hunter',
          data: {
            jobId: job.id,
            jobTitle: job.title,
            companyName: job.company.name,
            matchScore: match.score
          }
        });
      }
    } catch (error) {
      console.error('Error creating match notifications:', error);
    }
  }

  async createNotification(params: {
    userId: string;
    type: HRNotification['type'];
    module: string;
    data?: any;
    customTitle?: string;
    customMessage?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    expiresIn?: number; // hours
  }): Promise<HRNotification | null> {
    try {
      // Check user preferences
      const preferences = await this.getUserPreferences(params.userId);
      if (!preferences.inApp || !preferences.types[params.type]) {
        return null; // User doesn't want this type of notification
      }

      // Get template
      const templateKey = `${params.type}:${params.module}`;
      const template = this.templates.get(templateKey) || this.templates.get(`${params.type}:system`);
      
      if (!template) {
        console.warn(`No template found for ${templateKey}`);
        return null;
      }

      // Build notification content
      const title = params.customTitle || template.title;
      const message = params.customMessage || this.interpolateTemplate(template.messageTemplate, params.data || {});
      
      // Calculate expiry
      const expiresIn = params.expiresIn || template.expiresIn;
      const expiresAt = expiresIn 
        ? new Date(Date.now() + expiresIn * 60 * 60 * 1000).toISOString()
        : undefined;

      // Create notification
      const notification = await hrDataSync.createNotification({
        userId: params.userId,
        type: params.type,
        title,
        message,
        data: params.data,
        read: false,
        tenantId: params.data?.tenantId || 'default',
        module: params.module,
        expiresAt
      });

      // Send additional notifications if enabled
      if (preferences.email) {
        await this.sendEmailNotification(params.userId, notification);
      }

      if (preferences.push) {
        await this.sendPushNotification(params.userId, notification);
      }

      // Emit event for real-time updates
      this.emit('notification:created', notification);

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      return null;
    }
  }

  private interpolateTemplate(template: string, data: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || match;
    });
  }

  async getUserPreferences(userId: string): Promise<NotificationPreferences> {
    // Check cache first
    if (this.userPreferences.has(userId)) {
      return this.userPreferences.get(userId)!;
    }

    try {
      // Try to fetch from API
      const response = await fetch(`/api/hr/notifications/preferences?userId=${userId}`);
      if (response.ok) {
        const preferences = await response.json();
        this.userPreferences.set(userId, preferences);
        return preferences;
      }
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
    }

    // Return default preferences
    const defaultPreferences: NotificationPreferences = {
      userId,
      email: true,
      push: true,
      inApp: true,
      types: {
        job_match: true,
        application_update: true,
        interview_scheduled: true,
        system: true,
        message: true
      }
    };

    this.userPreferences.set(userId, defaultPreferences);
    return defaultPreferences;
  }

  async updateUserPreferences(userId: string, preferences: Partial<NotificationPreferences>): Promise<void> {
    try {
      await fetch('/api/hr/notifications/preferences', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, ...preferences })
      });

      // Update cache
      const current = await this.getUserPreferences(userId);
      this.userPreferences.set(userId, { ...current, ...preferences });
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }

  async markAsRead(notificationId: string): Promise<void> {
    try {
      await hrDataSync.markNotificationRead(notificationId);

      // Emit event for real-time updates
      this.emit('notification:read', notificationId);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async getNotifications(userId: string): Promise<HRNotification[]> {
    try {
      return await hrDataSync.getNotifications(userId);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  async getUnreadCount(userId: string): Promise<number> {
    try {
      const notifications = await this.getNotifications(userId);
      return notifications.filter(n => !n.read).length;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  private async getMatchingHeadhunters(job: any): Promise<any[]> {
    // This would typically query the headhunter database for matching criteria
    // For now, return empty array - implement based on headhunter matching logic
    return [];
  }

  private async getMatchingJobSeekers(job: any): Promise<any[]> {
    // This would typically query job seekers based on their preferences
    // For now, return empty array - implement based on job matching logic
    return [];
  }

  private async sendEmailNotification(userId: string, notification: HRNotification): Promise<void> {
    try {
      await fetch('/api/hr/notifications/email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, notification })
      });
    } catch (error) {
      console.error('Error sending email notification:', error);
    }
  }

  private async sendPushNotification(userId: string, notification: HRNotification): Promise<void> {
    try {
      await fetch('/api/hr/notifications/push', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, notification })
      });
    } catch (error) {
      console.error('Error sending push notification:', error);
    }
  }

  // System-wide announcements
  async createSystemAnnouncement(params: {
    title: string;
    message: string;
    targetUsers?: string[];
    targetModules?: string[];
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    expiresIn?: number; // hours
  }): Promise<void> {
    try {
      let userIds = params.targetUsers;
      
      if (!userIds && params.targetModules) {
        // Get users from specific modules
        userIds = await this.getUsersByModules(params.targetModules);
      }
      
      if (!userIds) {
        // Get all active users
        userIds = await this.getAllActiveUsers();
      }

      // Create notifications for all target users
      const promises = userIds.map(userId => 
        this.createNotification({
          userId,
          type: 'system',
          module: 'system',
          customTitle: params.title,
          customMessage: params.message,
          priority: params.priority,
          expiresIn: params.expiresIn
        })
      );

      await Promise.all(promises);
    } catch (error) {
      console.error('Error creating system announcement:', error);
      throw error;
    }
  }

  private async getUsersByModules(modules: string[]): Promise<string[]> {
    // Implementation would query users based on module access
    return [];
  }

  private async getAllActiveUsers(): Promise<string[]> {
    // Implementation would query all active users
    return [];
  }

  destroy(): void {
    this.userPreferences.clear();
    this.templates.clear();
    this.removeAllListeners();
  }
}

// Export singleton instance
export const hrNotificationService = HRNotificationService.getInstance();