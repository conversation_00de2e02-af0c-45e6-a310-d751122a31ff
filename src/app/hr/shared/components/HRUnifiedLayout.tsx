'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Users, 
  Search, 
  Briefcase, 
  Building, 
  UserCheck, 
  Settings, 
  Bell, 
  ChevronDown,
  Menu,
  X,
  Home,
  Target,
  Network,
  Gavel,
  FileText,
  GraduationCap,
  BarChart3,
  MessageSquare,
  Globe
} from 'lucide-react';
import { useHRAuth } from '../../hooks/useHRAuth';
import { hrNotificationService } from '../services/HRNotificationService';
import { hrSearchService, SearchResult } from '../services/HRSearchService';

interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon: React.ElementType;
  permissions?: string[];
  children?: NavigationItem[];
  badge?: string | number;
  external?: boolean;
}

const navigationConfig: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'HR Dashboard',
    href: '/hr',
    icon: Home,
    permissions: ['hr:read']
  },
  {
    id: 'recruitment',
    label: 'Recruitment',
    href: '/hr/recruitment',
    icon: Target,
    permissions: ['recruitment:read'],
    children: [
      { id: 'job-postings', label: 'Job Postings', href: '/hr/recruitment', icon: Briefcase },
      { id: 'applications', label: 'Applications', href: '/hr/recruitment/applications', icon: FileText },
      { id: 'candidates', label: 'Candidates', href: '/hr/recruitment/candidates', icon: Users }
    ]
  },
  {
    id: 'headhunter',
    label: 'Headhunter Portal',
    href: '/hr/headhunter',
    icon: Search,
    permissions: ['headhunter:read'],
    children: [
      { id: 'hh-dashboard', label: 'Dashboard', href: '/hr/headhunter/headhunter', icon: BarChart3 },
      { id: 'hh-analytics', label: 'Analytics', href: '/hr/headhunter/analytics', icon: BarChart3 },
      { id: 'hh-business', label: 'Business', href: '/hr/headhunter/business', icon: Building },
      { id: 'hh-ai', label: 'AI Showcase', href: '/hr/headhunter/ai-showcase', icon: Target },
      { id: 'hh-eor', label: 'EOR Services', href: '/hr/headhunter/eor-services', icon: Globe }
    ]
  },
  {
    id: 'hunter',
    label: 'Job Seekers Portal',
    href: '/hr/hunter',
    icon: Users,
    permissions: ['hunter:read'],
    children: [
      { id: 'job-search', label: 'Job Search', href: '/hr/hunter/jobs', icon: Search },
      { id: 'my-profile', label: 'My Profile', href: '/hr/hunter/profile', icon: UserCheck },
      { id: 'applications', label: 'My Applications', href: '/hr/hunter/applications', icon: FileText },
      { id: 'companies', label: 'Companies', href: '/hr/hunter/companies', icon: Building }
    ]
  },
  {
    id: 'upwork',
    label: 'Freelancing',
    href: '/hr/hunter/upwork',
    icon: Briefcase,
    permissions: ['upwork:read'],
    children: [
      { id: 'gigs', label: 'Find Gigs', href: '/hr/hunter/upwork/gigs', icon: Search },
      { id: 'my-proposals', label: 'My Proposals', href: '/hr/hunter/upwork/proposals', icon: FileText },
      { id: 'my-projects', label: 'My Projects', href: '/hr/hunter/upwork/projects', icon: Briefcase }
    ]
  },
  {
    id: 'abnrfp',
    label: 'Procurement (RFP)',
    href: '/hr/hunter/abnrfp',
    icon: FileText,
    permissions: ['rfp:read'],
    children: [
      { id: 'rfp-browse', label: 'Browse RFPs', href: '/hr/hunter/abnrfp/rfps', icon: Search },
      { id: 'my-rfps', label: 'My RFPs', href: '/hr/hunter/abnrfp/my-rfps', icon: FileText },
      { id: 'vendors', label: 'Vendors', href: '/hr/hunter/abnrfp/vendors', icon: Building }
    ]
  },
  {
    id: 'hrm',
    label: 'HR Management',
    href: '/hr/hrm',
    icon: UserCheck,
    permissions: ['hrm:read'],
    children: [
      { id: 'employees', label: 'Employees', href: '/hr/hrm/employees', icon: Users },
      { id: 'payroll', label: 'Payroll', href: '/hr/hrm/payroll', icon: BarChart3 },
      { id: 'training', label: 'Training', href: '/hr/hrm/training', icon: GraduationCap },
      { id: 'performance', label: 'Performance', href: '/hr/hrm/performance', icon: Target }
    ]
  },
  {
    id: 'worklink',
    label: 'Professional Network',
    href: '/hr/worklink',
    icon: Network,
    permissions: ['worklink:read'],
    children: [
      { id: 'connections', label: 'My Network', href: '/hr/worklink/connections', icon: Network },
      { id: 'matches', label: 'Discover', href: '/hr/worklink/discover', icon: Search },
      { id: 'messages', label: 'Messages', href: '/hr/worklink/messages', icon: MessageSquare }
    ]
  },
  {
    id: 'abnreferee',
    label: 'Dispute Resolution',
    href: '/hr/hunter/apps/abnreferee',
    icon: Gavel,
    permissions: ['referee:read']
  },
  {
    id: 'onboarding',
    label: 'AI Onboarding',
    href: '/hr/onboarding-buddy',
    icon: GraduationCap,
    permissions: ['onboarding:read']
  }
];

interface HRUnifiedLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
}

export default function HRUnifiedLayout({ 
  children, 
  title, 
  subtitle, 
  actions 
}: HRUnifiedLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const pathname = usePathname();
  const { user, hasPermission } = useHRAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);

  // Load notifications
  useEffect(() => {
    if (user?.id) {
      const loadNotifications = async () => {
        try {
          const userNotifications = await hrNotificationService.getNotifications(user.id);
          setNotifications(userNotifications);
          
          const count = await hrNotificationService.getUnreadCount(user.id);
          setUnreadCount(count);
        } catch (error) {
          console.error('Error loading notifications:', error);
        }
      };

      loadNotifications();

      // Listen for new notifications
      const handleNewNotification = () => {
        loadNotifications();
      };

      hrNotificationService.on('notification:created', handleNewNotification);
      hrNotificationService.on('notification:read', handleNewNotification);

      return () => {
        hrNotificationService.off('notification:created', handleNewNotification);
        hrNotificationService.off('notification:read', handleNewNotification);
      };
    }
  }, [user?.id]);

  // Auto-expand current section
  useEffect(() => {
    const currentSection = navigationConfig.find(item => 
      pathname.startsWith(item.href) || 
      item.children?.some(child => pathname.startsWith(child.href))
    );
    if (currentSection && currentSection.children) {
      setExpandedItems(prev => new Set([...prev, currentSection.id]));
    }
  }, [pathname]);

  // Search functionality
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    setSearchLoading(true);
    try {
      const results = await hrSearchService.search(query, {}, { limit: 10 });
      setSearchResults(results);
      setShowSearchResults(true);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      handleSearch(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  const handleSearchResultClick = (result: SearchResult) => {
    setShowSearchResults(false);
    setSearchQuery('');
    // Navigation will be handled by the Link component
  };

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const isActive = (href: string) => {
    if (href === '/hr') {
      return pathname === '/hr';
    }
    return pathname.startsWith(href);
  };

  const filterNavigation = (items: NavigationItem[]): NavigationItem[] => {
    return items.filter(item => {
      if (item.permissions && !item.permissions.some(permission => hasPermission(permission))) {
        return false;
      }
      if (item.children) {
        item.children = filterNavigation(item.children);
      }
      return true;
    });
  };

  const filteredNavigation = filterNavigation([...navigationConfig]);

  const unreadNotifications = unreadCount;

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <Link href="/hr" className="flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            <span className="ml-3 text-lg font-semibold text-gray-900">HR Suite</span>
          </Link>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {filteredNavigation.map((item) => {
              const isItemActive = isActive(item.href);
              const isExpanded = expandedItems.has(item.id);
              const hasChildren = item.children && item.children.length > 0;

              return (
                <div key={item.id}>
                  <div className="flex items-center">
                    <Link
                      href={item.href}
                      className={`
                        flex-1 flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                        ${isItemActive 
                          ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' 
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                        }
                      `}
                    >
                      <item.icon className={`
                        mr-3 h-5 w-5 flex-shrink-0
                        ${isItemActive ? 'text-blue-600' : 'text-gray-400'}
                      `} />
                      {item.label}
                      {item.badge && (
                        <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                    {hasChildren && (
                      <button
                        onClick={() => toggleExpanded(item.id)}
                        className="p-1 ml-1 rounded hover:bg-gray-100"
                      >
                        <ChevronDown className={`
                          w-4 h-4 text-gray-400 transition-transform
                          ${isExpanded ? 'rotate-180' : ''}
                        `} />
                      </button>
                    )}
                  </div>

                  {hasChildren && isExpanded && (
                    <div className="ml-6 mt-1 space-y-1">
                      {item.children!.map((child) => (
                        <Link
                          key={child.id}
                          href={child.href}
                          className={`
                            flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                            ${isActive(child.href)
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                            }
                          `}
                        >
                          <child.icon className={`
                            mr-3 h-4 w-4 flex-shrink-0
                            ${isActive(child.href) ? 'text-blue-600' : 'text-gray-400'}
                          `} />
                          {child.label}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </nav>

        {/* User Profile */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-white">
          {user ? (
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user.name?.charAt(0) || user.email?.charAt(0) || 'U'}
                </span>
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-900">{user.name || 'User'}</p>
                <p className="text-xs text-gray-500">{user.email}</p>
              </div>
              <Link
                href="/hr/settings"
                className="p-1 rounded-md text-gray-400 hover:text-gray-600"
              >
                <Settings className="w-4 h-4" />
              </Link>
            </div>
          ) : (
            <Link
              href="/hr/auth/login"
              className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Sign In
            </Link>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Top Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
                >
                  <Menu className="w-5 h-5" />
                </button>
                
                <div className="ml-4 lg:ml-0">
                  {title && (
                    <>
                      <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
                      {subtitle && (
                        <p className="text-sm text-gray-600">{subtitle}</p>
                      )}
                    </>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Global Search */}
                <div className="hidden md:block">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={handleSearchChange}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="Search across HR Suite..."
                      onFocus={() => searchQuery && setShowSearchResults(true)}
                      onBlur={() => setTimeout(() => setShowSearchResults(false), 200)}
                    />
                    
                    {/* Search Results Dropdown */}
                    {showSearchResults && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-96 overflow-y-auto">
                        {searchLoading ? (
                          <div className="p-4 text-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent mx-auto"></div>
                            <p className="text-sm text-gray-500 mt-2">Searching...</p>
                          </div>
                        ) : searchResults.length > 0 ? (
                          <div className="py-2">
                            {searchResults.map((result) => (
                              <Link
                                key={result.id}
                                href={result.url}
                                className="block px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                                onClick={() => handleSearchResultClick(result)}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                      {result.title}
                                    </p>
                                    {result.subtitle && (
                                      <p className="text-sm text-gray-500 truncate">
                                        {result.subtitle}
                                      </p>
                                    )}
                                    <p className="text-xs text-gray-400 mt-1 line-clamp-2">
                                      {result.description}
                                    </p>
                                  </div>
                                  <div className="ml-3 flex-shrink-0">
                                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                      {result.type}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex items-center justify-between mt-2">
                                  <span className="text-xs text-blue-600 font-medium">
                                    {result.module}
                                  </span>
                                  <span className="text-xs text-gray-400">
                                    {Math.round(result.relevance)}% match
                                  </span>
                                </div>
                              </Link>
                            ))}
                          </div>
                        ) : searchQuery && (
                          <div className="p-4 text-center">
                            <p className="text-sm text-gray-500">No results found for "{searchQuery}"</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Notifications */}
                <Link
                  href="/hr/notifications"
                  className="relative p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                >
                  <Bell className="w-5 h-5" />
                  {unreadNotifications > 0 && (
                    <span className="absolute -top-0.5 -right-0.5 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {unreadNotifications > 9 ? '9+' : unreadNotifications}
                    </span>
                  )}
                </Link>

                {/* Actions */}
                {actions}
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {children}
          </div>
        </main>
      </div>

      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}