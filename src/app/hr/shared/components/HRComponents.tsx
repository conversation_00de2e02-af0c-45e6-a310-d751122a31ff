'use client';

import React from 'react';
import { hrTheme, getModuleTheme, getModuleComponent } from '../styles/theme';
import { cn } from '../../../../lib/utils';

// Button Component
interface HRButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  module?: keyof typeof hrTheme.modules;
  loading?: boolean;
  icon?: React.ReactNode;
}

export const HRButton: React.FC<HRButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  module,
  loading = false,
  icon,
  className,
  disabled,
  ...props
}) => {
  const baseClasses = hrTheme.components.button.base;
  const variantClasses = hrTheme.components.button.variants[variant];
  const sizeClasses = hrTheme.components.button.sizes[size];
  
  // Apply module-specific styling if module is provided
  const moduleTheme = module ? getModuleTheme(module) : null;
  const moduleClasses = moduleTheme && variant === 'primary' 
    ? `bg-[${moduleTheme.primary}] hover:bg-[${moduleTheme.secondary}] focus:ring-[${moduleTheme.primary}]`
    : '';

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses,
        sizeClasses,
        moduleClasses,
        loading && 'opacity-50 cursor-not-allowed',
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {icon && !loading && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
};

// Card Component
interface HRCardProps extends React.HTMLAttributes<HTMLDivElement> {
  hover?: boolean;
  interactive?: boolean;
  module?: keyof typeof hrTheme.modules;
  component?: string;
}

export const HRCard: React.FC<HRCardProps> = ({
  children,
  hover = false,
  interactive = false,
  module,
  component,
  className,
  ...props
}) => {
  let cardClasses = hrTheme.components.card.base;
  
  if (hover) cardClasses += ` ${hrTheme.components.card.hover}`;
  if (interactive) cardClasses += ` ${hrTheme.components.card.interactive}`;
  
  // Apply module-specific component styling
  if (module && component) {
    const moduleComponentClass = getModuleComponent(module, component);
    if (moduleComponentClass) {
      cardClasses = moduleComponentClass;
    }
  }

  return (
    <div
      className={cn(cardClasses, className)}
      {...props}
    >
      {children}
    </div>
  );
};

// Input Component
interface HRInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  module?: keyof typeof hrTheme.modules;
}

export const HRInput: React.FC<HRInputProps> = ({
  label,
  error,
  helperText,
  module,
  className,
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  const baseClasses = hrTheme.components.input.base;
  const errorClasses = hrTheme.components.input.error;
  
  // Apply module-specific styling if module is provided
  const moduleTheme = module ? getModuleTheme(module) : null;
  const moduleClasses = moduleTheme 
    ? `focus:border-[${moduleTheme.primary}] focus:ring-[${moduleTheme.primary}]`
    : '';

  return (
    <div className="space-y-1">
      {label && (
        <label htmlFor={inputId} className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      <input
        id={inputId}
        className={cn(
          baseClasses,
          error ? errorClasses : moduleClasses,
          className
        )}
        {...props}
      />
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      {helperText && !error && (
        <p className="text-sm text-gray-500">{helperText}</p>
      )}
    </div>
  );
};

// Badge Component
interface HRBadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  module?: keyof typeof hrTheme.modules;
}

export const HRBadge: React.FC<HRBadgeProps> = ({
  children,
  variant = 'primary',
  module,
  className,
  ...props
}) => {
  const baseClasses = hrTheme.components.badge.base;
  const variantClasses = hrTheme.components.badge.variants[variant];
  
  // Apply module-specific styling if module is provided
  const moduleTheme = module ? getModuleTheme(module) : null;
  const moduleClasses = moduleTheme && variant === 'primary'
    ? `bg-[${moduleTheme.primary}]/10 text-[${moduleTheme.primary}]`
    : '';

  return (
    <span
      className={cn(
        baseClasses,
        variantClasses,
        moduleClasses,
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
};

// Notification Component
interface HRNotificationProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'success' | 'warning' | 'error' | 'info';
  title?: string;
  onClose?: () => void;
}

export const HRNotification: React.FC<HRNotificationProps> = ({
  children,
  variant = 'info',
  title,
  onClose,
  className,
  ...props
}) => {
  const baseClasses = hrTheme.components.notification.base;
  const variantClasses = hrTheme.components.notification.variants[variant];

  return (
    <div
      className={cn(baseClasses, variantClasses, className)}
      {...props}
    >
      <div className="flex">
        <div className="flex-1">
          {title && (
            <h4 className="text-sm font-medium mb-1">{title}</h4>
          )}
          <div className="text-sm">{children}</div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-4 inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <span className="sr-only">Close</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

// Loading Component
interface HRLoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  module?: keyof typeof hrTheme.modules;
}

export const HRLoading: React.FC<HRLoadingProps> = ({
  size = 'md',
  text,
  module
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const moduleTheme = module ? getModuleTheme(module) : null;
  const spinnerColor = moduleTheme?.primary || hrTheme.colors.primary[600];

  return (
    <div className="flex flex-col items-center justify-center p-4">
      <div className={cn("animate-spin rounded-full border-2 border-gray-200", sizeClasses[size])}>
        <div 
          className={cn("rounded-full border-2 border-transparent", sizeClasses[size])}
          style={{ borderTopColor: spinnerColor }}
        ></div>
      </div>
      {text && (
        <p className="mt-2 text-sm text-gray-600">{text}</p>
      )}
    </div>
  );
};

// Module Theme Provider
interface HRModuleThemeProviderProps {
  module: keyof typeof hrTheme.modules;
  children: React.ReactNode;
}

export const HRModuleThemeProvider: React.FC<HRModuleThemeProviderProps> = ({
  module,
  children
}) => {
  const moduleTheme = getModuleTheme(module);
  
  return (
    <div 
      data-hr-module={module}
      style={{
        '--hr-module-primary': moduleTheme.primary,
        '--hr-module-secondary': moduleTheme.secondary,
        '--hr-module-accent': moduleTheme.accent,
      } as React.CSSProperties}
    >
      {children}
    </div>
  );
};

// Section Header Component
interface HRSectionHeaderProps {
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
  module?: keyof typeof hrTheme.modules;
}

export const HRSectionHeader: React.FC<HRSectionHeaderProps> = ({
  title,
  subtitle,
  actions,
  module
}) => {
  const moduleTheme = module ? getModuleTheme(module) : null;

  return (
    <div className="flex items-center justify-between mb-6">
      <div>
        <h2 
          className="text-2xl font-bold text-gray-900"
          style={moduleTheme ? { color: moduleTheme.primary } : {}}
        >
          {title}
        </h2>
        {subtitle && (
          <p className="mt-1 text-sm text-gray-600">{subtitle}</p>
        )}
      </div>
      {actions && (
        <div className="flex items-center space-x-2">
          {actions}
        </div>
      )}
    </div>
  );
};

// Stats Card Component
interface HRStatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon?: React.ReactNode;
  module?: keyof typeof hrTheme.modules;
}

export const HRStatsCard: React.FC<HRStatsCardProps> = ({
  title,
  value,
  change,
  icon,
  module
}) => {
  const moduleTheme = module ? getModuleTheme(module) : null;
  const primaryColor = moduleTheme?.primary || hrTheme.colors.primary[600];

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <p 
            className="text-3xl font-bold mt-1"
            style={{ color: primaryColor }}
          >
            {value}
          </p>
          {change && (
            <div className="flex items-center mt-2">
              <span 
                className={cn(
                  "text-sm font-medium",
                  change.type === 'increase' ? 'text-green-600' : 'text-red-600'
                )}
              >
                {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
              </span>
              <span className="text-sm text-gray-500 ml-1">vs last period</span>
            </div>
          )}
        </div>
        {icon && (
          <div 
            className="p-3 rounded-lg"
            style={{ backgroundColor: `${primaryColor}20`, color: primaryColor }}
          >
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};

// Export all components and utilities
export {
  hrTheme,
  getModuleTheme,
  getModuleComponent
};