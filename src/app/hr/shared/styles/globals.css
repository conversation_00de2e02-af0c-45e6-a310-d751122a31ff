/* HR Suite Global Styles */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS Custom Properties for HR Theme */
:root {
  /* Primary Brand Colors */
  --hr-primary-50: #eff6ff;
  --hr-primary-100: #dbeafe;
  --hr-primary-200: #bfdbfe;
  --hr-primary-300: #93c5fd;
  --hr-primary-400: #60a5fa;
  --hr-primary-500: #3b82f6;
  --hr-primary-600: #2563eb;
  --hr-primary-700: #1d4ed8;
  --hr-primary-800: #1e40af;
  --hr-primary-900: #1e3a8a;

  /* Secondary Colors */
  --hr-secondary-50: #f0f9ff;
  --hr-secondary-100: #e0f2fe;
  --hr-secondary-200: #bae6fd;
  --hr-secondary-300: #7dd3fc;
  --hr-secondary-400: #38bdf8;
  --hr-secondary-500: #0ea5e9;
  --hr-secondary-600: #0284c7;
  --hr-secondary-700: #0369a1;
  --hr-secondary-800: #075985;
  --hr-secondary-900: #0c4a6e;

  /* Success Colors */
  --hr-success-50: #f0fdf4;
  --hr-success-100: #dcfce7;
  --hr-success-200: #bbf7d0;
  --hr-success-300: #86efac;
  --hr-success-400: #4ade80;
  --hr-success-500: #22c55e;
  --hr-success-600: #16a34a;
  --hr-success-700: #15803d;
  --hr-success-800: #166534;
  --hr-success-900: #14532d;

  /* Warning Colors */
  --hr-warning-50: #fffbeb;
  --hr-warning-100: #fef3c7;
  --hr-warning-200: #fde68a;
  --hr-warning-300: #fcd34d;
  --hr-warning-400: #fbbf24;
  --hr-warning-500: #f59e0b;
  --hr-warning-600: #d97706;
  --hr-warning-700: #b45309;
  --hr-warning-800: #92400e;
  --hr-warning-900: #78350f;

  /* Error Colors */
  --hr-error-50: #fef2f2;
  --hr-error-100: #fee2e2;
  --hr-error-200: #fecaca;
  --hr-error-300: #fca5a5;
  --hr-error-400: #f87171;
  --hr-error-500: #ef4444;
  --hr-error-600: #dc2626;
  --hr-error-700: #b91c1c;
  --hr-error-800: #991b1b;
  --hr-error-900: #7f1d1d;

  /* Layout Constants */
  --hr-sidebar-width: 16rem;
  --hr-sidebar-collapsed-width: 4rem;
  --hr-header-height: 4rem;
  --hr-max-width: 80rem;
  --hr-container-padding: 1.5rem;

  /* Typography */
  --hr-font-family: 'Inter', system-ui, sans-serif;
  --hr-font-mono: 'JetBrains Mono', 'Consolas', monospace;

  /* Shadows */
  --hr-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --hr-shadow-default: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --hr-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --hr-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --hr-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --hr-radius-sm: 0.125rem;
  --hr-radius-default: 0.25rem;
  --hr-radius-md: 0.375rem;
  --hr-radius-lg: 0.5rem;
  --hr-radius-xl: 0.75rem;
  --hr-radius-2xl: 1rem;
  --hr-radius-3xl: 1.5rem;
  --hr-radius-full: 9999px;
}

/* Module-specific color overrides */
[data-hr-module="recruitment"] {
  --hr-module-primary: #3b82f6;
  --hr-module-secondary: #0ea5e9;
  --hr-module-accent: #6366f1;
}

[data-hr-module="headhunter"] {
  --hr-module-primary: #059669;
  --hr-module-secondary: #0d9488;
  --hr-module-accent: #7c3aed;
}

[data-hr-module="hunter"] {
  --hr-module-primary: #2563eb;
  --hr-module-secondary: #3b82f6;
  --hr-module-accent: #8b5cf6;
}

[data-hr-module="upwork"] {
  --hr-module-primary: #7c2d12;
  --hr-module-secondary: #ea580c;
  --hr-module-accent: #f59e0b;
}

[data-hr-module="abnrfp"] {
  --hr-module-primary: #be123c;
  --hr-module-secondary: #e11d48;
  --hr-module-accent: #f43f5e;
}

[data-hr-module="hrm"] {
  --hr-module-primary: #1d4ed8;
  --hr-module-secondary: #2563eb;
  --hr-module-accent: #3730a3;
}

[data-hr-module="worklink"] {
  --hr-module-primary: #7c3aed;
  --hr-module-secondary: #8b5cf6;
  --hr-module-accent: #a855f7;
}

[data-hr-module="onboarding"] {
  --hr-module-primary: #059669;
  --hr-module-secondary: #10b981;
  --hr-module-accent: #34d399;
}

[data-hr-module="abnreferee"] {
  --hr-module-primary: #374151;
  --hr-module-secondary: #4b5563;
  --hr-module-accent: #6b7280;
}

/* HR Component Classes */
.hr-button {
  @apply inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
}

.hr-button-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.hr-button-secondary {
  @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
}

.hr-button-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.hr-button-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.hr-button-error {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.hr-button-outline {
  @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500;
}

.hr-button-sm {
  @apply text-sm px-3 py-1.5;
}

.hr-button-md {
  @apply text-sm px-4 py-2;
}

.hr-button-lg {
  @apply text-base px-6 py-3;
}

.hr-button-xl {
  @apply text-lg px-8 py-4;
}

.hr-card {
  @apply bg-white rounded-lg border border-gray-200 shadow-sm;
}

.hr-card-hover {
  @apply hover:shadow-md transition-shadow duration-200;
}

.hr-card-interactive {
  @apply cursor-pointer hover:shadow-md hover:border-gray-300 transition-all duration-200;
}

.hr-input {
  @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm;
}

.hr-input-error {
  @apply border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500;
}

.hr-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.hr-badge-primary {
  @apply bg-blue-100 text-blue-800;
}

.hr-badge-secondary {
  @apply bg-gray-100 text-gray-800;
}

.hr-badge-success {
  @apply bg-green-100 text-green-800;
}

.hr-badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.hr-badge-error {
  @apply bg-red-100 text-red-800;
}

.hr-notification {
  @apply p-4 rounded-md border;
}

.hr-notification-success {
  @apply bg-green-50 border-green-200 text-green-800;
}

.hr-notification-warning {
  @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.hr-notification-error {
  @apply bg-red-50 border-red-200 text-red-800;
}

.hr-notification-info {
  @apply bg-blue-50 border-blue-200 text-blue-800;
}

/* Module-specific component styles */
.hr-recruitment-job-card {
  @apply bg-white border border-blue-100 rounded-lg shadow-sm hover:shadow-md hover:border-blue-200 transition-all duration-200;
}

.hr-recruitment-application-badge {
  @apply bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium;
}

.hr-headhunter-candidate-card {
  @apply bg-white border border-emerald-100 rounded-lg shadow-sm hover:shadow-md hover:border-emerald-200 transition-all duration-200;
}

.hr-headhunter-match-score {
  @apply bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-semibold;
}

.hr-hunter-job-listing {
  @apply bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md hover:border-blue-200 transition-all duration-200;
}

.hr-hunter-company-logo {
  @apply w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center;
}

.hr-upwork-gig-card {
  @apply bg-white border border-orange-100 rounded-lg shadow-sm hover:shadow-md hover:border-orange-200 transition-all duration-200;
}

.hr-upwork-proposal-status {
  @apply bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium;
}

.hr-abnrfp-rfp-card {
  @apply bg-white border border-rose-100 rounded-lg shadow-sm hover:shadow-md hover:border-rose-200 transition-all duration-200;
}

.hr-abnrfp-vendor-badge {
  @apply bg-rose-100 text-rose-800 px-2 py-1 rounded-full text-xs font-medium;
}

.hr-hrm-employee-card {
  @apply bg-white border border-blue-100 rounded-lg shadow-sm hover:shadow-md hover:border-blue-200 transition-all duration-200;
}

.hr-hrm-performance-metric {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4;
}

.hr-worklink-profile-card {
  @apply bg-white border border-purple-100 rounded-lg shadow-sm hover:shadow-md hover:border-purple-200 transition-all duration-200;
}

.hr-worklink-connection-status {
  @apply bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium;
}

/* Animation Classes */
.hr-fade-in {
  @apply opacity-0 transition-opacity duration-200;
}

.hr-fade-in.active {
  @apply opacity-100;
}

.hr-slide-in {
  @apply transform -translate-x-full transition-transform duration-300;
}

.hr-slide-in.active {
  @apply translate-x-0;
}

.hr-scale-in {
  @apply transform scale-95 transition-transform duration-200;
}

.hr-scale-in.active {
  @apply scale-100;
}

.hr-bounce {
  @apply animate-bounce;
}

.hr-pulse {
  @apply animate-pulse;
}

.hr-spin {
  @apply animate-spin;
}

/* Layout Utilities */
.hr-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.hr-section {
  @apply py-16;
}

.hr-section-sm {
  @apply py-8;
}

.hr-section-lg {
  @apply py-24;
}

/* Typography */
.hr-heading-1 {
  @apply text-4xl md:text-5xl font-bold text-gray-900;
}

.hr-heading-2 {
  @apply text-3xl font-bold text-gray-900;
}

.hr-heading-3 {
  @apply text-2xl font-semibold text-gray-900;
}

.hr-heading-4 {
  @apply text-xl font-semibold text-gray-900;
}

.hr-body-large {
  @apply text-lg text-gray-600;
}

.hr-body {
  @apply text-base text-gray-700;
}

.hr-body-small {
  @apply text-sm text-gray-600;
}

.hr-caption {
  @apply text-xs text-gray-500;
}

/* Custom scrollbar for HR Suite */
.hr-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.hr-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.hr-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.hr-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading states */
.hr-loading {
  @apply animate-pulse bg-gray-200 rounded;
}

.hr-loading-text {
  @apply h-4 bg-gray-200 rounded w-3/4 animate-pulse;
}

.hr-loading-circle {
  @apply w-10 h-10 bg-gray-200 rounded-full animate-pulse;
}

/* Focus states for accessibility */
.hr-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Print styles */
@media print {
  .hr-no-print {
    display: none !important;
  }
  
  .hr-print-only {
    display: block !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .hr-dark {
    /* Dark theme variables would go here */
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .hr-high-contrast {
    /* High contrast overrides would go here */
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .hr-animate {
    animation: none !important;
    transition: none !important;
  }
}