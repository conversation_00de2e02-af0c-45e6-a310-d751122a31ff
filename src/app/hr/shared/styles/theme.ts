// HR Suite Unified Theme Configuration

export const hrTheme = {
  // Primary Brand Colors
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe', 
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',  // Main brand blue
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a'
    },
    secondary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',  // Accent blue
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e'
    },
    accent: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',  // Neutral gray
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a'
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',  // Success green
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d'
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',  // Warning yellow
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f'
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',  // Error red
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d'
    }
  },

  // Module-specific color overrides
  modules: {
    recruitment: {
      primary: '#3b82f6',    // Blue
      secondary: '#0ea5e9',
      accent: '#6366f1'      // Indigo
    },
    headhunter: {
      primary: '#059669',    // Emerald
      secondary: '#0d9488',  // Teal
      accent: '#7c3aed'      // Violet
    },
    hunter: {
      primary: '#2563eb',    // Blue
      secondary: '#3b82f6',
      accent: '#8b5cf6'      // Purple
    },
    upwork: {
      primary: '#7c2d12',    // Orange
      secondary: '#ea580c',
      accent: '#f59e0b'      // Amber
    },
    abnrfp: {
      primary: '#be123c',    // Rose
      secondary: '#e11d48',
      accent: '#f43f5e'      // Pink
    },
    hrm: {
      primary: '#1d4ed8',    // Blue
      secondary: '#2563eb',
      accent: '#3730a3'      // Indigo
    },
    worklink: {
      primary: '#7c3aed',    // Violet
      secondary: '#8b5cf6',  // Purple
      accent: '#a855f7'      // Purple
    },
    onboarding: {
      primary: '#059669',    // Emerald
      secondary: '#10b981',
      accent: '#34d399'      // Green
    },
    abnreferee: {
      primary: '#374151',    // Gray
      secondary: '#4b5563',
      accent: '#6b7280'      // Gray
    }
  },

  // Typography Scale
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace']
    },
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      '5xl': ['3rem', { lineHeight: '1' }],
      '6xl': ['3.75rem', { lineHeight: '1' }]
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700'
    }
  },

  // Spacing Scale
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem'
  },

  // Border Radius
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    DEFAULT: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px'
  },

  // Shadows
  boxShadow: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: 'none'
  },

  // Component Styles
  components: {
    button: {
      base: 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',
      variants: {
        primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
        secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500',
        success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
        warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
        error: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
        outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500'
      },
      sizes: {
        sm: 'text-sm px-3 py-1.5',
        md: 'text-sm px-4 py-2',
        lg: 'text-base px-6 py-3',
        xl: 'text-lg px-8 py-4'
      }
    },
    card: {
      base: 'bg-white rounded-lg border border-gray-200 shadow-sm',
      hover: 'hover:shadow-md transition-shadow duration-200',
      interactive: 'cursor-pointer hover:shadow-md hover:border-gray-300 transition-all duration-200'
    },
    input: {
      base: 'block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm',
      error: 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
    },
    badge: {
      base: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
      variants: {
        primary: 'bg-blue-100 text-blue-800',
        secondary: 'bg-gray-100 text-gray-800',
        success: 'bg-green-100 text-green-800',
        warning: 'bg-yellow-100 text-yellow-800',
        error: 'bg-red-100 text-red-800'
      }
    },
    notification: {
      base: 'p-4 rounded-md border',
      variants: {
        success: 'bg-green-50 border-green-200 text-green-800',
        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
        error: 'bg-red-50 border-red-200 text-red-800',
        info: 'bg-blue-50 border-blue-200 text-blue-800'
      }
    }
  },

  // Module-specific component overrides
  moduleComponents: {
    recruitment: {
      jobCard: 'bg-white border border-blue-100 rounded-lg shadow-sm hover:shadow-md hover:border-blue-200 transition-all duration-200',
      applicationBadge: 'bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium'
    },
    headhunter: {
      candidateCard: 'bg-white border border-emerald-100 rounded-lg shadow-sm hover:shadow-md hover:border-emerald-200 transition-all duration-200',
      matchScore: 'bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-semibold'
    },
    hunter: {
      jobListing: 'bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md hover:border-blue-200 transition-all duration-200',
      companyLogo: 'w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center'
    },
    upwork: {
      gigCard: 'bg-white border border-orange-100 rounded-lg shadow-sm hover:shadow-md hover:border-orange-200 transition-all duration-200',
      proposalStatus: 'bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium'
    },
    abnrfp: {
      rfpCard: 'bg-white border border-rose-100 rounded-lg shadow-sm hover:shadow-md hover:border-rose-200 transition-all duration-200',
      vendorBadge: 'bg-rose-100 text-rose-800 px-2 py-1 rounded-full text-xs font-medium'
    },
    hrm: {
      employeeCard: 'bg-white border border-blue-100 rounded-lg shadow-sm hover:shadow-md hover:border-blue-200 transition-all duration-200',
      performanceMetric: 'bg-blue-50 border border-blue-200 rounded-lg p-4'
    },
    worklink: {
      profileCard: 'bg-white border border-purple-100 rounded-lg shadow-sm hover:shadow-md hover:border-purple-200 transition-all duration-200',
      connectionStatus: 'bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium'
    }
  },

  // Animation Presets
  animations: {
    fadeIn: 'animate-in fade-in duration-200',
    slideIn: 'animate-in slide-in-from-left duration-300',
    scaleIn: 'animate-in zoom-in-95 duration-200',
    bounce: 'animate-bounce',
    pulse: 'animate-pulse',
    spin: 'animate-spin'
  },

  // Layout Constants
  layout: {
    sidebar: {
      width: '16rem',
      collapsedWidth: '4rem'
    },
    header: {
      height: '4rem'
    },
    maxWidth: '80rem', // max-w-7xl
    containerPadding: '1.5rem' // px-6
  }
} as const;

// Utility function to get module-specific colors
export function getModuleTheme(module: keyof typeof hrTheme.modules) {
  return hrTheme.modules[module] || hrTheme.modules.recruitment;
}

// Utility function to get module-specific component styles
export function getModuleComponent(module: keyof typeof hrTheme.moduleComponents, component: string) {
  const moduleComponents = hrTheme.moduleComponents[module];
  return moduleComponents?.[component as keyof typeof moduleComponents] || '';
}

// CSS custom properties for dynamic theming
export function generateCSSCustomProperties(module?: keyof typeof hrTheme.modules) {
  const moduleTheme = module ? getModuleTheme(module) : hrTheme.colors.primary;
  
  return {
    '--hr-primary': moduleTheme.primary,
    '--hr-secondary': moduleTheme.secondary,
    '--hr-accent': moduleTheme.accent,
    '--hr-primary-50': hrTheme.colors.primary[50],
    '--hr-primary-100': hrTheme.colors.primary[100],
    '--hr-primary-500': hrTheme.colors.primary[500],
    '--hr-primary-600': hrTheme.colors.primary[600],
    '--hr-primary-700': hrTheme.colors.primary[700],
    '--hr-success': hrTheme.colors.success[500],
    '--hr-warning': hrTheme.colors.warning[500],
    '--hr-error': hrTheme.colors.error[500],
    '--hr-sidebar-width': hrTheme.layout.sidebar.width,
    '--hr-header-height': hrTheme.layout.header.height
  };
}