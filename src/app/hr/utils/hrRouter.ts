'use client';

/**
 * HR Router Utility
 * 
 * This utility provides proper navigation for the HR module,
 * ensuring redirects work correctly on the production domain
 * instead of defaulting to localhost.
 */

let routerInstance: any = null;

export const HRRouter = {
  // Initialize with Next.js router instance
  init: (router: any) => {
    routerInstance = router;
  },

  // Navigate to a path within the HR module
  push: (path: string) => {
    if (routerInstance) {
      routerInstance.push(path);
    } else {
      // Fallback to window.location for non-component contexts
      // But ensure we stay on the same domain
      if (typeof window !== 'undefined') {
        const currentOrigin = window.location.origin;
        if (path.startsWith('/')) {
          window.location.href = currentOrigin + path;
        } else {
          window.location.href = path;
        }
      }
    }
  },

  // Navigate and replace current history entry
  replace: (path: string) => {
    if (routerInstance) {
      routerInstance.replace(path);
    } else {
      // Fallback to window.location.replace for non-component contexts
      if (typeof window !== 'undefined') {
        const currentOrigin = window.location.origin;
        if (path.startsWith('/')) {
          window.location.replace(currentOrigin + path);
        } else {
          window.location.replace(path);
        }
      }
    }
  },

  // Go back in history
  back: () => {
    if (routerInstance) {
      routerInstance.back();
    } else {
      if (typeof window !== 'undefined') {
        window.history.back();
      }
    }
  },

  // Navigate to HR login
  toLogin: (redirect?: string) => {
    const loginPath = `/hr/login${redirect ? `?redirect=${encodeURIComponent(redirect)}` : ''}`;
    HRRouter.push(loginPath);
  },

  // Navigate to HR home
  toHome: () => {
    HRRouter.push('/hr');
  },

  // Navigate to access denied page
  toAccessDenied: () => {
    HRRouter.push('/hr/access-denied');
  }
};

export default HRRouter;
