'use client'

import React, { useState, useEffect, useRef } from 'react';
import {
  Search,
  Filter,
  X,
  SlidersHorizontal,
  Star,
  TrendingUp,
  Package,
  Calendar,
  Tag,
  Hash,
  User,
  Clock,
  BarChart3,
  Grid,
  List,
  ChevronDown,
  ChevronUp,
  Bookmark,
  Eye,
  Download,
  Zap,
  Shield,
  Globe,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';
import { AdvancedSearchEngine, SearchFilters, SearchResult, SearchSuggestion } from '../utils/searchEngine';

// Common search suggestions - constant array moved outside component
const COMMON_SEARCHES = [
  'farming apps',
  'business tools',
  'admin dashboard',
  'crm software',
  'productivity apps',
  'analytics tools',
  'e-commerce platforms',
  'health monitoring',
  'educational games',
  'project management',
  'data visualization',
  'mobile apps',
  'web applications',
  'developer tools'
];

interface AdvancedSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  availableCategories: string[];
  availableTags: string[];
  availableDevelopers: string[];
  onTrackSearch: (query: string) => void;
  searchHistory: string[];
  onClearHistory: () => void;
  searchEngine?: AdvancedSearchEngine;
  onSearchResults?: (results: SearchResult[]) => void;
  placeholder?: string;
  showAdvancedFilters?: boolean;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  searchQuery,
  onSearchChange,
  filters,
  onFiltersChange,
  availableCategories,
  availableTags = [],
  availableDevelopers = [],
  onTrackSearch,
  searchHistory,
  onClearHistory,
  searchEngine,
  onSearchResults,
  placeholder = "Search applications, categories, or features...",
  showAdvancedFilters = true
}) => {
  const [showFilters, setShowFilters] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [expandedFilterSections, setExpandedFilterSections] = useState<Set<string>>(new Set(['categories', 'features']));
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Enhanced search suggestions using search engine
  useEffect(() => {
    if (searchQuery.length > 0) {
      let enhancedSuggestions: SearchSuggestion[] = [];

      // Use search engine if available
      if (searchEngine) {
        enhancedSuggestions = searchEngine.generateSuggestions(searchQuery, 6);
      }

      // Add search history suggestions
      const historySuggestions: SearchSuggestion[] = searchHistory
        .filter(item =>
          item.toLowerCase().includes(searchQuery.toLowerCase()) &&
          item !== searchQuery
        )
        .slice(0, 3)
        .map(item => ({
          text: item,
          type: 'query' as const,
          icon: 'Clock'
        }));

      // Add common search suggestions
      const commonSuggestions: SearchSuggestion[] = COMMON_SEARCHES
        .filter((item: string) =>
          item.toLowerCase().includes(searchQuery.toLowerCase()) &&
          item !== searchQuery &&
          !searchHistory.includes(item)
        )
        .slice(0, 3)
        .map((item: string) => ({
          text: item,
          type: 'query' as const,
          icon: 'Search'
        }));

      // Combine all suggestions
      const allSuggestions = [
        ...historySuggestions,
        ...enhancedSuggestions,
        ...commonSuggestions
      ].slice(0, 10);

      // Only update suggestions if they've actually changed
      setSuggestions(prev => {
        if (prev.length !== allSuggestions.length) return allSuggestions;
        const hasChanged = prev.some((prevSuggestion, index) => 
          prevSuggestion.text !== allSuggestions[index]?.text ||
          prevSuggestion.type !== allSuggestions[index]?.type
        );
        return hasChanged ? allSuggestions : prev;
      });
      setShowSuggestions(allSuggestions.length > 0);
    } else {
      // Clear suggestions and hide dropdown when search is empty
      setSuggestions(prev => prev.length > 0 ? [] : prev);
      setShowSuggestions(prev => prev ? false : prev);
    }
  }, [searchQuery, searchHistory, availableCategories, searchEngine]);

  // Perform search when query or filters change
  useEffect(() => {
    if (searchEngine && onSearchResults) {
      setIsSearching(true);

      // Debounce search
      const timeoutId = setTimeout(() => {
        const results = searchEngine.search(searchQuery, filters);
        setSearchResults(results);
        onSearchResults(results);
        setIsSearching(false);
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [searchQuery, filters, searchEngine, onSearchResults]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onTrackSearch(searchQuery.trim());
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    let searchText = suggestion.text;

    // Add prefixes for different suggestion types
    if (suggestion.type === 'category') {
      searchText = `category:${suggestion.text}`;
    } else if (suggestion.type === 'tag') {
      searchText = `tag:${suggestion.text}`;
    } else if (suggestion.type === 'developer') {
      searchText = `developer:${suggestion.text}`;
    }

    onSearchChange(searchText);
    onTrackSearch(searchText);
    setShowSuggestions(false);
    searchInputRef.current?.blur();
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'app': return Package;
      case 'category': return Tag;
      case 'tag': return Hash;
      case 'developer': return User;
      case 'query': return Search;
      default: return Search;
    }
  };

  const handleCategoryToggle = (category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];
    
    onFiltersChange({ ...filters, categories: newCategories });
  };

  const handleFeatureToggle = (feature: 'isHot' | 'isNew' | 'isFeatured') => {
    const newFeatures = filters.features.includes(feature)
      ? filters.features.filter(f => f !== feature)
      : [...filters.features, feature];

    onFiltersChange({ ...filters, features: newFeatures });
  };

  const handleTagToggle = (tag: string) => {
    const currentTags = filters.tags || [];
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];

    onFiltersChange({ ...filters, tags: newTags });
  };

  const handlePlatformToggle = (platform: string) => {
    const currentPlatforms = filters.platform || [];
    const newPlatforms = currentPlatforms.includes(platform)
      ? currentPlatforms.filter(p => p !== platform)
      : [...currentPlatforms, platform];

    onFiltersChange({ ...filters, platform: newPlatforms });
  };

  const handleRatingRangeChange = (min: number, max: number) => {
    onFiltersChange({
      ...filters,
      ratingRange: { min, max }
    });
  };

  const toggleFilterSection = (section: string) => {
    const newExpanded = new Set(expandedFilterSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedFilterSections(newExpanded);
  };

  const clearAllFilters = () => {
    onFiltersChange({
      categories: [],
      features: [],
      sortBy: 'relevance',
      sortOrder: 'desc',
      showHidden: false,
      tags: [],
      platform: [],
      ratingRange: undefined,
      dateRange: undefined,
      developer: undefined,
      size: undefined
    });
  };

  const hasActiveFilters = filters.categories.length > 0 ||
                          filters.features.length > 0 ||
                          filters.sortBy !== 'relevance' ||
                          filters.sortOrder !== 'desc' ||
                          filters.showHidden ||
                          (filters.tags && filters.tags.length > 0) ||
                          (filters.platform && filters.platform.length > 0) ||
                          filters.ratingRange ||
                          filters.dateRange ||
                          filters.developer ||
                          filters.size;

  return (
    <div className="relative">
      {/* Search Bar */}
      <form onSubmit={handleSearchSubmit} className="relative">
        <div className="relative">
          <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder={placeholder}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            onFocus={() => setShowSuggestions(suggestions.length > 0)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            className={`w-full pl-10 pr-20 py-3 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all ${
              isSearching ? 'bg-gray-50' : 'bg-white'
            }`}
            disabled={isSearching}
          />
          
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
            {searchQuery && (
              <button
                type="button"
                onClick={() => {
                  onSearchChange('');
                  setShowSuggestions(false);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
            
            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className={`p-1 rounded ${
                showFilters || hasActiveFilters
                  ? 'text-green-600 bg-green-100' 
                  : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <SlidersHorizontal className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Enhanced Search Suggestions */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
            {suggestions.map((suggestion, index) => {
              const IconComponent = getSuggestionIcon(suggestion.type);
              return (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left px-4 py-3 hover:bg-gray-50 flex items-center space-x-3 transition-colors"
                >
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                    suggestion.type === 'app' ? 'bg-blue-100 text-blue-600' :
                    suggestion.type === 'category' ? 'bg-green-100 text-green-600' :
                    suggestion.type === 'tag' ? 'bg-purple-100 text-purple-600' :
                    suggestion.type === 'developer' ? 'bg-orange-100 text-orange-600' :
                    'bg-gray-100 text-gray-600'
                  }`}>
                    <IconComponent className="w-4 h-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium truncate">{suggestion.text}</span>
                      {suggestion.count && (
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                          {suggestion.count}
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 capitalize">{suggestion.type}</div>
                  </div>
                  {suggestion.type === 'query' && searchHistory.includes(suggestion.text) && (
                    <Clock className="w-3 h-3 text-gray-400" />
                  )}
                </button>
              );
            })}

            {/* Search Tips */}
            <div className="border-t bg-gray-50 p-3">
              <div className="text-xs text-gray-600 space-y-1">
                <div><strong>Tips:</strong> Use prefixes like "category:", "tag:", "developer:" for specific searches</div>
                <div>Example: "category:business" or "tag:productivity"</div>
              </div>
              {searchHistory.length > 0 && (
                <button
                  type="button"
                  onClick={onClearHistory}
                  className="text-xs text-red-600 hover:text-red-800 mt-2"
                >
                  Clear search history
                </button>
              )}
            </div>
          </div>
        )}
      </form>

      {/* Enhanced Advanced Filters */}
      {showFilters && showAdvancedFilters && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border rounded-lg shadow-lg z-40 max-h-96 overflow-y-auto">
          <div className="sticky top-0 bg-white border-b p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                <Filter className="w-4 h-4" />
                <span>Advanced Filters</span>
                {hasActiveFilters && (
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                    Active
                  </span>
                )}
              </h3>
              {hasActiveFilters && (
                <button
                  onClick={clearAllFilters}
                  className="text-sm text-red-600 hover:text-red-800 flex items-center space-x-1"
                >
                  <X className="w-3 h-3" />
                  <span>Clear all</span>
                </button>
              )}
            </div>
          </div>

          <div className="p-4 space-y-6">
            {/* Categories */}
            <div className="border rounded-lg">
              <button
                onClick={() => toggleFilterSection('categories')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
              >
                <div className="flex items-center space-x-2">
                  <Tag className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Categories</span>
                  {filters.categories.length > 0 && (
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      {filters.categories.length}
                    </span>
                  )}
                </div>
                {expandedFilterSections.has('categories') ?
                  <ChevronUp className="w-4 h-4 text-gray-400" /> :
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                }
              </button>
              {expandedFilterSections.has('categories') && (
                <div className="p-3 border-t bg-gray-50">
                  <div className="flex flex-wrap gap-2">
                    {availableCategories.map((category) => (
                      <button
                        key={category}
                        onClick={() => handleCategoryToggle(category)}
                        className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                          filters.categories.includes(category)
                            ? 'bg-green-100 text-green-800 border-green-300'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Features */}
            <div className="border rounded-lg">
              <button
                onClick={() => toggleFilterSection('features')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
              >
                <div className="flex items-center space-x-2">
                  <Star className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Features</span>
                  {filters.features.length > 0 && (
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      {filters.features.length}
                    </span>
                  )}
                </div>
                {expandedFilterSections.has('features') ?
                  <ChevronUp className="w-4 h-4 text-gray-400" /> :
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                }
              </button>
              {expandedFilterSections.has('features') && (
                <div className="p-3 border-t bg-gray-50">
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => handleFeatureToggle('isFeatured')}
                      className={`px-3 py-1 rounded-full text-sm border transition-colors flex items-center space-x-1 ${
                        filters.features.includes('isFeatured')
                          ? 'bg-yellow-100 text-yellow-800 border-yellow-300'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                      }`}
                    >
                      <Star className="w-3 h-3" />
                      <span>Featured</span>
                    </button>

                    <button
                      onClick={() => handleFeatureToggle('isHot')}
                      className={`px-3 py-1 rounded-full text-sm border transition-colors flex items-center space-x-1 ${
                        filters.features.includes('isHot')
                          ? 'bg-red-100 text-red-800 border-red-300'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                      }`}
                    >
                      <TrendingUp className="w-3 h-3" />
                      <span>Hot</span>
                    </button>

                    <button
                      onClick={() => handleFeatureToggle('isNew')}
                      className={`px-3 py-1 rounded-full text-sm border transition-colors flex items-center space-x-1 ${
                        filters.features.includes('isNew')
                          ? 'bg-green-100 text-green-800 border-green-300'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                      }`}
                    >
                      <Package className="w-3 h-3" />
                      <span>New</span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Tags */}
            {availableTags.length > 0 && (
              <div className="border rounded-lg">
                <button
                  onClick={() => toggleFilterSection('tags')}
                  className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-2">
                    <Hash className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">Tags</span>
                    {filters.tags && filters.tags.length > 0 && (
                      <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                        {filters.tags.length}
                      </span>
                    )}
                  </div>
                  {expandedFilterSections.has('tags') ?
                    <ChevronUp className="w-4 h-4 text-gray-400" /> :
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  }
                </button>
                {expandedFilterSections.has('tags') && (
                  <div className="p-3 border-t bg-gray-50">
                    <div className="flex flex-wrap gap-2">
                      {availableTags.map((tag) => (
                        <button
                          key={tag}
                          onClick={() => handleTagToggle(tag)}
                          className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                            filters.tags?.includes(tag)
                              ? 'bg-purple-100 text-purple-800 border-purple-300'
                              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                          }`}
                        >
                          #{tag}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Platform */}
            <div className="border rounded-lg">
              <button
                onClick={() => toggleFilterSection('platform')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
              >
                <div className="flex items-center space-x-2">
                  <Monitor className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Platform</span>
                  {filters.platform && filters.platform.length > 0 && (
                    <span className="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full">
                      {filters.platform.length}
                    </span>
                  )}
                </div>
                {expandedFilterSections.has('platform') ?
                  <ChevronUp className="w-4 h-4 text-gray-400" /> :
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                }
              </button>
              {expandedFilterSections.has('platform') && (
                <div className="p-3 border-t bg-gray-50">
                  <div className="flex flex-wrap gap-2">
                    {['web', 'mobile', 'desktop', 'tablet'].map((platform) => (
                      <button
                        key={platform}
                        onClick={() => handlePlatformToggle(platform)}
                        className={`px-3 py-1 rounded-full text-sm border transition-colors flex items-center space-x-1 ${
                          filters.platform?.includes(platform)
                            ? 'bg-indigo-100 text-indigo-800 border-indigo-300'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                        }`}
                      >
                        {platform === 'web' && <Globe className="w-3 h-3" />}
                        {platform === 'mobile' && <Smartphone className="w-3 h-3" />}
                        {platform === 'desktop' && <Monitor className="w-3 h-3" />}
                        {platform === 'tablet' && <Tablet className="w-3 h-3" />}
                        <span className="capitalize">{platform}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Rating Range */}
            <div className="border rounded-lg">
              <button
                onClick={() => toggleFilterSection('rating')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
              >
                <div className="flex items-center space-x-2">
                  <Star className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Rating</span>
                  {filters.ratingRange && (
                    <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                      {filters.ratingRange.min}-{filters.ratingRange.max}★
                    </span>
                  )}
                </div>
                {expandedFilterSections.has('rating') ?
                  <ChevronUp className="w-4 h-4 text-gray-400" /> :
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                }
              </button>
              {expandedFilterSections.has('rating') && (
                <div className="p-3 border-t bg-gray-50">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600 w-8">Min:</span>
                      <input
                        type="range"
                        min="0"
                        max="5"
                        step="0.5"
                        value={filters.ratingRange?.min || 0}
                        onChange={(e) => handleRatingRangeChange(
                          parseFloat(e.target.value),
                          filters.ratingRange?.max || 5
                        )}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600 w-8">{filters.ratingRange?.min || 0}★</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600 w-8">Max:</span>
                      <input
                        type="range"
                        min="0"
                        max="5"
                        step="0.5"
                        value={filters.ratingRange?.max || 5}
                        onChange={(e) => handleRatingRangeChange(
                          filters.ratingRange?.min || 0,
                          parseFloat(e.target.value)
                        )}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600 w-8">{filters.ratingRange?.max || 5}★</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Sorting */}
            <div className="border rounded-lg">
              <button
                onClick={() => toggleFilterSection('sorting')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
              >
                <div className="flex items-center space-x-2">
                  <BarChart3 className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Sorting</span>
                </div>
                {expandedFilterSections.has('sorting') ?
                  <ChevronUp className="w-4 h-4 text-gray-400" /> :
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                }
              </button>
              {expandedFilterSections.has('sorting') && (
                <div className="p-3 border-t bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                      <select
                        value={filters.sortBy}
                        onChange={(e) => onFiltersChange({
                          ...filters,
                          sortBy: e.target.value as SearchFilters['sortBy']
                        })}
                        className="w-full border rounded-lg px-3 py-2 text-sm"
                      >
                        <option value="relevance">Relevance</option>
                        <option value="name">Name</option>
                        <option value="category">Category</option>
                        <option value="rating">Rating</option>
                        <option value="recent">Recently Added</option>
                        <option value="popularity">Popularity</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Order</label>
                      <select
                        value={filters.sortOrder}
                        onChange={(e) => onFiltersChange({
                          ...filters,
                          sortOrder: e.target.value as SearchFilters['sortOrder']
                        })}
                        className="w-full border rounded-lg px-3 py-2 text-sm"
                      >
                        <option value="asc">Ascending</option>
                        <option value="desc">Descending</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Additional Options */}
            <div className="border rounded-lg">
              <button
                onClick={() => toggleFilterSection('options')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
              >
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Options</span>
                </div>
                {expandedFilterSections.has('options') ?
                  <ChevronUp className="w-4 h-4 text-gray-400" /> :
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                }
              </button>
              {expandedFilterSections.has('options') && (
                <div className="p-3 border-t bg-gray-50 space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={filters.showHidden}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        showHidden: e.target.checked
                      })}
                      className="rounded"
                    />
                    <span className="text-sm text-gray-700">Show hidden apps</span>
                  </label>

                  {/* Developer Filter */}
                  {availableDevelopers.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Developer</label>
                      <select
                        value={filters.developer || ''}
                        onChange={(e) => onFiltersChange({
                          ...filters,
                          developer: e.target.value || undefined
                        })}
                        className="w-full border rounded-lg px-3 py-2 text-sm"
                      >
                        <option value="">All Developers</option>
                        {availableDevelopers.map((developer) => (
                          <option key={developer} value={developer}>{developer}</option>
                        ))}
                      </select>
                    </div>
                  )}

                  {/* App Size Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">App Size</label>
                    <select
                      value={filters.size || ''}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        size: e.target.value as SearchFilters['size'] || undefined
                      })}
                      className="w-full border rounded-lg px-3 py-2 text-sm"
                    >
                      <option value="">Any Size</option>
                      <option value="small">Small (&lt; 1MB)</option>
                      <option value="medium">Medium (1-10MB)</option>
                      <option value="large">Large (&gt; 10MB)</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Search Statistics */}
            {searchEngine && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-xs text-blue-800 space-y-1">
                  <div className="font-medium">Search Statistics</div>
                  <div>Total Apps: {searchEngine.getSearchStats().totalApps}</div>
                  <div>Categories: {searchEngine.getSearchStats().totalCategories}</div>
                  <div>Search Index: {searchEngine.getSearchStats().indexSize} terms</div>
                  {searchResults.length > 0 && (
                    <div className="mt-2 pt-2 border-t border-blue-300">
                      <div className="font-medium">Current Results: {searchResults.length}</div>
                      {searchResults.length > 0 && searchQuery && (
                        <div>Avg. Relevance: {(searchResults.reduce((sum, r) => sum + r.relevanceScore, 0) / searchResults.length).toFixed(1)}</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedSearch;
