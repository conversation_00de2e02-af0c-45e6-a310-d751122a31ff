import { defaultPortalIndustry } from './default-portal';
import { aiHospitalIndustry } from './ai-hospital';
import { hrIndustry } from './hr-industry';
import { IndustryConfig } from '../types';

// Export all industry configurations
export const industries: IndustryConfig[] = [
  defaultPortalIndustry,
  aiHospitalIndustry,
  hrIndustry
];

// Export individual industries
export { defaultPortalIndustry, aiHospitalIndustry, hrIndustry };

// Helper function to get industry by ID
export function getIndustryById(id: string): IndustryConfig | undefined {
  return industries.find(industry => industry.id === id);
}

// Helper function to get all industry IDs
export function getAllIndustryIds(): string[] {
  return industries.map(industry => industry.id);
}

// Helper function to get all industry names
export function getAllIndustryNames(): { id: string; name: string }[] {
  return industries.map(industry => ({ id: industry.id, name: industry.name }));
}
