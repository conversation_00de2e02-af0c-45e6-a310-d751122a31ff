import { IndustryConfig, App, IndustryCategory } from '../types';

// HR Applications
const hrApps: App[] = [
  {
    id: 'hunter',
    name: 'Hunter - Job Seekers Portal',
    path: '/hr/hunter',
    description: 'Vietnam\'s leading job portal for job seekers to find opportunities and connect with employers',
    category: ['Job Seekers', 'Job Search'],
    isHot: true,
    isNew: false
  },
  {
    id: 'headhunter',
    name: 'Headhunter - Recruiter Portal',
    path: '/hr/headhunter',
    description: 'Professional recruitment platform for headhunters and talent acquisition specialists',
    category: ['Recruiters', 'Talent Acquisition'],
    isHot: true,
    isNew: false
  },
  {
    id: 'upwork-clone',
    name: 'Upwork Clone - Freelancing',
    path: '/hr/hunter/upwork',
    description: 'Comprehensive freelancing platform for gig-based work and project management',
    category: ['Freelancing', 'Gig Economy'],
    isHot: false,
    isNew: true
  },
  {
    id: 'abnrfp',
    name: 'ABN RFP Platform',
    path: '/hr/hunter/abnrfp',
    description: 'Request for Proposal platform for procurement and vendor management',
    category: ['Procurement', 'Vendor Management'],
    isHot: false,
    isNew: false
  },
  {
    id: 'abnreferee',
    name: 'ABN Referee - Dispute Resolution',
    path: '/hr/hunter/apps/abnreferee',
    description: 'Professional dispute resolution and arbitration system',
    category: ['Legal', 'Dispute Resolution'],
    isHot: false,
    isNew: false
  },
  {
    id: 'onboarding-buddy',
    name: 'AI Onboarding Buddy',
    path: '/onboarding-buddy',
    description: 'AI-powered onboarding system for seamless new hire experience',
    category: ['Onboarding', 'AI Tools'],
    isHot: true,
    isNew: true
  },
  {
    id: 'workdone-hrm',
    name: 'WorkDone HRM',
    path: '/workdone/hrm',
    description: 'Comprehensive HR management system for employee lifecycle',
    category: ['HR Management', 'Employee Management'],
    isHot: false,
    isNew: false
  },
  {
    id: 'hr-recruitment',
    name: 'HR Recruitment',
    path: '/hr/recruitment',
    description: 'Advanced recruitment management system with AI-powered candidate matching',
    category: ['Recruiters', 'Talent Acquisition'],
    isHot: false,
    isNew: false
  },
  {
    id: 'hr-talents',
    name: 'HR Talents',
    path: '/hr/talents',
    description: 'Talent management and development platform for workforce optimization',
    category: ['Talent Management', 'Employee Development'],
    isHot: false,
    isNew: false
  },
  {
    id: 'worklink',
    name: 'WorkLink',
    path: '/hr/worklink',
    description: 'Professional networking and collaboration platform for workplace connections',
    category: ['Networking', 'Collaboration'],
    isHot: false,
    isNew: true
  },
  {
    id: 'jobseeker-dashboard',
    name: 'JobSeeker Dashboard',
    path: '/hr/jobseeker/dashboard',
    description: 'Personalized dashboard for job seekers to track applications and opportunities',
    category: ['Job Seekers', 'Dashboard'],
    isHot: false,
    isNew: false
  },
  {
    id: 'hr-hrm',
    name: 'HR Management System',
    path: '/hr/hrm',
    description: 'Complete HR management solution for employee lifecycle management',
    category: ['HR Management', 'Employee Management'],
    isHot: false,
    isNew: false
  }
];

// HR Categories
const hrCategories: IndustryCategory[] = [
  {
    id: 'all',
    name: 'All',
    description: 'All HR applications and tools',
    color: '#6366f1',
    subcategories: []
  },
  {
    id: 'job-seekers',
    name: 'Job Seekers',
    description: 'Tools and platforms for job seekers to find opportunities',
    color: '#3b82f6',
    subcategories: [
      {
        id: 'job-search',
        name: 'Job Search',
        description: 'Job search and discovery tools'
      },
      {
        id: 'dashboard',
        name: 'Dashboard',
        description: 'Personal dashboards for job seekers'
      }
    ]
  },
  {
    id: 'recruiters',
    name: 'Recruiters',
    description: 'Professional recruitment and talent acquisition tools',
    color: '#10b981',
    subcategories: [
      {
        id: 'talent-acquisition',
        name: 'Talent Acquisition',
        description: 'Tools for finding and acquiring talent'
      },
      {
        id: 'candidate-management',
        name: 'Candidate Management',
        description: 'Systems for managing recruitment candidates'
      }
    ]
  },
  {
    id: 'freelancing',
    name: 'Freelancing',
    description: 'Platforms for freelance work and gig economy',
    color: '#f59e0b',
    subcategories: [
      {
        id: 'gig-economy',
        name: 'Gig Economy',
        description: 'Platforms for gig-based work'
      },
      {
        id: 'project-management',
        name: 'Project Management',
        description: 'Tools for managing freelance projects'
      }
    ]
  },
  {
    id: 'hr-management',
    name: 'HR Management',
    description: 'Comprehensive HR management systems',
    color: '#8b5cf6',
    subcategories: [
      {
        id: 'employee-management',
        name: 'Employee Management',
        description: 'Systems for managing employee lifecycle'
      },
      {
        id: 'payroll',
        name: 'Payroll',
        description: 'Payroll management systems'
      },
      {
        id: 'performance',
        name: 'Performance',
        description: 'Performance management tools'
      }
    ]
  },
  {
    id: 'onboarding',
    name: 'Onboarding',
    description: 'Employee onboarding and integration tools',
    color: '#06b6d4',
    subcategories: [
      {
        id: 'ai-tools',
        name: 'AI Tools',
        description: 'AI-powered onboarding solutions'
      },
      {
        id: 'automation',
        name: 'Automation',
        description: 'Automated onboarding processes'
      }
    ]
  },
  {
    id: 'talent-management',
    name: 'Talent Management',
    description: 'Tools for talent development and management',
    color: '#ef4444',
    subcategories: [
      {
        id: 'employee-development',
        name: 'Employee Development',
        description: 'Tools for employee growth and development'
      },
      {
        id: 'skills-management',
        name: 'Skills Management',
        description: 'Skills tracking and development'
      }
    ]
  },
  {
    id: 'procurement',
    name: 'Procurement',
    description: 'Procurement and vendor management systems',
    color: '#84cc16',
    subcategories: [
      {
        id: 'vendor-management',
        name: 'Vendor Management',
        description: 'Systems for managing vendors and suppliers'
      },
      {
        id: 'rfp-management',
        name: 'RFP Management',
        description: 'Request for Proposal management'
      }
    ]
  },
  {
    id: 'legal',
    name: 'Legal',
    description: 'Legal and compliance tools for HR',
    color: '#f97316',
    subcategories: [
      {
        id: 'dispute-resolution',
        name: 'Dispute Resolution',
        description: 'Tools for resolving workplace disputes'
      },
      {
        id: 'compliance',
        name: 'Compliance',
        description: 'HR compliance management'
      }
    ]
  },
  {
    id: 'networking',
    name: 'Networking',
    description: 'Professional networking and collaboration',
    color: '#ec4899',
    subcategories: [
      {
        id: 'collaboration',
        name: 'Collaboration',
        description: 'Workplace collaboration tools'
      },
      {
        id: 'professional-network',
        name: 'Professional Network',
        description: 'Professional networking platforms'
      }
    ]
  }
];

// HR Industry Configuration
export const hrIndustry: IndustryConfig = {
  id: 'hr',
  name: 'Human Resources',
  description: 'Comprehensive HR solutions for recruitment, talent management, and workforce optimization',
  icon: 'Users',
  color: '#3b82f6',
  theme: {
    primaryColor: '#3b82f6',
    secondaryColor: '#1e40af',
    accentColor: '#60a5fa',
    backgroundColor: '#f8fafc'
  },
  categories: hrCategories,
  apps: hrApps,
  metadata: {
    version: '1.0.0',
    lastUpdated: new Date().toISOString(),
    author: 'ABN Green',
    tags: ['hr', 'recruitment', 'talent-management', 'workforce']
  }
};
