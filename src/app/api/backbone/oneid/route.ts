// ABN ONEID Main API Route

import { NextRequest, NextResponse } from 'next/server';
import { initializeOneID } from '@/app/backbone/oneid';

export async function GET(request: NextRequest) {
  try {
    const oneID = await initializeOneID();
    const healthCheck = await oneID.healthCheck();

    return NextResponse.json({
      message: 'ABN ONEID Authentication System',
      version: '1.0.0',
      status: healthCheck.status,
      endpoints: {
        // Authentication endpoints
        auth: {
          login: '/api/backbone/oneid/auth/login',
          logout: '/api/backbone/oneid/auth/logout',
          refresh: '/api/backbone/oneid/auth/refresh',
          register: {
            individual: '/api/backbone/oneid/auth/register/individual',
            company: '/api/backbone/oneid/auth/register/company',
            employee: '/api/backbone/oneid/auth/register/employee'
          },
          magicLink: {
            send: '/api/backbone/oneid/auth/magic-link',
            verify: '/api/backbone/oneid/auth/magic-link/verify'
          },
          password: {
            change: '/api/backbone/oneid/auth/password/change',
            reset: '/api/backbone/oneid/auth/password/reset',
            forgot: '/api/backbone/oneid/auth/password/forgot'
          }
        },
        
        // User management endpoints
        users: {
          profile: '/api/backbone/oneid/users/profile',
          search: '/api/backbone/oneid/users/search',
          sessions: '/api/backbone/oneid/users/sessions'
        },
        
        // Company management endpoints
        companies: {
          list: '/api/backbone/oneid/companies',
          details: '/api/backbone/oneid/companies/[id]',
          employees: '/api/backbone/oneid/companies/[id]/employees',
          settings: '/api/backbone/oneid/companies/[id]/settings'
        },
        
        // Admin endpoints
        admin: {
          users: '/api/backbone/oneid/admin/users',
          companies: '/api/backbone/oneid/admin/companies',
          statistics: '/api/backbone/oneid/admin/statistics',
          health: '/api/backbone/oneid/admin/health'
        }
      },
      healthCheck
    });
  } catch (error) {
    console.error('ONEID API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to initialize ONEID system'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
