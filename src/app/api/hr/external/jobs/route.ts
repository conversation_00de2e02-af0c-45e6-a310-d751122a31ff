import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

const HR_DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'hr');
const JOBS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'jobs.json');
const COMPANIES_FILE = path.join(HR_DATA_DIR, 'recruitment', 'companies.json');
const API_KEYS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'api_keys.json');

interface ApiKey {
  id: string;
  key: string;
  companyId: string;
  tenantId: string;
  name: string;
  permissions: string[];
  isActive: boolean;
  createdAt: string;
  lastUsed?: string;
}

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  responsibilities?: string;
  benefits?: string;
  salaryMin?: number;
  salaryMax?: number;
  salaryType?: string;
  location?: string;
  remoteWork: boolean;
  employmentType: string;
  experienceLevel: string;
  department?: string;
  companyId: string;
  postedById: string;
  applicationDeadline?: string;
  startDate?: string;
  isUrgent: boolean;
  status: string;
  tenantId: string;
  viewCount: number;
  applicationCount: number;
  skills: Array<{
    name: string;
    level: string;
    isRequired: boolean;
  }>;
  createdAt: string;
  updatedAt: string;
  source?: string; // Track if job came from external API
}

async function ensureDirectoryExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function readJsonFile<T>(filePath: string, defaultValue: T): Promise<T> {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch {
    await ensureDirectoryExists(path.dirname(filePath));
    await fs.writeFile(filePath, JSON.stringify(defaultValue, null, 2));
    return defaultValue;
  }
}

async function writeJsonFile<T>(filePath: string, data: T): Promise<void> {
  await ensureDirectoryExists(path.dirname(filePath));
  await fs.writeFile(filePath, JSON.stringify(data, null, 2));
}

async function validateApiKey(apiKey: string): Promise<ApiKey | null> {
  const apiKeys = await readJsonFile<ApiKey[]>(API_KEYS_FILE, []);
  const key = apiKeys.find(k => k.key === apiKey && k.isActive);
  
  if (key) {
    // Update last used timestamp
    key.lastUsed = new Date().toISOString();
    await writeJsonFile(API_KEYS_FILE, apiKeys);
  }
  
  return key || null;
}

function formatJobForExternal(job: JobPosting) {
  return {
    id: job.id,
    title: job.title,
    description: job.description,
    requirements: job.requirements,
    responsibilities: job.responsibilities,
    benefits: job.benefits,
    salary: {
      min: job.salaryMin,
      max: job.salaryMax,
      type: job.salaryType
    },
    location: job.location,
    remote_work: job.remoteWork,
    employment_type: job.employmentType,
    experience_level: job.experienceLevel,
    department: job.department,
    application_deadline: job.applicationDeadline,
    start_date: job.startDate,
    is_urgent: job.isUrgent,
    status: job.status,
    skills: job.skills,
    created_at: job.createdAt,
    updated_at: job.updatedAt
  };
}

// GET /api/hr/external/jobs - Retrieve jobs for external HR systems
export async function GET(request: NextRequest) {
  try {
    const apiKey = request.headers.get('x-api-key');
    if (!apiKey) {
      return NextResponse.json({ 
        error: 'API key is required',
        code: 'MISSING_API_KEY'
      }, { status: 401 });
    }

    const validKey = await validateApiKey(apiKey);
    if (!validKey) {
      return NextResponse.json({ 
        error: 'Invalid or expired API key',
        code: 'INVALID_API_KEY'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const department = searchParams.get('department');
    const employmentType = searchParams.get('employment_type');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const since = searchParams.get('since'); // ISO date string

    const jobs = await readJsonFile<JobPosting[]>(JOBS_FILE, []);

    // Filter jobs for the API key's company and tenant
    let filteredJobs = jobs.filter(job => 
      job.companyId === validKey.companyId && 
      job.tenantId === validKey.tenantId
    );

    // Apply filters
    if (status) {
      filteredJobs = filteredJobs.filter(job => job.status === status);
    }

    if (department) {
      filteredJobs = filteredJobs.filter(job => job.department === department);
    }

    if (employmentType) {
      filteredJobs = filteredJobs.filter(job => job.employmentType === employmentType);
    }

    if (since) {
      const sinceDate = new Date(since);
      filteredJobs = filteredJobs.filter(job => new Date(job.updatedAt) > sinceDate);
    }

    // Sort by creation date (newest first)
    filteredJobs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Apply pagination
    const paginatedJobs = filteredJobs.slice(offset, offset + limit);

    // Format for external consumption
    const formattedJobs = paginatedJobs.map(formatJobForExternal);

    return NextResponse.json({
      jobs: formattedJobs,
      pagination: {
        total: filteredJobs.length,
        limit,
        offset,
        has_more: offset + limit < filteredJobs.length
      },
      meta: {
        company_id: validKey.companyId,
        tenant_id: validKey.tenantId,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('External API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// POST /api/hr/external/jobs - Create job posting from external HR system
export async function POST(request: NextRequest) {
  try {
    const apiKey = request.headers.get('x-api-key');
    if (!apiKey) {
      return NextResponse.json({ 
        error: 'API key is required',
        code: 'MISSING_API_KEY'
      }, { status: 401 });
    }

    const validKey = await validateApiKey(apiKey);
    if (!validKey || !validKey.permissions.includes('jobs:create')) {
      return NextResponse.json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      title,
      description,
      requirements,
      responsibilities,
      benefits,
      salary,
      location,
      remote_work,
      employment_type,
      experience_level,
      department,
      application_deadline,
      start_date,
      is_urgent,
      skills,
      status = 'active',
      external_id
    } = body;

    // Validate required fields
    if (!title || !description || !employment_type || !experience_level) {
      return NextResponse.json({ 
        error: 'Missing required fields: title, description, employment_type, experience_level',
        code: 'MISSING_REQUIRED_FIELDS'
      }, { status: 400 });
    }

    // Read existing jobs
    const jobs = await readJsonFile<JobPosting[]>(JOBS_FILE, []);

    // Check for duplicate external_id if provided
    if (external_id) {
      const existingJob = jobs.find(job => 
        job.source === 'external' && 
        (job as any).external_id === external_id &&
        job.companyId === validKey.companyId
      );
      
      if (existingJob) {
        return NextResponse.json({ 
          error: 'Job with this external_id already exists',
          code: 'DUPLICATE_EXTERNAL_ID',
          existing_job_id: existingJob.id
        }, { status: 409 });
      }
    }

    // Create new job posting
    const newJob: JobPosting & { external_id?: string } = {
      id: `job_ext_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      title,
      description,
      requirements,
      responsibilities,
      benefits,
      salaryMin: salary?.min,
      salaryMax: salary?.max,
      salaryType: salary?.type || 'yearly',
      location,
      remoteWork: Boolean(remote_work),
      employmentType: employment_type,
      experienceLevel: experience_level,
      department,
      companyId: validKey.companyId,
      postedById: 'external_system',
      applicationDeadline: application_deadline,
      startDate: start_date,
      isUrgent: Boolean(is_urgent),
      status,
      tenantId: validKey.tenantId,
      viewCount: 0,
      applicationCount: 0,
      skills: skills || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      source: 'external',
      external_id
    };

    // Add to jobs array
    jobs.push(newJob);

    // Save to file
    await writeJsonFile(JOBS_FILE, jobs);

    // Return formatted response
    return NextResponse.json({
      job: formatJobForExternal(newJob),
      message: 'Job created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('External API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// PUT /api/hr/external/jobs - Update job posting from external HR system
export async function PUT(request: NextRequest) {
  try {
    const apiKey = request.headers.get('x-api-key');
    if (!apiKey) {
      return NextResponse.json({ 
        error: 'API key is required',
        code: 'MISSING_API_KEY'
      }, { status: 401 });
    }

    const validKey = await validateApiKey(apiKey);
    if (!validKey || !validKey.permissions.includes('jobs:update')) {
      return NextResponse.json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('id');
    const externalId = searchParams.get('external_id');

    if (!jobId && !externalId) {
      return NextResponse.json({ 
        error: 'Job ID or external_id is required',
        code: 'MISSING_JOB_IDENTIFIER'
      }, { status: 400 });
    }

    const body = await request.json();
    const jobs = await readJsonFile<JobPosting[]>(JOBS_FILE, []);

    // Find job to update
    const jobIndex = jobs.findIndex(job => {
      const matchesId = jobId ? job.id === jobId : true;
      const matchesExternalId = externalId ? (job as any).external_id === externalId : true;
      const matchesCompany = job.companyId === validKey.companyId;
      
      return matchesId && matchesExternalId && matchesCompany;
    });

    if (jobIndex === -1) {
      return NextResponse.json({ 
        error: 'Job not found',
        code: 'JOB_NOT_FOUND'
      }, { status: 404 });
    }

    // Update job
    const existingJob = jobs[jobIndex];
    const updatedJob = {
      ...existingJob,
      ...body,
      id: existingJob.id, // Preserve original ID
      companyId: existingJob.companyId, // Preserve company
      tenantId: existingJob.tenantId, // Preserve tenant
      createdAt: existingJob.createdAt, // Preserve creation date
      updatedAt: new Date().toISOString(),
      // Handle nested salary object
      ...(body.salary && {
        salaryMin: body.salary.min,
        salaryMax: body.salary.max,
        salaryType: body.salary.type
      }),
      // Handle boolean conversion
      remoteWork: body.remote_work !== undefined ? Boolean(body.remote_work) : existingJob.remoteWork,
      isUrgent: body.is_urgent !== undefined ? Boolean(body.is_urgent) : existingJob.isUrgent,
    };

    jobs[jobIndex] = updatedJob;
    await writeJsonFile(JOBS_FILE, jobs);

    return NextResponse.json({
      job: formatJobForExternal(updatedJob),
      message: 'Job updated successfully'
    });

  } catch (error) {
    console.error('External API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}