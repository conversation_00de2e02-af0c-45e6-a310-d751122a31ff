import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const NOTIFICATIONS_FILE = path.join(process.cwd(), 'data/apps/hr/notifications.json');

interface HRNotification {
  id: string;
  userId: string;
  type: 'job_match' | 'application_update' | 'interview_scheduled' | 'system' | 'message';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
  tenantId: string;
  module: string;
}

interface NotificationsData {
  notifications: HRNotification[];
}

function readNotifications(): NotificationsData {
  try {
    if (!fs.existsSync(NOTIFICATIONS_FILE)) {
      return { notifications: [] };
    }
    const data = fs.readFileSync(NOTIFICATIONS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading notifications file:', error);
    return { notifications: [] };
  }
}

function writeNotifications(data: NotificationsData): void {
  try {
    const dataDir = path.dirname(NOTIFICATIONS_FILE);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    fs.writeFileSync(NOTIFICATIONS_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing notifications file:', error);
    throw error;
  }
}

// POST /api/hr/notifications/[id]/read
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const notificationId = params.id;
    
    if (!notificationId) {
      return NextResponse.json({ error: 'Notification ID is required' }, { status: 400 });
    }
    
    const data = readNotifications();
    const notificationIndex = data.notifications.findIndex(n => n.id === notificationId);
    
    if (notificationIndex === -1) {
      return NextResponse.json({ error: 'Notification not found' }, { status: 404 });
    }
    
    // Mark as read
    data.notifications[notificationIndex].read = true;
    writeNotifications(data);
    
    return NextResponse.json({ 
      message: 'Notification marked as read',
      notification: data.notifications[notificationIndex]
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return NextResponse.json({ error: 'Failed to mark notification as read' }, { status: 500 });
  }
}
