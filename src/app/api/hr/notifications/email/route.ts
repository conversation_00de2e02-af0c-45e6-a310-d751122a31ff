import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const EMAIL_LOG_FILE = path.join(process.cwd(), 'data/apps/hr/email-notifications.json');

interface EmailNotification {
  id: string;
  userId: string;
  notificationId: string;
  to: string;
  subject: string;
  body: string;
  sentAt: string;
  status: 'sent' | 'failed' | 'pending';
  error?: string;
}

interface EmailLogData {
  emails: EmailNotification[];
}

function ensureDataFile(): void {
  const dataDir = path.dirname(EMAIL_LOG_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  if (!fs.existsSync(EMAIL_LOG_FILE)) {
    const initialData: EmailLogData = { emails: [] };
    fs.writeFileSync(EMAIL_LOG_FILE, JSON.stringify(initialData, null, 2));
  }
}

function readEmailLog(): EmailLogData {
  ensureDataFile();
  try {
    const data = fs.readFileSync(EMAIL_LOG_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading email log file:', error);
    return { emails: [] };
  }
}

function writeEmailLog(data: EmailLogData): void {
  try {
    fs.writeFileSync(EMAIL_LOG_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing email log file:', error);
    throw error;
  }
}

// POST /api/hr/notifications/email
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, notification } = body;
    
    if (!userId || !notification) {
      return NextResponse.json({ 
        error: 'Missing required fields: userId, notification' 
      }, { status: 400 });
    }
    
    // In a real implementation, you would:
    // 1. Get user email from user service
    // 2. Send actual email using service like SendGrid, AWS SES, etc.
    // 3. Handle email templates and formatting
    
    // For now, we'll simulate email sending and log it
    const emailLog = readEmailLog();
    
    const emailNotification: EmailNotification = {
      id: `email-${Date.now()}`,
      userId,
      notificationId: notification.id,
      to: `user-${userId}@example.com`, // In real app, get from user profile
      subject: `HR Notification: ${notification.title}`,
      body: `
        <html>
          <body>
            <h2>${notification.title}</h2>
            <p>${notification.message}</p>
            ${notification.data ? `<p><strong>Additional Details:</strong> ${JSON.stringify(notification.data, null, 2)}</p>` : ''}
            <hr>
            <p><small>This is an automated notification from the HR system.</small></p>
          </body>
        </html>
      `,
      sentAt: new Date().toISOString(),
      status: 'sent' // In real implementation, this would be based on actual email service response
    };
    
    emailLog.emails.push(emailNotification);
    writeEmailLog(emailLog);
    
    console.log(`Email notification sent to ${emailNotification.to}: ${notification.title}`);
    
    return NextResponse.json({ 
      success: true,
      message: 'Email notification sent successfully',
      emailId: emailNotification.id
    });
  } catch (error) {
    console.error('Error sending email notification:', error);
    
    // Log failed email attempt
    try {
      const emailLog = readEmailLog();
      const failedEmail: EmailNotification = {
        id: `email-failed-${Date.now()}`,
        userId: request.body?.userId || 'unknown',
        notificationId: request.body?.notification?.id || 'unknown',
        to: 'unknown',
        subject: 'Failed to send',
        body: 'Failed to send email',
        sentAt: new Date().toISOString(),
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      emailLog.emails.push(failedEmail);
      writeEmailLog(emailLog);
    } catch (logError) {
      console.error('Error logging failed email:', logError);
    }
    
    return NextResponse.json({ 
      success: false,
      error: 'Failed to send email notification' 
    }, { status: 500 });
  }
}

// GET /api/hr/notifications/email - Get email log
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    
    const emailLog = readEmailLog();
    let emails = emailLog.emails;
    
    // Filter by user if specified
    if (userId) {
      emails = emails.filter(email => email.userId === userId);
    }
    
    // Filter by status if specified
    if (status) {
      emails = emails.filter(email => email.status === status);
    }
    
    // Sort by sent date (most recent first)
    emails.sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime());
    
    // Apply limit
    emails = emails.slice(0, limit);
    
    return NextResponse.json({
      emails,
      total: emails.length
    });
  } catch (error) {
    console.error('Error fetching email log:', error);
    return NextResponse.json({ error: 'Failed to fetch email log' }, { status: 500 });
  }
}
