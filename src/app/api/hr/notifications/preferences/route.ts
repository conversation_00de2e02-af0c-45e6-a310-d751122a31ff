import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const PREFERENCES_FILE = path.join(process.cwd(), 'data/apps/hr/notification-preferences.json');

interface NotificationPreferences {
  userId: string;
  email: boolean;
  push: boolean;
  inApp: boolean;
  types: {
    job_match: boolean;
    application_update: boolean;
    interview_scheduled: boolean;
    system: boolean;
    message: boolean;
  };
}

interface PreferencesData {
  preferences: NotificationPreferences[];
}

function ensureDataFile(): void {
  const dataDir = path.dirname(PREFERENCES_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  if (!fs.existsSync(PREFERENCES_FILE)) {
    const initialData: PreferencesData = { preferences: [] };
    fs.writeFileSync(PREFERENCES_FILE, JSON.stringify(initialData, null, 2));
  }
}

function readPreferences(): PreferencesData {
  ensureDataFile();
  try {
    const data = fs.readFileSync(PREFERENCES_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading preferences file:', error);
    return { preferences: [] };
  }
}

function writePreferences(data: PreferencesData): void {
  try {
    fs.writeFileSync(PREFERENCES_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing preferences file:', error);
    throw error;
  }
}

function getDefaultPreferences(userId: string): NotificationPreferences {
  return {
    userId,
    email: true,
    push: true,
    inApp: true,
    types: {
      job_match: true,
      application_update: true,
      interview_scheduled: true,
      system: true,
      message: true
    }
  };
}

// GET /api/hr/notifications/preferences
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    
    const data = readPreferences();
    let userPreferences = data.preferences.find(p => p.userId === userId);
    
    // If no preferences found, return default preferences
    if (!userPreferences) {
      userPreferences = getDefaultPreferences(userId);
      // Save default preferences
      data.preferences.push(userPreferences);
      writePreferences(data);
    }
    
    return NextResponse.json(userPreferences);
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    return NextResponse.json({ error: 'Failed to fetch preferences' }, { status: 500 });
  }
}

// POST /api/hr/notifications/preferences
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, email, push, inApp, types } = body;
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    
    const data = readPreferences();
    const existingIndex = data.preferences.findIndex(p => p.userId === userId);
    
    const updatedPreferences: NotificationPreferences = {
      userId,
      email: email !== undefined ? email : true,
      push: push !== undefined ? push : true,
      inApp: inApp !== undefined ? inApp : true,
      types: {
        job_match: types?.job_match !== undefined ? types.job_match : true,
        application_update: types?.application_update !== undefined ? types.application_update : true,
        interview_scheduled: types?.interview_scheduled !== undefined ? types.interview_scheduled : true,
        system: types?.system !== undefined ? types.system : true,
        message: types?.message !== undefined ? types.message : true
      }
    };
    
    if (existingIndex >= 0) {
      data.preferences[existingIndex] = updatedPreferences;
    } else {
      data.preferences.push(updatedPreferences);
    }
    
    writePreferences(data);
    
    return NextResponse.json(updatedPreferences);
  } catch (error) {
    console.error('Error updating notification preferences:', error);
    return NextResponse.json({ error: 'Failed to update preferences' }, { status: 500 });
  }
}
