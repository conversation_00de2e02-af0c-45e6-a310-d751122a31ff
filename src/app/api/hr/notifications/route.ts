import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const NOTIFICATIONS_FILE = path.join(process.cwd(), 'data/apps/hr/notifications.json');

interface HRNotification {
  id: string;
  userId: string;
  type: 'job_match' | 'application_update' | 'interview_scheduled' | 'system' | 'message';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
  tenantId: string;
  module: string;
}

interface NotificationsData {
  notifications: HRNotification[];
}

function ensureDataFile(): void {
  const dataDir = path.dirname(NOTIFICATIONS_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  if (!fs.existsSync(NOTIFICATIONS_FILE)) {
    const initialData: NotificationsData = { notifications: [] };
    fs.writeFileSync(NOTIFICATIONS_FILE, JSON.stringify(initialData, null, 2));
  }
}

function readNotifications(): NotificationsData {
  ensureDataFile();
  try {
    const data = fs.readFileSync(NOTIFICATIONS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading notifications file:', error);
    return { notifications: [] };
  }
}

function writeNotifications(data: NotificationsData): void {
  try {
    fs.writeFileSync(NOTIFICATIONS_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing notifications file:', error);
    throw error;
  }
}

// GET /api/hr/notifications
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';
    const limit = searchParams.get('limit');
    const module = searchParams.get('module');
    const type = searchParams.get('type');
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    
    const data = readNotifications();
    let notifications = data.notifications || [];
    
    // Filter by user
    notifications = notifications.filter((n: HRNotification) => n.userId === userId);
    
    // Filter by module if specified
    if (module) {
      notifications = notifications.filter((n: HRNotification) => n.module === module);
    }
    
    // Filter by type if specified
    if (type) {
      notifications = notifications.filter((n: HRNotification) => n.type === type);
    }
    
    // Filter unread only
    if (unreadOnly) {
      notifications = notifications.filter((n: HRNotification) => !n.read);
    }
    
    // Filter out expired notifications
    const now = new Date();
    notifications = notifications.filter((n: HRNotification) => {
      if (!n.expiresAt) return true;
      return new Date(n.expiresAt) > now;
    });
    
    // Sort by created date (most recent first)
    notifications.sort((a: HRNotification, b: HRNotification) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    // Apply limit
    if (limit) {
      const limitNum = parseInt(limit);
      notifications = notifications.slice(0, limitNum);
    }
    
    return NextResponse.json(notifications);
  } catch (error) {
    console.error('Error fetching HR notifications:', error);
    return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 });
  }
}

// POST /api/hr/notifications
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, type, title, message, data, tenantId = 'default', module, expiresAt } = body;
    
    if (!userId || !type || !title || !message || !module) {
      return NextResponse.json({ 
        error: 'Missing required fields: userId, type, title, message, module' 
      }, { status: 400 });
    }
    
    const notificationsData = readNotifications();
    
    const newNotification: HRNotification = {
      id: uuidv4(),
      userId,
      type,
      title,
      message,
      data,
      read: false,
      createdAt: new Date().toISOString(),
      expiresAt,
      tenantId,
      module
    };
    
    notificationsData.notifications.push(newNotification);
    writeNotifications(notificationsData);
    
    return NextResponse.json(newNotification, { status: 201 });
  } catch (error) {
    console.error('Error creating HR notification:', error);
    return NextResponse.json({ error: 'Failed to create notification' }, { status: 500 });
  }
}
