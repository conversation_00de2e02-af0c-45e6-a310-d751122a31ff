import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const PUSH_LOG_FILE = path.join(process.cwd(), 'data/apps/hr/push-notifications.json');

interface PushNotification {
  id: string;
  userId: string;
  notificationId: string;
  deviceToken?: string;
  title: string;
  body: string;
  data?: any;
  sentAt: string;
  status: 'sent' | 'failed' | 'pending';
  error?: string;
}

interface PushLogData {
  pushNotifications: PushNotification[];
}

function ensureDataFile(): void {
  const dataDir = path.dirname(PUSH_LOG_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  if (!fs.existsSync(PUSH_LOG_FILE)) {
    const initialData: PushLogData = { pushNotifications: [] };
    fs.writeFileSync(PUSH_LOG_FILE, JSON.stringify(initialData, null, 2));
  }
}

function readPushLog(): PushLogData {
  ensureDataFile();
  try {
    const data = fs.readFileSync(PUSH_LOG_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading push log file:', error);
    return { pushNotifications: [] };
  }
}

function writePushLog(data: PushLogData): void {
  try {
    fs.writeFileSync(PUSH_LOG_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing push log file:', error);
    throw error;
  }
}

// POST /api/hr/notifications/push
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, notification } = body;
    
    if (!userId || !notification) {
      return NextResponse.json({ 
        error: 'Missing required fields: userId, notification' 
      }, { status: 400 });
    }
    
    // In a real implementation, you would:
    // 1. Get user's device tokens from user service
    // 2. Send actual push notification using service like FCM, APNs, etc.
    // 3. Handle different device types (iOS, Android, Web)
    
    // For now, we'll simulate push notification sending and log it
    const pushLog = readPushLog();
    
    const pushNotification: PushNotification = {
      id: `push-${Date.now()}`,
      userId,
      notificationId: notification.id,
      deviceToken: `device-token-${userId}`, // In real app, get from user's registered devices
      title: notification.title,
      body: notification.message,
      data: notification.data,
      sentAt: new Date().toISOString(),
      status: 'sent' // In real implementation, this would be based on actual push service response
    };
    
    pushLog.pushNotifications.push(pushNotification);
    writePushLog(pushLog);
    
    console.log(`Push notification sent to user ${userId}: ${notification.title}`);
    
    return NextResponse.json({ 
      success: true,
      message: 'Push notification sent successfully',
      pushId: pushNotification.id
    });
  } catch (error) {
    console.error('Error sending push notification:', error);
    
    // Log failed push attempt
    try {
      const pushLog = readPushLog();
      const failedPush: PushNotification = {
        id: `push-failed-${Date.now()}`,
        userId: request.body?.userId || 'unknown',
        notificationId: request.body?.notification?.id || 'unknown',
        title: 'Failed to send',
        body: 'Failed to send push notification',
        sentAt: new Date().toISOString(),
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      pushLog.pushNotifications.push(failedPush);
      writePushLog(pushLog);
    } catch (logError) {
      console.error('Error logging failed push:', logError);
    }
    
    return NextResponse.json({ 
      success: false,
      error: 'Failed to send push notification' 
    }, { status: 500 });
  }
}

// GET /api/hr/notifications/push - Get push log
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    
    const pushLog = readPushLog();
    let pushNotifications = pushLog.pushNotifications;
    
    // Filter by user if specified
    if (userId) {
      pushNotifications = pushNotifications.filter(push => push.userId === userId);
    }
    
    // Filter by status if specified
    if (status) {
      pushNotifications = pushNotifications.filter(push => push.status === status);
    }
    
    // Sort by sent date (most recent first)
    pushNotifications.sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime());
    
    // Apply limit
    pushNotifications = pushNotifications.slice(0, limit);
    
    return NextResponse.json({
      pushNotifications,
      total: pushNotifications.length
    });
  } catch (error) {
    console.error('Error fetching push log:', error);
    return NextResponse.json({ error: 'Failed to fetch push log' }, { status: 500 });
  }
}
