import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const HR_DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'hr', 'headhunter');
const PROFILES_FILE = path.join(HR_DATA_DIR, 'headhunter_profiles.json');

interface HeadHunterProfile {
  id: string;
  userId: string;
  name: string;
  email: string;
  phone?: string;
  company: string;
  title: string;
  yearsOfExperience: number;
  location: string;
  avatar?: string;
  specializations: Array<{
    industry: string;
    subIndustries: string[];
    yearsExperience: number;
    placementCount: number;
  }>;
  focusAreas: {
    jobLevels: string[];
    jobTypes: string[];
    salaryRanges: Array<{
      currency: string;
      minSalary: number;
      maxSalary: number;
      period: string;
    }>;
    locations: string[];
    remoteWork: boolean;
    willingToRelocate: boolean;
  };
  skills: Array<{
    name: string;
    level: string;
    verified: boolean;
  }>;
  preferences: {
    preferredIndustries: string[];
    avoidIndustries: string[];
    preferredCompanySizes: string[];
    workingHours: string;
    communicationStyle: string;
    candidateTypes: string[];
    placementFeeRange: {
      min: number;
      max: number;
      unit: string;
    };
  };
  performance: {
    totalPlacements: number;
    successRate: number;
    averageTimeToHire: number;
    clientSatisfactionScore: number;
    candidateSatisfactionScore: number;
    totalEarnings: number;
    currentYear: {
      placements: number;
      earnings: number;
      successRate: number;
    };
  };
  certifications?: Array<{
    name: string;
    organization: string;
    year: number;
  }>;
  isActive: boolean;
  lastActiveAt: string;
  createdAt: string;
  updatedAt: string;
}

async function ensureDirectoryExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function readJsonFile<T>(filePath: string, defaultValue: T): Promise<T> {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch {
    await ensureDirectoryExists(path.dirname(filePath));
    await fs.writeFile(filePath, JSON.stringify(defaultValue, null, 2));
    return defaultValue;
  }
}

async function writeJsonFile<T>(filePath: string, data: T): Promise<void> {
  await ensureDirectoryExists(path.dirname(filePath));
  await fs.writeFile(filePath, JSON.stringify(data, null, 2));
}

// GET /api/hr/headhunter/profile - Get headhunter profile
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const headhunterId = searchParams.get('headhunterId');

    const profiles = await readJsonFile<HeadHunterProfile[]>(PROFILES_FILE, []);

    let profile: HeadHunterProfile | undefined;
    
    if (headhunterId) {
      profile = profiles.find(p => p.id === headhunterId);
    } else {
      // Find by current user ID
      profile = profiles.find(p => p.userId === session.user.id);
    }

    if (!profile) {
      return NextResponse.json({ 
        error: 'Headhunter profile not found',
        code: 'PROFILE_NOT_FOUND'
      }, { status: 404 });
    }

    // Update last active timestamp
    profile.lastActiveAt = new Date().toISOString();
    const profileIndex = profiles.findIndex(p => p.id === profile!.id);
    if (profileIndex !== -1) {
      profiles[profileIndex] = profile;
      await writeJsonFile(PROFILES_FILE, profiles);
    }

    return NextResponse.json(profile);

  } catch (error) {
    console.error('Error fetching headhunter profile:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// POST /api/hr/headhunter/profile - Create headhunter profile
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      name,
      email,
      phone,
      company,
      title,
      yearsOfExperience,
      location,
      specializations = [],
      focusAreas = {
        jobLevels: [],
        jobTypes: [],
        salaryRanges: [],
        locations: [],
        remoteWork: false,
        willingToRelocate: false
      },
      skills = [],
      preferences = {
        preferredIndustries: [],
        avoidIndustries: [],
        preferredCompanySizes: [],
        workingHours: 'standard',
        communicationStyle: 'professional',
        candidateTypes: ['active'],
        placementFeeRange: { min: 15, max: 25, unit: 'percentage' }
      }
    } = body;

    if (!name || !email || !company || !title) {
      return NextResponse.json({ 
        error: 'Missing required fields: name, email, company, title' 
      }, { status: 400 });
    }

    const profiles = await readJsonFile<HeadHunterProfile[]>(PROFILES_FILE, []);

    // Check if profile already exists for this user
    const existingProfile = profiles.find(p => p.userId === session.user.id);
    if (existingProfile) {
      return NextResponse.json({ 
        error: 'Headhunter profile already exists for this user' 
      }, { status: 409 });
    }

    // Create new profile
    const newProfile: HeadHunterProfile = {
      id: `hh_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      userId: session.user.id,
      name,
      email,
      phone,
      company,
      title,
      yearsOfExperience: yearsOfExperience || 0,
      location,
      specializations,
      focusAreas,
      skills,
      preferences,
      performance: {
        totalPlacements: 0,
        successRate: 0,
        averageTimeToHire: 0,
        clientSatisfactionScore: 0,
        candidateSatisfactionScore: 0,
        totalEarnings: 0,
        currentYear: {
          placements: 0,
          earnings: 0,
          successRate: 0
        }
      },
      isActive: true,
      lastActiveAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    profiles.push(newProfile);
    await writeJsonFile(PROFILES_FILE, profiles);

    return NextResponse.json(newProfile, { status: 201 });

  } catch (error) {
    console.error('Error creating headhunter profile:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// PUT /api/hr/headhunter/profile - Update headhunter profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { profileId, ...updateData } = body;

    const profiles = await readJsonFile<HeadHunterProfile[]>(PROFILES_FILE, []);

    let profileIndex = -1;
    
    if (profileId) {
      profileIndex = profiles.findIndex(p => p.id === profileId);
    } else {
      // Find by current user ID
      profileIndex = profiles.findIndex(p => p.userId === session.user.id);
    }

    if (profileIndex === -1) {
      return NextResponse.json({ 
        error: 'Headhunter profile not found' 
      }, { status: 404 });
    }

    // Check ownership
    if (profiles[profileIndex].userId !== session.user.id) {
      return NextResponse.json({ 
        error: 'Unauthorized to update this profile' 
      }, { status: 403 });
    }

    // Update profile
    const updatedProfile = {
      ...profiles[profileIndex],
      ...updateData,
      id: profiles[profileIndex].id, // Preserve ID
      userId: profiles[profileIndex].userId, // Preserve user ID
      createdAt: profiles[profileIndex].createdAt, // Preserve creation date
      updatedAt: new Date().toISOString(),
      lastActiveAt: new Date().toISOString()
    };

    profiles[profileIndex] = updatedProfile;
    await writeJsonFile(PROFILES_FILE, profiles);

    return NextResponse.json(updatedProfile);

  } catch (error) {
    console.error('Error updating headhunter profile:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// DELETE /api/hr/headhunter/profile - Delete headhunter profile
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const profileId = searchParams.get('profileId');

    const profiles = await readJsonFile<HeadHunterProfile[]>(PROFILES_FILE, []);

    let profileIndex = -1;
    
    if (profileId) {
      profileIndex = profiles.findIndex(p => p.id === profileId);
    } else {
      // Find by current user ID
      profileIndex = profiles.findIndex(p => p.userId === session.user.id);
    }

    if (profileIndex === -1) {
      return NextResponse.json({ 
        error: 'Headhunter profile not found' 
      }, { status: 404 });
    }

    // Check ownership
    if (profiles[profileIndex].userId !== session.user.id) {
      return NextResponse.json({ 
        error: 'Unauthorized to delete this profile' 
      }, { status: 403 });
    }

    // Mark as inactive instead of deleting
    profiles[profileIndex].isActive = false;
    profiles[profileIndex].updatedAt = new Date().toISOString();

    await writeJsonFile(PROFILES_FILE, profiles);

    return NextResponse.json({ 
      message: 'Profile deactivated successfully' 
    });

  } catch (error) {
    console.error('Error deleting headhunter profile:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}