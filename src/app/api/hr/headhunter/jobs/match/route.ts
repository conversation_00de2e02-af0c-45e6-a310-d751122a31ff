import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const HR_DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'hr', 'headhunter');
const JOBS_FILE = path.join(HR_DATA_DIR, 'jobs.json');
const PROFILES_FILE = path.join(HR_DATA_DIR, 'headhunter_profiles.json');

interface HeadHunterProfile {
  id: string;
  userId: string;
  name: string;
  specializations: Array<{
    industry: string;
    subIndustries: string[];
    yearsExperience: number;
    placementCount: number;
  }>;
  focusAreas: {
    jobLevels: string[];
    jobTypes: string[];
    salaryRanges: Array<{
      currency: string;
      minSalary: number;
      maxSalary: number;
      period: string;
    }>;
    locations: string[];
    remoteWork: boolean;
    willingToRelocate: boolean;
  };
  preferences: {
    preferredIndustries: string[];
    avoidIndustries: string[];
    preferredCompanySizes: string[];
    candidateTypes: string[];
    placementFeeRange: {
      min: number;
      max: number;
      unit: string;
    };
  };
  skills: Array<{
    name: string;
    level: string;
    verified: boolean;
  }>;
}

interface Job {
  id: string;
  title: string;
  company: {
    name: string;
    size: string;
    industry: string;
    location: string;
    logo?: string;
  };
  description: string;
  requirements: string[];
  salary: {
    currency: string;
    min: number;
    max: number;
    period: string;
    negotiable: boolean;
  };
  jobLevel: string;
  jobType: string;
  location: string;
  remoteWork: boolean;
  relocationAssistance: boolean;
  skills: Array<{
    name: string;
    level: string;
    required: boolean;
  }>;
  placementFee: {
    percentage: number;
    currency: string;
    guaranteePeriod: number;
  };
  urgency: string;
  status: string;
  tags: string[];
  postedBy: string;
  postedAt: string;
}

interface JobMatch {
  job: Job;
  matchScore: number;
  matchReasons: string[];
  concerns: string[];
  recommendationLevel: 'high' | 'medium' | 'low';
}

async function ensureDirectoryExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function readJsonFile<T>(filePath: string, defaultValue: T): Promise<T> {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch {
    await ensureDirectoryExists(path.dirname(filePath));
    await fs.writeFile(filePath, JSON.stringify(defaultValue, null, 2));
    return defaultValue;
  }
}

function calculateJobMatch(headhunter: HeadHunterProfile, job: Job): JobMatch {
  let score = 0;
  const maxScore = 100;
  const matchReasons: string[] = [];
  const concerns: string[] = [];

  // Industry match (25 points)
  const industryMatch = headhunter.specializations.some(spec => 
    spec.industry.toLowerCase() === job.company.industry.toLowerCase()
  );
  if (industryMatch) {
    score += 25;
    matchReasons.push(`Perfect industry match: ${job.company.industry}`);
  } else if (headhunter.preferences.preferredIndustries.some(ind => 
    ind.toLowerCase() === job.company.industry.toLowerCase()
  )) {
    score += 15;
    matchReasons.push(`Preferred industry: ${job.company.industry}`);
  } else if (headhunter.preferences.avoidIndustries.some(ind => 
    ind.toLowerCase() === job.company.industry.toLowerCase()
  )) {
    score -= 20;
    concerns.push(`Industry in avoid list: ${job.company.industry}`);
  }

  // Job level match (20 points)
  if (headhunter.focusAreas.jobLevels.includes(job.jobLevel)) {
    score += 20;
    matchReasons.push(`Job level match: ${job.jobLevel}`);
  } else {
    score -= 10;
    concerns.push(`Job level not in focus: ${job.jobLevel}`);
  }

  // Job type match (15 points)
  if (headhunter.focusAreas.jobTypes.includes(job.jobType)) {
    score += 15;
    matchReasons.push(`Job type match: ${job.jobType}`);
  } else {
    score -= 5;
    concerns.push(`Job type not preferred: ${job.jobType}`);
  }

  // Location match (15 points)
  const locationMatch = headhunter.focusAreas.locations.some(loc => 
    loc.toLowerCase().includes(job.location.toLowerCase()) ||
    job.location.toLowerCase().includes(loc.toLowerCase())
  );
  if (locationMatch) {
    score += 15;
    matchReasons.push(`Location match: ${job.location}`);
  } else if (job.remoteWork && headhunter.focusAreas.remoteWork) {
    score += 10;
    matchReasons.push(`Remote work available`);
  } else {
    score -= 8;
    concerns.push(`Location not in focus area: ${job.location}`);
  }

  // Salary range match (15 points)
  const salaryMatch = headhunter.focusAreas.salaryRanges.some(range => {
    if (range.currency === job.salary.currency) {
      const jobMidPoint = (job.salary.min + job.salary.max) / 2;
      return jobMidPoint >= range.minSalary && jobMidPoint <= range.maxSalary;
    }
    return false;
  });
  if (salaryMatch) {
    score += 15;
    matchReasons.push(`Salary in target range: ${job.salary.currency} ${job.salary.min}-${job.salary.max}/${job.salary.period}`);
  } else {
    score -= 5;
    concerns.push(`Salary outside target range`);
  }

  // Company size match (10 points)
  if (headhunter.preferences.preferredCompanySizes.includes(job.company.size)) {
    score += 10;
    matchReasons.push(`Preferred company size: ${job.company.size}`);
  }

  // Placement fee attractiveness (10 points)
  const feeInRange = job.placementFee.percentage >= headhunter.preferences.placementFeeRange.min &&
                    job.placementFee.percentage <= headhunter.preferences.placementFeeRange.max;
  if (feeInRange) {
    score += 10;
    matchReasons.push(`Placement fee in range: ${job.placementFee.percentage}%`);
  } else if (job.placementFee.percentage > headhunter.preferences.placementFeeRange.max) {
    score += 15; // Higher fee is better for headhunter
    matchReasons.push(`High placement fee: ${job.placementFee.percentage}%`);
  } else {
    score -= 5;
    concerns.push(`Low placement fee: ${job.placementFee.percentage}%`);
  }

  // Urgency bonus (5 points)
  if (job.urgency === 'high') {
    score += 5;
    matchReasons.push(`Urgent hiring - quick placement opportunity`);
  }

  // Experience level alignment bonus (5 points)
  const relevantSpec = headhunter.specializations.find(spec => 
    spec.industry.toLowerCase() === job.company.industry.toLowerCase()
  );
  if (relevantSpec) {
    const experienceBonus = Math.min(relevantSpec.yearsExperience / 2, 5);
    score += experienceBonus;
    if (experienceBonus > 3) {
      matchReasons.push(`Strong industry experience: ${relevantSpec.yearsExperience} years`);
    }
  }

  // Normalize score to 0-100
  score = Math.max(0, Math.min(maxScore, score));

  // Determine recommendation level
  let recommendationLevel: 'high' | 'medium' | 'low' = 'low';
  if (score >= 75) {
    recommendationLevel = 'high';
  } else if (score >= 50) {
    recommendationLevel = 'medium';
  }

  return {
    job,
    matchScore: Math.round(score),
    matchReasons,
    concerns,
    recommendationLevel
  };
}

// GET /api/hr/headhunter/jobs/match - Get jobs matching headhunter profile
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const headhunterId = searchParams.get('headhunterId');
    const minScore = parseInt(searchParams.get('minScore') || '0');
    const limit = parseInt(searchParams.get('limit') || '20');
    const recommendationLevel = searchParams.get('recommendationLevel'); // 'high', 'medium', 'low'

    // Read data files
    const [jobs, profiles] = await Promise.all([
      readJsonFile<Job[]>(JOBS_FILE, []),
      readJsonFile<HeadHunterProfile[]>(PROFILES_FILE, [])
    ]);

    // Find headhunter profile
    let headhunter: HeadHunterProfile | undefined;
    
    if (headhunterId) {
      headhunter = profiles.find(p => p.id === headhunterId);
    } else {
      // Find by current user ID
      headhunter = profiles.find(p => p.userId === session.user.id);
    }

    if (!headhunter) {
      return NextResponse.json({ 
        error: 'Headhunter profile not found. Please complete your profile first.',
        code: 'PROFILE_NOT_FOUND'
      }, { status: 404 });
    }

    // Filter active jobs
    const activeJobs = jobs.filter(job => job.status === 'active');

    // Calculate matches for all jobs
    const jobMatches = activeJobs
      .map(job => calculateJobMatch(headhunter!, job))
      .filter(match => match.matchScore >= minScore)
      .filter(match => !recommendationLevel || match.recommendationLevel === recommendationLevel)
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, limit);

    // Add additional metadata
    const response = {
      headhunter: {
        id: headhunter.id,
        name: headhunter.name,
        specializations: headhunter.specializations,
        focusAreas: headhunter.focusAreas
      },
      matches: jobMatches,
      stats: {
        totalJobs: activeJobs.length,
        matchedJobs: jobMatches.length,
        highMatches: jobMatches.filter(m => m.recommendationLevel === 'high').length,
        mediumMatches: jobMatches.filter(m => m.recommendationLevel === 'medium').length,
        lowMatches: jobMatches.filter(m => m.recommendationLevel === 'low').length,
        averageMatchScore: jobMatches.length > 0 
          ? Math.round(jobMatches.reduce((sum, m) => sum + m.matchScore, 0) / jobMatches.length)
          : 0
      },
      filters: {
        minScore,
        recommendationLevel,
        limit
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error matching jobs:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// POST /api/hr/headhunter/jobs/match/feedback - Provide feedback on job matches
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { jobId, feedback, rating, notes } = body;

    if (!jobId || !feedback) {
      return NextResponse.json({ 
        error: 'Missing required fields: jobId, feedback' 
      }, { status: 400 });
    }

    // Here you would store the feedback to improve future matching
    // For now, we'll just log it and return success
    console.log('Job match feedback received:', {
      userId: session.user.id,
      jobId,
      feedback, // 'relevant', 'not-relevant', 'interested', 'not-interested'
      rating, // 1-5 scale
      notes,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      message: 'Feedback received successfully',
      status: 'recorded'
    });

  } catch (error) {
    console.error('Error recording feedback:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}