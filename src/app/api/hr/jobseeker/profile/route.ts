import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const HR_DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'hr');
const PROFILES_FILE = path.join(HR_DATA_DIR, 'recruitment', 'jobseeker_profiles.json');
const EXPERIENCES_FILE = path.join(HR_DATA_DIR, 'recruitment', 'work_experiences.json');
const EDUCATIONS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'educations.json');

interface JobSeekerProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  website?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  headline?: string;
  summary?: string;
  preferredSalary?: number;
  salaryType?: string;
  availableFrom?: string;
  workAuthorization?: string;
  remoteWork: boolean;
  willingToRelocate: boolean;
  resume?: string;
  profilePicture?: string;
  isPublic: boolean;
  isActiveJobSeeker: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

interface WorkExperience {
  id: string;
  jobSeekerProfileId: string;
  title: string;
  company: string;
  location?: string;
  description?: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
  industry?: string;
  employmentType?: string;
  createdAt: string;
  updatedAt: string;
}

interface Education {
  id: string;
  jobSeekerProfileId: string;
  institution: string;
  degree: string;
  fieldOfStudy?: string;
  grade?: string;
  startDate?: string;
  endDate?: string;
  isCurrent: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

async function ensureDirectoryExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function readJsonFile<T>(filePath: string, defaultValue: T): Promise<T> {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch {
    await ensureDirectoryExists(path.dirname(filePath));
    await fs.writeFile(filePath, JSON.stringify(defaultValue, null, 2));
    return defaultValue;
  }
}

async function writeJsonFile<T>(filePath: string, data: T): Promise<void> {
  await ensureDirectoryExists(path.dirname(filePath));
  await fs.writeFile(filePath, JSON.stringify(data, null, 2));
}

// GET /api/hr/jobseeker/profile - Get job seeker profile
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Read data
    const [profiles, experiences, educations] = await Promise.all([
      readJsonFile<JobSeekerProfile[]>(PROFILES_FILE, []),
      readJsonFile<WorkExperience[]>(EXPERIENCES_FILE, []),
      readJsonFile<Education[]>(EDUCATIONS_FILE, [])
    ]);

    // Find user's profile
    const profile = profiles.find(p => 
      p.userId === session.user.id && p.tenantId === tenantId
    );

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Get related experiences and educations
    const userExperiences = experiences.filter(exp => exp.jobSeekerProfileId === profile.id);
    const userEducations = educations.filter(edu => edu.jobSeekerProfileId === profile.id);

    // Sort by date
    userExperiences.sort((a, b) => {
      if (a.isCurrent && !b.isCurrent) return -1;
      if (!a.isCurrent && b.isCurrent) return 1;
      return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
    });

    userEducations.sort((a, b) => {
      if (a.isCurrent && !b.isCurrent) return -1;
      if (!a.isCurrent && b.isCurrent) return 1;
      const aEnd = a.endDate || a.startDate;
      const bEnd = b.endDate || b.startDate;
      return new Date(bEnd).getTime() - new Date(aEnd).getTime();
    });

    return NextResponse.json({
      profile,
      experiences: userExperiences,
      educations: userEducations
    });

  } catch (error) {
    console.error('Error fetching profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/hr/jobseeker/profile - Create job seeker profile
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      firstName,
      lastName,
      phone,
      location,
      website,
      linkedinUrl,
      githubUrl,
      portfolioUrl,
      headline,
      summary,
      preferredSalary,
      salaryType,
      availableFrom,
      workAuthorization,
      remoteWork,
      willingToRelocate,
      tenantId
    } = body;

    if (!firstName || !lastName || !tenantId) {
      return NextResponse.json({ 
        error: 'Missing required fields: firstName, lastName, tenantId' 
      }, { status: 400 });
    }

    const profiles = await readJsonFile<JobSeekerProfile[]>(PROFILES_FILE, []);

    // Check if profile already exists
    const existingProfile = profiles.find(p => 
      p.userId === session.user.id && p.tenantId === tenantId
    );

    if (existingProfile) {
      return NextResponse.json({ 
        error: 'Profile already exists for this user' 
      }, { status: 409 });
    }

    // Create new profile
    const newProfile: JobSeekerProfile = {
      id: `profile_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      userId: session.user.id,
      firstName,
      lastName,
      email: session.user.email || '',
      phone,
      location,
      website,
      linkedinUrl,
      githubUrl,
      portfolioUrl,
      headline,
      summary,
      preferredSalary: preferredSalary ? parseFloat(preferredSalary) : undefined,
      salaryType,
      availableFrom,
      workAuthorization,
      remoteWork: Boolean(remoteWork),
      willingToRelocate: Boolean(willingToRelocate),
      isPublic: true,
      isActiveJobSeeker: true,
      tenantId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    profiles.push(newProfile);
    await writeJsonFile(PROFILES_FILE, profiles);

    return NextResponse.json(newProfile, { status: 201 });

  } catch (error) {
    console.error('Error creating profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/hr/jobseeker/profile - Update job seeker profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { tenantId, ...updateData } = body;

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    const profiles = await readJsonFile<JobSeekerProfile[]>(PROFILES_FILE, []);

    const profileIndex = profiles.findIndex(p => 
      p.userId === session.user.id && p.tenantId === tenantId
    );

    if (profileIndex === -1) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Update profile
    const updatedProfile = {
      ...profiles[profileIndex],
      ...updateData,
      userId: profiles[profileIndex].userId, // Preserve original user ID
      tenantId: profiles[profileIndex].tenantId, // Preserve original tenant ID
      createdAt: profiles[profileIndex].createdAt, // Preserve creation date
      updatedAt: new Date().toISOString(),
      preferredSalary: updateData.preferredSalary ? parseFloat(updateData.preferredSalary) : profiles[profileIndex].preferredSalary
    };

    profiles[profileIndex] = updatedProfile;
    await writeJsonFile(PROFILES_FILE, profiles);

    return NextResponse.json(updatedProfile);

  } catch (error) {
    console.error('Error updating profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}