import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const HR_DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'hr');
const APPLICATIONS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'applications.json');
const JOBS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'jobs.json');
const PROFILES_FILE = path.join(HR_DATA_DIR, 'recruitment', 'jobseeker_profiles.json');

interface JobApplication {
  id: string;
  jobPostingId: string;
  jobSeekerProfileId: string;
  coverLetter?: string;
  resume?: string;
  status: string;
  appliedAt: string;
  lastUpdated: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewerNotes?: string;
  salaryExpectation?: number;
  availableFrom?: string;
  tenantId: string;
}

interface JobSeekerProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  headline?: string;
  summary?: string;
  preferredSalary?: number;
  salaryType?: string;
  availableFrom?: string;
  workAuthorization?: string;
  remoteWork: boolean;
  willingToRelocate: boolean;
  resume?: string;
  profilePicture?: string;
  isPublic: boolean;
  isActiveJobSeeker: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

async function ensureDirectoryExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function readJsonFile<T>(filePath: string, defaultValue: T): Promise<T> {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch {
    await ensureDirectoryExists(path.dirname(filePath));
    await fs.writeFile(filePath, JSON.stringify(defaultValue, null, 2));
    return defaultValue;
  }
}

async function writeJsonFile<T>(filePath: string, data: T): Promise<void> {
  await ensureDirectoryExists(path.dirname(filePath));
  await fs.writeFile(filePath, JSON.stringify(data, null, 2));
}

// POST /api/hr/jobseeker/apply - Submit job application
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      jobPostingId,
      coverLetter,
      salaryExpectation,
      availableFrom,
      tenantId
    } = body;

    if (!jobPostingId || !tenantId) {
      return NextResponse.json({ 
        error: 'Missing required fields: jobPostingId, tenantId' 
      }, { status: 400 });
    }

    // Read existing data
    const [applications, jobs, profiles] = await Promise.all([
      readJsonFile<JobApplication[]>(APPLICATIONS_FILE, []),
      readJsonFile<any[]>(JOBS_FILE, []),
      readJsonFile<JobSeekerProfile[]>(PROFILES_FILE, [])
    ]);

    // Find the job posting
    const jobPosting = jobs.find(job => job.id === jobPostingId && job.tenantId === tenantId);
    if (!jobPosting) {
      return NextResponse.json({ 
        error: 'Job posting not found' 
      }, { status: 404 });
    }

    // Check if job is still accepting applications
    if (jobPosting.status !== 'active') {
      return NextResponse.json({ 
        error: 'This job is no longer accepting applications' 
      }, { status: 400 });
    }

    // Check application deadline
    if (jobPosting.applicationDeadline) {
      const deadline = new Date(jobPosting.applicationDeadline);
      if (new Date() > deadline) {
        return NextResponse.json({ 
          error: 'Application deadline has passed' 
        }, { status: 400 });
      }
    }

    // Find job seeker profile
    const jobSeekerProfile = profiles.find(profile => 
      profile.userId === session.user.id && profile.tenantId === tenantId
    );
    if (!jobSeekerProfile) {
      return NextResponse.json({ 
        error: 'Job seeker profile not found. Please complete your profile first.' 
      }, { status: 404 });
    }

    // Check if already applied
    const existingApplication = applications.find(app => 
      app.jobPostingId === jobPostingId && 
      app.jobSeekerProfileId === jobSeekerProfile.id
    );
    if (existingApplication) {
      return NextResponse.json({ 
        error: 'You have already applied to this job' 
      }, { status: 409 });
    }

    // Create new application
    const newApplication: JobApplication = {
      id: `app_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      jobPostingId,
      jobSeekerProfileId: jobSeekerProfile.id,
      coverLetter,
      resume: jobSeekerProfile.resume, // Use profile resume by default
      status: 'applied',
      appliedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      salaryExpectation: salaryExpectation ? parseFloat(salaryExpectation) : undefined,
      availableFrom,
      tenantId
    };

    // Add to applications
    applications.push(newApplication);

    // Update job posting application count
    const jobIndex = jobs.findIndex(job => job.id === jobPostingId);
    if (jobIndex !== -1) {
      jobs[jobIndex].applicationCount = (jobs[jobIndex].applicationCount || 0) + 1;
      jobs[jobIndex].updatedAt = new Date().toISOString();
    }

    // Save data
    await Promise.all([
      writeJsonFile(APPLICATIONS_FILE, applications),
      writeJsonFile(JOBS_FILE, jobs)
    ]);

    // Send notification email (would be implemented with actual email service)
    console.log(`New application received for job: ${jobPosting.title}`);

    return NextResponse.json({
      application: newApplication,
      message: 'Application submitted successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error submitting application:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET /api/hr/jobseeker/apply - Get user's applications
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const status = searchParams.get('status');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Read data
    const [applications, jobs, profiles, companies] = await Promise.all([
      readJsonFile<JobApplication[]>(APPLICATIONS_FILE, []),
      readJsonFile<any[]>(JOBS_FILE, []),
      readJsonFile<JobSeekerProfile[]>(PROFILES_FILE, []),
      readJsonFile<any[]>(path.join(HR_DATA_DIR, 'recruitment', 'companies.json'), [])
    ]);

    // Find user's profile
    const userProfile = profiles.find(profile => 
      profile.userId === session.user.id && profile.tenantId === tenantId
    );

    if (!userProfile) {
      return NextResponse.json({ applications: [] });
    }

    // Filter applications for this user
    let userApplications = applications.filter(app => 
      app.jobSeekerProfileId === userProfile.id && app.tenantId === tenantId
    );

    // Filter by status if provided
    if (status && status !== 'all') {
      userApplications = userApplications.filter(app => app.status === status);
    }

    // Enrich with job and company details
    const enrichedApplications = userApplications.map(app => {
      const job = jobs.find(j => j.id === app.jobPostingId);
      const company = job ? companies.find(c => c.id === job.companyId) : null;

      return {
        ...app,
        job: job ? {
          title: job.title,
          department: job.department,
          location: job.location,
          employmentType: job.employmentType,
          experienceLevel: job.experienceLevel,
          company: company ? { name: company.name, logo: company.logo } : null
        } : null
      };
    });

    // Sort by application date (newest first)
    enrichedApplications.sort((a, b) => 
      new Date(b.appliedAt).getTime() - new Date(a.appliedAt).getTime()
    );

    return NextResponse.json({
      applications: enrichedApplications,
      total: enrichedApplications.length
    });

  } catch (error) {
    console.error('Error fetching applications:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}