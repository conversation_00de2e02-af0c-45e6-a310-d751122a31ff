import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const HR_DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'hr');
const APPLICATIONS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'applications.json');
const JOBS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'jobs.json');
const PROFILES_FILE = path.join(HR_DATA_DIR, 'recruitment', 'jobseeker_profiles.json');
const COMPANIES_FILE = path.join(HR_DATA_DIR, 'recruitment', 'companies.json');

interface JobApplication {
  id: string;
  jobPostingId: string;
  jobSeekerProfileId: string;
  coverLetter?: string;
  resume?: string;
  status: string;
  appliedAt: string;
  lastUpdated: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewerNotes?: string;
  salaryExpectation?: number;
  availableFrom?: string;
  tenantId: string;
}

async function ensureDirectoryExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function readJsonFile<T>(filePath: string, defaultValue: T): Promise<T> {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch {
    await ensureDirectoryExists(path.dirname(filePath));
    await fs.writeFile(filePath, JSON.stringify(defaultValue, null, 2));
    return defaultValue;
  }
}

async function writeJsonFile<T>(filePath: string, data: T): Promise<void> {
  await ensureDirectoryExists(path.dirname(filePath));
  await fs.writeFile(filePath, JSON.stringify(data, null, 2));
}

// GET /api/hr/recruitment/applications - Get applications for jobs
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const jobPostingId = searchParams.get('jobPostingId');
    const tenantId = searchParams.get('tenantId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Read data
    const [applications, jobs, profiles, companies] = await Promise.all([
      readJsonFile<JobApplication[]>(APPLICATIONS_FILE, []),
      readJsonFile<any[]>(JOBS_FILE, []),
      readJsonFile<any[]>(PROFILES_FILE, []),
      readJsonFile<any[]>(COMPANIES_FILE, [])
    ]);

    // Filter applications
    let filteredApplications = applications.filter(app => app.tenantId === tenantId);

    if (jobPostingId) {
      filteredApplications = filteredApplications.filter(app => app.jobPostingId === jobPostingId);
    }

    if (status && status !== 'all') {
      filteredApplications = filteredApplications.filter(app => app.status === status);
    }

    // Check if user has access to these applications (company member or job poster)
    if (jobPostingId) {
      const job = jobs.find(j => j.id === jobPostingId && j.tenantId === tenantId);
      if (!job) {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 });
      }
      
      // For now, allow if user posted the job or is in the same tenant
      // In production, you'd check company membership here
    }

    // Sort by application date (newest first)
    filteredApplications.sort((a, b) => 
      new Date(b.appliedAt).getTime() - new Date(a.appliedAt).getTime()
    );

    // Paginate
    const total = filteredApplications.length;
    const startIndex = (page - 1) * limit;
    const paginatedApplications = filteredApplications.slice(startIndex, startIndex + limit);

    // Enrich with job seeker profile and job details
    const enrichedApplications = paginatedApplications.map(app => {
      const profile = profiles.find(p => p.id === app.jobSeekerProfileId);
      const job = jobs.find(j => j.id === app.jobPostingId);
      const company = job ? companies.find(c => c.id === job.companyId) : null;

      return {
        ...app,
        jobSeekerProfile: profile || null,
        job: job ? {
          title: job.title,
          department: job.department,
          location: job.location,
          employmentType: job.employmentType,
          company: company ? { name: company.name, logo: company.logo } : null
        } : null
      };
    });

    return NextResponse.json({
      applications: enrichedApplications,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching applications:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/hr/recruitment/applications - Update application status or add notes
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      applicationId,
      status,
      reviewerNotes,
      tenantId
    } = body;

    if (!applicationId || !tenantId) {
      return NextResponse.json({ 
        error: 'Missing required fields: applicationId, tenantId' 
      }, { status: 400 });
    }

    const applications = await readJsonFile<JobApplication[]>(APPLICATIONS_FILE, []);

    const applicationIndex = applications.findIndex(app => 
      app.id === applicationId && app.tenantId === tenantId
    );

    if (applicationIndex === -1) {
      return NextResponse.json({ error: 'Application not found' }, { status: 404 });
    }

    // Update application
    const updatedApplication = {
      ...applications[applicationIndex],
      ...(status && { status }),
      ...(reviewerNotes !== undefined && { reviewerNotes }),
      lastUpdated: new Date().toISOString(),
      reviewedAt: new Date().toISOString(),
      reviewedBy: session.user.name || session.user.email || 'HR Manager'
    };

    applications[applicationIndex] = updatedApplication;
    await writeJsonFile(APPLICATIONS_FILE, applications);

    // Send notification to candidate (would be implemented with actual email service)
    console.log(`Application ${applicationId} updated. Status: ${status || 'unchanged'}`);

    return NextResponse.json({
      application: updatedApplication,
      message: 'Application updated successfully'
    });

  } catch (error) {
    console.error('Error updating application:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/hr/recruitment/applications/bulk - Bulk update applications
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      applicationIds,
      action,
      status,
      reviewerNotes,
      tenantId
    } = body;

    if (!applicationIds || !Array.isArray(applicationIds) || !action || !tenantId) {
      return NextResponse.json({ 
        error: 'Missing required fields: applicationIds, action, tenantId' 
      }, { status: 400 });
    }

    const applications = await readJsonFile<JobApplication[]>(APPLICATIONS_FILE, []);

    let updatedCount = 0;
    const timestamp = new Date().toISOString();
    const reviewer = session.user.name || session.user.email || 'HR Manager';

    // Update applications
    for (let i = 0; i < applications.length; i++) {
      if (applicationIds.includes(applications[i].id) && applications[i].tenantId === tenantId) {
        if (action === 'update_status' && status) {
          applications[i].status = status;
        }
        if (reviewerNotes !== undefined) {
          applications[i].reviewerNotes = reviewerNotes;
        }
        applications[i].lastUpdated = timestamp;
        applications[i].reviewedAt = timestamp;
        applications[i].reviewedBy = reviewer;
        updatedCount++;
      }
    }

    await writeJsonFile(APPLICATIONS_FILE, applications);

    return NextResponse.json({
      message: `Successfully updated ${updatedCount} applications`,
      updatedCount
    });

  } catch (error) {
    console.error('Error bulk updating applications:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}