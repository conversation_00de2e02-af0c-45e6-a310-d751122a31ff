import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const HR_DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'hr');
const JOBS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'jobs.json');
const COMPANIES_FILE = path.join(HR_DATA_DIR, 'recruitment', 'companies.json');
const APPLICATIONS_FILE = path.join(HR_DATA_DIR, 'recruitment', 'applications.json');

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  responsibilities?: string;
  benefits?: string;
  salaryMin?: number;
  salaryMax?: number;
  salaryType?: string;
  location?: string;
  remoteWork: boolean;
  employmentType: string;
  experienceLevel: string;
  department?: string;
  companyId: string;
  postedById: string;
  applicationDeadline?: string;
  startDate?: string;
  isUrgent: boolean;
  status: string;
  tenantId: string;
  viewCount: number;
  applicationCount: number;
  skills: Array<{
    name: string;
    level: string;
    isRequired: boolean;
  }>;
  createdAt: string;
  updatedAt: string;
}

async function ensureDirectoryExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function readJsonFile<T>(filePath: string, defaultValue: T): Promise<T> {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch {
    await ensureDirectoryExists(path.dirname(filePath));
    await fs.writeFile(filePath, JSON.stringify(defaultValue, null, 2));
    return defaultValue;
  }
}

async function writeJsonFile<T>(filePath: string, data: T): Promise<void> {
  await ensureDirectoryExists(path.dirname(filePath));
  await fs.writeFile(filePath, JSON.stringify(data, null, 2));
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const status = searchParams.get('status');
    const department = searchParams.get('department');
    const employmentType = searchParams.get('employmentType');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    const jobs = await readJsonFile<JobPosting[]>(JOBS_FILE, []);
    const companies = await readJsonFile<any[]>(COMPANIES_FILE, []);
    const applications = await readJsonFile<any[]>(APPLICATIONS_FILE, []);

    // Filter jobs
    let filteredJobs = jobs.filter(job => job.tenantId === tenantId);

    if (status && status !== 'all') {
      filteredJobs = filteredJobs.filter(job => job.status === status);
    }

    if (department) {
      filteredJobs = filteredJobs.filter(job => job.department === department);
    }

    if (employmentType) {
      filteredJobs = filteredJobs.filter(job => job.employmentType === employmentType);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredJobs = filteredJobs.filter(job => 
        job.title.toLowerCase().includes(searchLower) ||
        job.department?.toLowerCase().includes(searchLower) ||
        job.description.toLowerCase().includes(searchLower)
      );
    }

    // Sort by creation date (newest first)
    filteredJobs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    const total = filteredJobs.length;
    const startIndex = (page - 1) * limit;
    const paginatedJobs = filteredJobs.slice(startIndex, startIndex + limit);

    // Enrich jobs with company and application data
    const enrichedJobs = paginatedJobs.map(job => {
      const company = companies.find(c => c.id === job.companyId);
      const jobApplications = applications.filter(app => app.jobPostingId === job.id);
      
      return {
        ...job,
        company: company ? { name: company.name, logo: company.logo } : null,
        applicationCount: jobApplications.length
      };
    });

    return NextResponse.json({
      jobs: enrichedJobs,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching jobs:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      title,
      description,
      requirements,
      responsibilities,
      benefits,
      salaryMin,
      salaryMax,
      salaryType,
      location,
      remoteWork,
      employmentType,
      experienceLevel,
      department,
      companyId,
      applicationDeadline,
      startDate,
      isUrgent,
      skills,
      tenantId,
      status = 'active'
    } = body;

    // Validate required fields
    if (!title || !description || !employmentType || !experienceLevel || !companyId || !tenantId) {
      return NextResponse.json({ 
        error: 'Missing required fields' 
      }, { status: 400 });
    }

    // Read existing data
    const jobs = await readJsonFile<JobPosting[]>(JOBS_FILE, []);
    const companies = await readJsonFile<any[]>(COMPANIES_FILE, []);

    // Check if company exists
    const company = companies.find(c => c.id === companyId && c.tenantId === tenantId);
    if (!company) {
      return NextResponse.json({ 
        error: 'Company not found' 
      }, { status: 404 });
    }

    // Create new job posting
    const newJob: JobPosting = {
      id: `job_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      title,
      description,
      requirements,
      responsibilities,
      benefits,
      salaryMin: salaryMin ? parseFloat(salaryMin) : undefined,
      salaryMax: salaryMax ? parseFloat(salaryMax) : undefined,
      salaryType,
      location,
      remoteWork: Boolean(remoteWork),
      employmentType,
      experienceLevel,
      department,
      companyId,
      postedById: session.user.id,
      applicationDeadline,
      startDate,
      isUrgent: Boolean(isUrgent),
      status,
      tenantId,
      viewCount: 0,
      applicationCount: 0,
      skills: skills || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add to jobs array
    jobs.push(newJob);

    // Save to file
    await writeJsonFile(JOBS_FILE, jobs);

    // Return complete job posting with company info
    const completeJobPosting = {
      ...newJob,
      company: { name: company.name, logo: company.logo }
    };

    return NextResponse.json(completeJobPosting, { status: 201 });
  } catch (error) {
    console.error('Error creating job posting:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}