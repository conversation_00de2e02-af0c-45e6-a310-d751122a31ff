'use client';

import React, { useState } from 'react';
import { TaskPriority, AIGenerationTaskType } from '../types';

export default function AIPhotoTaskSubmitter() {
  const [loading, setLoading] = useState(false);
  const [taskType, setTaskType] = useState<AIGenerationTaskType>(AIGenerationTaskType.TRAIN_MODEL);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Simulate task submission
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Task submitted successfully!');
    } catch (error) {
      console.error('Error submitting task:', error);
      alert('Error submitting task');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <h2 className="text-2xl font-bold">AI Photo Task Submitter</h2>
      
      <div className="bg-white p-6 rounded-lg border">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Task Type</label>
            <select
              value={taskType}
              onChange={(e) => setTaskType(e.target.value as AIGenerationTaskType)}
              className="w-full p-2 border rounded"
            >
              <option value={AIGenerationTaskType.TRAIN_MODEL}>Train AI Model</option>
              <option value={AIGenerationTaskType.GENERATE_PHOTOS}>Generate Photos</option>
              <option value={AIGenerationTaskType.PHOTO_TO_VIDEO}>Photo to Video</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Model Name</label>
            <input
              type="text"
              className="w-full p-2 border rounded"
              placeholder="Enter model name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Training Images</label>
            <input
              type="file"
              multiple
              accept="image/*"
              className="w-full p-2 border rounded"
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white p-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Submitting...' : 'Submit Task'}
          </button>
        </form>
      </div>
    </div>
  );
}
