"use client";

import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useOneIDAuth } from '../hooks/useOneIDAuth';
import { User } from '../../core/types';

interface OneIDAuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredUserTypes?: ('individual' | 'employee' | 'company_admin')[];
  requiredCompanyId?: string;
  fallbackUrl?: string;
  loadingComponent?: ReactNode;
  unauthorizedComponent?: ReactNode;
  onAuthStateChange?: (isAuthenticated: boolean, user: User | null) => void;
}

export function OneIDAuthGuard({
  children,
  requireAuth = true,
  requiredUserTypes,
  requiredCompanyId,
  fallbackUrl = '/backbone/oneid/auth/login',
  loadingComponent,
  unauthorizedComponent,
  onAuthStateChange,
}: OneIDAuthGuardProps) {
  const router = useRouter();
  const { user, isLoading, isAuthenticated } = useOneIDAuth();

  useEffect(() => {
    if (onAuthStateChange) {
      onAuthStateChange(isAuthenticated, user);
    }
  }, [isAuthenticated, user, onAuthStateChange]);

  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      const currentUrl = window.location.pathname + window.location.search;
      const loginUrl = `${fallbackUrl}?callbackUrl=${encodeURIComponent(currentUrl)}`;
      router.push(loginUrl);
    }
  }, [isLoading, requireAuth, isAuthenticated, fallbackUrl, router]);

  // Show loading component while checking authentication
  if (isLoading) {
    return (
      <>
        {loadingComponent || (
          <div className="flex min-h-screen items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Đang kiểm tra xác thực...</p>
            </div>
          </div>
        )}
      </>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  // If authentication is not required and user is not authenticated, show children
  if (!requireAuth && !isAuthenticated) {
    return <>{children}</>;
  }

  // Check user type requirements
  if (requiredUserTypes && user && !requiredUserTypes.includes(user.userType)) {
    return (
      <>
        {unauthorizedComponent || (
          <div className="flex min-h-screen items-center justify-center">
            <div className="text-center max-w-md mx-auto p-6">
              <div className="text-red-500 text-6xl mb-4">🚫</div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Không có quyền truy cập</h1>
              <p className="text-gray-600 mb-4">
                Tài khoản của bạn không có quyền truy cập vào trang này.
              </p>
              <p className="text-sm text-gray-500 mb-6">
                Loại tài khoản hiện tại: <strong>{user.userType}</strong><br />
                Yêu cầu: <strong>{requiredUserTypes.join(', ')}</strong>
              </p>
              <button
                onClick={() => router.back()}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Quay lại
              </button>
            </div>
          </div>
        )}
      </>
    );
  }

  // Check company ID requirements
  if (requiredCompanyId && user && user.companyId !== requiredCompanyId) {
    return (
      <>
        {unauthorizedComponent || (
          <div className="flex min-h-screen items-center justify-center">
            <div className="text-center max-w-md mx-auto p-6">
              <div className="text-red-500 text-6xl mb-4">🏢</div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Sai công ty</h1>
              <p className="text-gray-600 mb-4">
                Bạn không thuộc công ty được yêu cầu để truy cập trang này.
              </p>
              <p className="text-sm text-gray-500 mb-6">
                Công ty hiện tại: <strong>{user.companyId || 'Không có'}</strong><br />
                Yêu cầu: <strong>{requiredCompanyId}</strong>
              </p>
              <button
                onClick={() => router.back()}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Quay lại
              </button>
            </div>
          </div>
        )}
      </>
    );
  }

  // All checks passed, render children
  return <>{children}</>;
}

// Convenience wrapper for pages that require authentication
export function withOneIDAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<OneIDAuthGuardProps, 'children'>
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <OneIDAuthGuard {...options}>
        <Component {...props} />
      </OneIDAuthGuard>
    );
  };
}

// Hook for checking specific permissions
export function useOneIDPermissions() {
  const { user, isAuthenticated } = useOneIDAuth();

  const hasUserType = (userType: 'individual' | 'employee' | 'company_admin'): boolean => {
    return isAuthenticated && user?.userType === userType;
  };

  const belongsToCompany = (companyId: string): boolean => {
    return isAuthenticated && user?.companyId === companyId;
  };

  const isCompanyAdmin = (): boolean => {
    return hasUserType('company_admin');
  };

  const isEmployee = (): boolean => {
    return hasUserType('employee');
  };

  const isIndividual = (): boolean => {
    return hasUserType('individual');
  };

  const canAccessCompanyFeatures = (): boolean => {
    return isAuthenticated && (user?.userType === 'employee' || user?.userType === 'company_admin');
  };

  return {
    user,
    isAuthenticated,
    hasUserType,
    belongsToCompany,
    isCompanyAdmin,
    isEmployee,
    isIndividual,
    canAccessCompanyFeatures,
  };
}
