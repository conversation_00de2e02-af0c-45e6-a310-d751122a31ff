.oneid-auth-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.oneid-auth-layout .backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.oneid-auth-layout .backdrop-blur-md {
  backdrop-filter: blur(12px);
}

/* Custom scrollbar for auth pages */
.oneid-auth-layout ::-webkit-scrollbar {
  width: 6px;
}

.oneid-auth-layout ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.oneid-auth-layout ::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.oneid-auth-layout ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Animation for form elements */
.oneid-auth-layout input:focus {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.oneid-auth-layout button:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.oneid-auth-layout button:active {
  transform: translateY(0);
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.oneid-auth-layout .animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Fade in animation for auth forms */
.oneid-auth-layout > div {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .oneid-auth-layout .max-w-md {
    max-width: 95%;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .oneid-auth-layout .bg-white\/80 {
    background-color: white;
  }
  
  .oneid-auth-layout .text-gray-600 {
    color: #000;
  }
  
  .oneid-auth-layout .border-gray-300 {
    border-color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .oneid-auth-layout * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .oneid-auth-layout {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
}

/* Print styles */
@media print {
  .oneid-auth-layout {
    background: white !important;
  }
  
  .oneid-auth-layout .backdrop-blur-sm,
  .oneid-auth-layout .backdrop-blur-md {
    backdrop-filter: none !important;
  }
}
