"use client";

import { useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { OneIDService } from '../../services/OneIDService';
import {
  Lock as LockIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ArrowBack as ArrowBackIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  Refresh as RefreshIcon,
  Group as GroupIcon,
  Work as WorkIcon,
  AccountCircle as AccountCircleIcon,
} from '@mui/icons-material';

// Default OneID logo
const DEFAULT_LOGO = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/dace.logo.1.png';

function OneIDRegisterForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/';

  const [formData, setFormData] = useState({
    userType: 'individual' as 'individual' | 'employee' | 'company_admin',
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    confirmPassword: '',
    companyName: '',
    companyCode: '',
    agreeToTerms: false,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [registerError, setRegisterError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  const oneIDService = OneIDService.getInstance();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const validateForm = () => {
    if (!formData.username.trim()) {
      setRegisterError('Vui lòng nhập tên đăng nhập');
      return false;
    }
    
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      setRegisterError('Vui lòng nhập họ và tên');
      return false;
    }
    
    if (formData.password.length < 6) {
      setRegisterError('Mật khẩu phải có ít nhất 6 ký tự');
      return false;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setRegisterError('Mật khẩu xác nhận không khớp');
      return false;
    }
    
    if ((formData.userType === 'employee' || formData.userType === 'company_admin') && !formData.companyCode.trim()) {
      setRegisterError('Vui lòng nhập mã công ty');
      return false;
    }
    
    if (formData.userType === 'company_admin' && !formData.companyName.trim()) {
      setRegisterError('Vui lòng nhập tên công ty');
      return false;
    }
    
    if (!formData.agreeToTerms) {
      setRegisterError('Vui lòng đồng ý với điều khoản sử dụng');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setRegisterError('');
    
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);

    try {
      const result = await oneIDService.register({
        userType: formData.userType,
        username: formData.username,
        email: formData.email || undefined,
        firstName: formData.firstName,
        lastName: formData.lastName,
        password: formData.password,
        companyName: formData.companyName || undefined,
        companyCode: formData.companyCode || undefined,
      });

      if (!result.success) {
        setRegisterError(result.error || 'Đã xảy ra lỗi trong quá trình đăng ký');
        setIsLoading(false);
      } else {
        setRegistrationSuccess(true);
        setIsLoading(false);
        
        // Auto redirect after 3 seconds
        setTimeout(() => {
          router.push(`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`);
        }, 3000);
      }
    } catch (error) {
      console.error('[OneID REGISTER] Registration error:', error);
      setRegisterError('Đã xảy ra lỗi trong quá trình đăng ký');
      setIsLoading(false);
    }
  };

  if (registrationSuccess) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Đăng ký thành công!
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Tài khoản của bạn đã được tạo thành công. Đang chuyển hướng đến trang đăng nhập...
              </p>
            </div>
            <div className="mt-4">
              <Link 
                href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`}
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Đăng nhập ngay
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
        <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
          <div>
            <div className="flex justify-center">
              <Image 
                src={DEFAULT_LOGO} 
                alt="OneID Logo" 
                width={120} 
                height={120}
                priority
              />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Tạo tài khoản OneID
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Đăng ký tài khoản mới trong hệ thống ABN
            </p>
          </div>
          
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {/* User Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Loại tài khoản
              </label>
              <div className="grid grid-cols-1 gap-2">
                <label className="flex items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="userType"
                    value="individual"
                    checked={formData.userType === 'individual'}
                    onChange={handleInputChange}
                    className="mr-3"
                  />
                  <AccountCircleIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <div className="font-medium">Cá nhân</div>
                    <div className="text-xs text-gray-500">Tài khoản cá nhân</div>
                  </div>
                </label>
                <label className="flex items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="userType"
                    value="employee"
                    checked={formData.userType === 'employee'}
                    onChange={handleInputChange}
                    className="mr-3"
                  />
                  <WorkIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <div className="font-medium">Nhân viên</div>
                    <div className="text-xs text-gray-500">Nhân viên công ty</div>
                  </div>
                </label>
                <label className="flex items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="userType"
                    value="company_admin"
                    checked={formData.userType === 'company_admin'}
                    onChange={handleInputChange}
                    className="mr-3"
                  />
                  <BusinessIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <div className="font-medium">Quản trị công ty</div>
                    <div className="text-xs text-gray-500">Tạo công ty mới</div>
                  </div>
                </label>
              </div>
            </div>

            {/* Basic Information */}
            <div className="space-y-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <PersonIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  name="username"
                  type="text"
                  required
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Tên đăng nhập"
                  value={formData.username}
                  onChange={handleInputChange}
                />
              </div>

              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <EmailIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  name="email"
                  type="email"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Email (tùy chọn)"
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <input
                  name="firstName"
                  type="text"
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Họ"
                  value={formData.firstName}
                  onChange={handleInputChange}
                />
                <input
                  name="lastName"
                  type="text"
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Tên"
                  value={formData.lastName}
                  onChange={handleInputChange}
                />
              </div>

              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Mật khẩu"
                  value={formData.password}
                  onChange={handleInputChange}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-600"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <VisibilityOffIcon className="h-5 w-5" /> : <VisibilityIcon className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  required
                  className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Xác nhận mật khẩu"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-600"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <VisibilityOffIcon className="h-5 w-5" /> : <VisibilityIcon className="h-5 w-5" />}
                  </button>
                </div>
              </div>
            </div>

            {/* Company Information */}
            {(formData.userType === 'employee' || formData.userType === 'company_admin') && (
              <div className="space-y-4">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <BusinessIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    name="companyCode"
                    type="text"
                    required
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder={formData.userType === 'company_admin' ? "Mã công ty mới" : "Mã công ty"}
                    value={formData.companyCode}
                    onChange={handleInputChange}
                  />
                </div>

                {formData.userType === 'company_admin' && (
                  <input
                    name="companyName"
                    type="text"
                    required
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Tên công ty"
                    value={formData.companyName}
                    onChange={handleInputChange}
                  />
                )}
              </div>
            )}

            {registerError && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <ErrorIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{registerError}</h3>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center">
              <input
                id="agree-terms"
                name="agreeToTerms"
                type="checkbox"
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                checked={formData.agreeToTerms}
                onChange={handleInputChange}
              />
              <label htmlFor="agree-terms" className="ml-2 block text-sm text-gray-900">
                Tôi đồng ý với{' '}
                <Link href="/terms" className="text-indigo-600 hover:text-indigo-500">
                  điều khoản sử dụng
                </Link>
              </label>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <RefreshIcon className="animate-spin h-4 w-4 mr-2" />
                    ĐANG ĐĂNG KÝ...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <ArrowForwardIcon className="h-4 w-4 mr-2" />
                    ĐĂNG KÝ
                  </div>
                )}
              </button>
            </div>
            
            <div className="text-center">
              <Link href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`} className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center justify-center">
                <ArrowBackIcon className="h-4 w-4 mr-1" />
                Đã có tài khoản? Đăng nhập
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function OneIDRegisterPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">Đang tải...</div>}>
      <OneIDRegisterForm />
    </Suspense>
  );
}
