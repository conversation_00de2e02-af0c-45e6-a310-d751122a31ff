# OneID Authentication System

A comprehensive Single Sign-On (SSO) authentication system for ABN Green applications.

## Overview

OneID provides centralized authentication for all ABN Green applications with support for:
- **Multiple user types**: Individual, Employee, Company Admin
- **Multiple authentication methods**: Password, Magic Link, Password Reset
- **Single Sign-On (SSO)**: Login once, access all applications
- **Secure token management**: JWT-based sessions with automatic refresh
- **Company management**: Multi-tenant support with company hierarchies

## Features

### 🔐 Authentication Methods
- **Username/Password Login**: Traditional authentication
- **Magic Link**: Passwordless authentication via email
- **Password Reset**: Secure password recovery
- **Remember Me**: Extended session duration

### 👥 User Types
- **Individual**: Personal accounts for independent users
- **Employee**: Company employee accounts with restricted permissions
- **Company Admin**: Administrative accounts with company management rights

### 🏢 Company Support
- **Multi-tenant**: Support for multiple companies
- **Company Hierarchies**: Parent-child company relationships
- **Employee Management**: Company admins can manage employees
- **Company Registration**: Automatic company creation for admins

### 🛡️ Security Features
- **JWT Tokens**: Secure session management
- **Token Refresh**: Automatic session renewal
- **Cross-domain SSO**: Works across all ABN applications
- **Secure Cookies**: HttpOnly and SameSite cookie protection

## Quick Start

### 1. Basic Authentication Hook

```tsx
import { useOneIDAuth } from '@/app/backbone/oneid/auth/hooks/useOneIDAuth';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useOneIDAuth();

  const handleLogin = async () => {
    const success = await login('username', 'password');
    if (success) {
      console.log('Logged in successfully');
    }
  };

  if (!isAuthenticated) {
    return <button onClick={handleLogin}>Login</button>;
  }

  return (
    <div>
      <p>Welcome, {user?.firstName}!</p>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### 2. Authentication Guard

```tsx
import { OneIDAuthGuard } from '@/app/backbone/oneid/auth/components/OneIDAuthGuard';

function ProtectedPage() {
  return (
    <OneIDAuthGuard requireAuth={true} requiredUserTypes={['employee', 'company_admin']}>
      <div>This content is only visible to authenticated employees and admins</div>
    </OneIDAuthGuard>
  );
}
```

### 3. Higher-Order Component

```tsx
import { withOneIDAuth } from '@/app/backbone/oneid/auth/components/OneIDAuthGuard';

const ProtectedComponent = withOneIDAuth(MyComponent, {
  requireAuth: true,
  requiredUserTypes: ['company_admin']
});
```

## Authentication Pages

### Available Routes

- `/backbone/oneid/auth` - Main authentication hub
- `/backbone/oneid/auth/login` - Login page
- `/backbone/oneid/auth/register` - Registration page
- `/backbone/oneid/auth/forgot-password` - Password reset request
- `/backbone/oneid/auth/reset-password` - Password reset form
- `/backbone/oneid/auth/magic-link` - Magic link authentication
- `/backbone/oneid/auth/logout` - Logout page

### URL Parameters

All authentication pages support these URL parameters:
- `callbackUrl`: Redirect URL after successful authentication
- `error`: Error message to display
- `token`: Authentication token (for reset/magic link pages)

### Example Usage

```tsx
// Redirect to login with callback
const loginUrl = `/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent('/dashboard')}`;

// Direct registration
const registerUrl = `/backbone/oneid/auth/register?callbackUrl=${encodeURIComponent('/onboarding')}`;

// Magic link with callback
const magicUrl = `/backbone/oneid/auth/magic-link?callbackUrl=${encodeURIComponent('/app')}`;
```

## API Integration

### OneID Service

```tsx
import { OneIDService } from '@/app/backbone/oneid/services/OneIDService';

const oneID = OneIDService.getInstance();

// Initialize the service
await oneID.initialize();

// Authenticate user
const result = await oneID.authenticate('username', 'password');

// Register new user
const registerResult = await oneID.register({
  userType: 'individual',
  username: 'newuser',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  password: 'securepassword'
});

// Validate session
const sessionResult = await oneID.validateSession(token);

// Send magic link
const magicResult = await oneID.sendMagicLink('username', '/dashboard');
```

### Session Management

```tsx
// Check if user is authenticated
const token = localStorage.getItem('oneid_token');
if (token) {
  const { valid, user } = await oneID.validateSession(token);
  if (valid) {
    // User is authenticated
    console.log('User:', user);
  }
}

// Logout
await oneID.logout(token);
localStorage.removeItem('oneid_token');
localStorage.removeItem('oneid_user');
```

## User Types and Permissions

### Individual Users
- Personal accounts not associated with any company
- Full access to individual features
- Cannot access company-specific features

### Employee Users
- Associated with a specific company
- Managed by company administrators
- Access to company features based on permissions

### Company Admin Users
- Can create and manage companies
- Full administrative rights within their company
- Can manage employee accounts

## Integration Examples

### Protecting Routes

```tsx
// pages/admin/dashboard.tsx
import { OneIDAuthGuard } from '@/app/backbone/oneid/auth/components/OneIDAuthGuard';

export default function AdminDashboard() {
  return (
    <OneIDAuthGuard 
      requireAuth={true} 
      requiredUserTypes={['company_admin']}
      fallbackUrl="/backbone/oneid/auth/login"
    >
      <div>Admin Dashboard Content</div>
    </OneIDAuthGuard>
  );
}
```

### Custom Authentication Logic

```tsx
import { useOneIDPermissions } from '@/app/backbone/oneid/auth/components/OneIDAuthGuard';

function MyComponent() {
  const { isCompanyAdmin, canAccessCompanyFeatures, user } = useOneIDPermissions();

  if (isCompanyAdmin()) {
    return <AdminPanel />;
  }

  if (canAccessCompanyFeatures()) {
    return <EmployeePanel />;
  }

  return <IndividualPanel />;
}
```

### Handling Authentication State

```tsx
function App() {
  const { user, isLoading, isAuthenticated, error } = useOneIDAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage error={error} />;
  }

  return (
    <div>
      {isAuthenticated ? (
        <AuthenticatedApp user={user} />
      ) : (
        <UnauthenticatedApp />
      )}
    </div>
  );
}
```

## Security Considerations

### Token Storage
- Tokens are stored in localStorage for persistence
- Cookies are used for cross-domain SSO
- Tokens have expiration times and are automatically refreshed

### CSRF Protection
- SameSite cookie attributes prevent CSRF attacks
- Tokens are validated on every request

### Session Management
- Sessions are automatically invalidated on logout
- Concurrent session limits can be configured
- Session timeout is configurable per company

## Troubleshooting

### Common Issues

1. **Token not found**: Clear localStorage and cookies, then re-authenticate
2. **Invalid session**: Token may have expired, redirect to login
3. **Permission denied**: Check user type and company requirements
4. **Magic link not working**: Verify email configuration and token validity

### Debug Mode

Enable debug logging by setting localStorage:
```javascript
localStorage.setItem('oneid_debug', 'true');
```

This will log authentication events to the browser console.

## Contributing

When adding new authentication features:

1. Update the OneIDService with new methods
2. Add corresponding UI pages if needed
3. Update the authentication hook
4. Add proper TypeScript types
5. Update this documentation

## Support

For issues or questions about the OneID authentication system, please contact the ABN Green development team.
