"use client";

import { useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { OneIDService } from '../../services/OneIDService';
import {
  Email as EmailIcon,
  Person as PersonIcon,
  ArrowBack as ArrowBackIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  Refresh as RefreshIcon,
  Help as HelpIcon,
} from '@mui/icons-material';

// Default OneID logo
const DEFAULT_LOGO = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/dace.logo.1.png';

function OneIDForgotPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/';

  const [identifier, setIdentifier] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const oneIDService = OneIDService.getInstance();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!identifier.trim()) {
      setError('Vui lòng nhập tên đăng nhập hoặc email');
      return;
    }
    
    setIsLoading(true);

    try {
      const result = await oneIDService.requestPasswordReset(identifier);

      if (!result.success) {
        setError(result.error || 'Đã xảy ra lỗi trong quá trình gửi yêu cầu');
        setIsLoading(false);
      } else {
        setSuccess(true);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('[OneID FORGOT PASSWORD] Error:', error);
      setError('Đã xảy ra lỗi trong quá trình gửi yêu cầu');
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Yêu cầu đã được gửi!
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Chúng tôi đã gửi hướng dẫn đặt lại mật khẩu đến email của bạn (nếu có). 
                Vui lòng kiểm tra hộp thư và làm theo hướng dẫn.
              </p>
              <p className="mt-4 text-center text-xs text-gray-500">
                Nếu bạn không nhận được email trong vòng 5 phút, vui lòng kiểm tra thư mục spam 
                hoặc liên hệ với quản trị viên.
              </p>
            </div>
            <div className="mt-6 space-y-4">
              <Link 
                href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Quay lại đăng nhập
              </Link>
              <button
                onClick={() => {
                  setSuccess(false);
                  setIdentifier('');
                }}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Gửi lại yêu cầu
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
        <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
          <div>
            <div className="flex justify-center">
              <Image 
                src={DEFAULT_LOGO} 
                alt="OneID Logo" 
                width={120} 
                height={120}
                priority
              />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Quên mật khẩu?
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Nhập tên đăng nhập hoặc email để đặt lại mật khẩu
            </p>
          </div>
          
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="identifier" className="block text-sm font-medium text-gray-700 mb-2">
                Tên đăng nhập hoặc Email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <PersonIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="identifier"
                  name="identifier"
                  type="text"
                  required
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Nhập tên đăng nhập hoặc email"
                  value={identifier}
                  onChange={(e) => setIdentifier(e.target.value)}
                />
              </div>
            </div>

            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <ErrorIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{error}</h3>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <HelpIcon className="h-5 w-5 text-blue-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">Lưu ý</h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>Nếu tài khoản có email, chúng tôi sẽ gửi hướng dẫn đặt lại mật khẩu</li>
                      <li>Nếu tài khoản không có email, vui lòng liên hệ quản trị viên</li>
                      <li>Liên kết đặt lại mật khẩu có hiệu lực trong 1 giờ</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <RefreshIcon className="animate-spin h-4 w-4 mr-2" />
                    ĐANG GỬI YÊU CẦU...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <ArrowForwardIcon className="h-4 w-4 mr-2" />
                    GỬI YÊU CẦU ĐẶT LẠI
                  </div>
                )}
              </button>
            </div>
            
            <div className="text-center space-y-2">
              <Link 
                href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`} 
                className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center justify-center"
              >
                <ArrowBackIcon className="h-4 w-4 mr-1" />
                Quay lại đăng nhập
              </Link>
              <div className="text-xs text-gray-500">
                <Link href="/backbone/oneid/auth/register" className="hover:text-indigo-500">
                  Chưa có tài khoản? Đăng ký ngay
                </Link>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function OneIDForgotPasswordPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">Đang tải...</div>}>
      <OneIDForgotPasswordForm />
    </Suspense>
  );
}
