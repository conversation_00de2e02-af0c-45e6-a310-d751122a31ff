"use client";

import { useState, Suspense, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { OneIDService } from '../../services/OneIDService';
import {
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ArrowBack as ArrowBackIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

// Default OneID logo
const DEFAULT_LOGO = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/dace.logo.1.png';

function OneIDResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams?.get('token');
  const callbackUrl = searchParams?.get('callbackUrl') || '/';

  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [tokenValid, setTokenValid] = useState<boolean | null>(null);

  const oneIDService = OneIDService.getInstance();

  useEffect(() => {
    if (token) {
      // Verify token validity
      oneIDService.verifyResetToken(token)
        .then(result => {
          setTokenValid(result.success);
          if (!result.success) {
            setError(result.error || 'Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn');
          }
        })
        .catch(() => {
          setTokenValid(false);
          setError('Không thể xác minh liên kết đặt lại mật khẩu');
        });
    } else {
      setTokenValid(false);
      setError('Thiếu mã xác thực đặt lại mật khẩu');
    }
  }, [token]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (formData.password.length < 6) {
      setError('Mật khẩu phải có ít nhất 6 ký tự');
      return false;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setError('Mật khẩu xác nhận không khớp');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!validateForm()) {
      return;
    }
    
    if (!token) {
      setError('Thiếu mã xác thực đặt lại mật khẩu');
      return;
    }
    
    setIsLoading(true);

    try {
      const result = await oneIDService.resetPassword(token, formData.password);

      if (!result.success) {
        setError(result.error || 'Đã xảy ra lỗi trong quá trình đặt lại mật khẩu');
        setIsLoading(false);
      } else {
        setSuccess(true);
        setIsLoading(false);
        
        // Auto redirect after 3 seconds
        setTimeout(() => {
          router.push(`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`);
        }, 3000);
      }
    } catch (error) {
      console.error('[OneID RESET PASSWORD] Error:', error);
      setError('Đã xảy ra lỗi trong quá trình đặt lại mật khẩu');
      setIsLoading(false);
    }
  };

  if (tokenValid === null) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <RefreshIcon className="animate-spin mx-auto h-16 w-16 text-indigo-500" />
              <h2 className="mt-6 text-center text-2xl font-extrabold text-gray-900">
                Đang xác minh...
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Vui lòng chờ trong khi chúng tôi xác minh liên kết đặt lại mật khẩu
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (tokenValid === false) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <WarningIcon className="mx-auto h-16 w-16 text-red-500" />
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Liên kết không hợp lệ
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn.
              </p>
              {error && (
                <p className="mt-2 text-center text-sm text-red-600">
                  {error}
                </p>
              )}
            </div>
            <div className="mt-6 space-y-4">
              <Link 
                href="/backbone/oneid/auth/forgot-password"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Yêu cầu liên kết mới
              </Link>
              <Link 
                href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Quay lại đăng nhập
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Đặt lại mật khẩu thành công!
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Mật khẩu của bạn đã được đặt lại thành công. Đang chuyển hướng đến trang đăng nhập...
              </p>
            </div>
            <div className="mt-4">
              <Link 
                href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`}
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Đăng nhập ngay
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
        <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
          <div>
            <div className="flex justify-center">
              <Image 
                src={DEFAULT_LOGO} 
                alt="OneID Logo" 
                width={120} 
                height={120}
                priority
              />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Đặt lại mật khẩu
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Nhập mật khẩu mới cho tài khoản của bạn
            </p>
          </div>
          
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="relative">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Mật khẩu mới
                </label>
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" style={{ top: '28px' }}>
                  <LockIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Nhập mật khẩu mới"
                  value={formData.password}
                  onChange={handleInputChange}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center" style={{ top: '28px' }}>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-600"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <VisibilityOffIcon className="h-5 w-5" /> : <VisibilityIcon className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              <div className="relative">
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                  Xác nhận mật khẩu mới
                </label>
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" style={{ top: '28px' }}>
                  <LockIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  required
                  className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Nhập lại mật khẩu mới"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center" style={{ top: '28px' }}>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-600"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <VisibilityOffIcon className="h-5 w-5" /> : <VisibilityIcon className="h-5 w-5" />}
                  </button>
                </div>
              </div>
            </div>

            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <ErrorIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{error}</h3>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="text-sm text-blue-700">
                <strong>Yêu cầu mật khẩu:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Ít nhất 6 ký tự</li>
                  <li>Nên bao gồm chữ hoa, chữ thường và số</li>
                  <li>Tránh sử dụng thông tin cá nhân dễ đoán</li>
                </ul>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <RefreshIcon className="animate-spin h-4 w-4 mr-2" />
                    ĐANG ĐẶT LẠI...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <ArrowForwardIcon className="h-4 w-4 mr-2" />
                    ĐẶT LẠI MẬT KHẨU
                  </div>
                )}
              </button>
            </div>
            
            <div className="text-center">
              <Link 
                href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`} 
                className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center justify-center"
              >
                <ArrowBackIcon className="h-4 w-4 mr-1" />
                Quay lại đăng nhập
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function OneIDResetPasswordPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">Đang tải...</div>}>
      <OneIDResetPasswordForm />
    </Suspense>
  );
}
