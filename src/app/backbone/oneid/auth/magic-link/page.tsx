"use client";

import { useState, Suspense, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { OneIDService } from '../../services/OneIDService';
import {
  Email as EmailIcon,
  Person as PersonIcon,
  ArrowBack as ArrowBackIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  Refresh as RefreshIcon,
  AutoAwesome as MagicIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

// Default OneID logo
const DEFAULT_LOGO = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/dace.logo.1.png';

function OneIDMagicLinkForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/';
  const token = searchParams?.get('token');

  const [identifier, setIdentifier] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  const oneIDService = OneIDService.getInstance();

  // Handle magic link verification if token is present
  useEffect(() => {
    if (token) {
      setIsVerifying(true);
      oneIDService.verifyMagicLink(token)
        .then(result => {
          if (result.success && result.user && result.session) {
            // Store session token for SSO
            if (result.session.token) {
              localStorage.setItem('oneid_token', result.session.token);
              localStorage.setItem('oneid_user', JSON.stringify(result.user));
              
              // Set cookie for cross-domain SSO
              document.cookie = `oneid_token=${result.session.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
            }
            
            // Redirect to callback URL
            window.location.href = callbackUrl;
          } else {
            setError(result.error || 'Magic link không hợp lệ hoặc đã hết hạn');
            setIsVerifying(false);
          }
        })
        .catch(error => {
          console.error('[OneID MAGIC LINK] Verification error:', error);
          setError('Đã xảy ra lỗi trong quá trình xác minh magic link');
          setIsVerifying(false);
        });
    }
  }, [token, callbackUrl]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!identifier.trim()) {
      setError('Vui lòng nhập tên đăng nhập hoặc email');
      return;
    }
    
    setIsLoading(true);

    try {
      const result = await oneIDService.sendMagicLink(identifier, callbackUrl);

      if (!result.success) {
        setError(result.error || 'Đã xảy ra lỗi trong quá trình gửi magic link');
        setIsLoading(false);
      } else {
        setSuccess(true);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('[OneID MAGIC LINK] Send error:', error);
      setError('Đã xảy ra lỗi trong quá trình gửi magic link');
      setIsLoading(false);
    }
  };

  // Show verification screen if token is being processed
  if (isVerifying) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <RefreshIcon className="animate-spin mx-auto h-16 w-16 text-indigo-500" />
              <h2 className="mt-6 text-center text-2xl font-extrabold text-gray-900">
                Đang xác minh Magic Link...
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Vui lòng chờ trong khi chúng tôi xác minh và đăng nhập cho bạn
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show success screen after sending magic link
  if (success) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Magic Link đã được gửi!
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Chúng tôi đã gửi magic link đến email của bạn (nếu có). 
                Vui lòng kiểm tra hộp thư và nhấp vào liên kết để đăng nhập.
              </p>
              <p className="mt-4 text-center text-xs text-gray-500">
                Magic link có hiệu lực trong 15 phút. Nếu bạn không nhận được email, 
                vui lòng kiểm tra thư mục spam hoặc thử lại.
              </p>
            </div>
            <div className="mt-6 space-y-4">
              <button
                onClick={() => {
                  setSuccess(false);
                  setIdentifier('');
                }}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Gửi lại Magic Link
              </button>
              <Link 
                href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Đăng nhập bằng mật khẩu
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
        <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
          <div>
            <div className="flex justify-center">
              <Image 
                src={DEFAULT_LOGO} 
                alt="OneID Logo" 
                width={120} 
                height={120}
                priority
              />
            </div>
            <div className="flex justify-center mt-4">
              <MagicIcon className="h-8 w-8 text-indigo-600" />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Magic Link Đăng nhập
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Đăng nhập không cần mật khẩu với Magic Link
            </p>
          </div>
          
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="identifier" className="block text-sm font-medium text-gray-700 mb-2">
                Tên đăng nhập hoặc Email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <PersonIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="identifier"
                  name="identifier"
                  type="text"
                  required
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Nhập tên đăng nhập hoặc email"
                  value={identifier}
                  onChange={(e) => setIdentifier(e.target.value)}
                />
              </div>
            </div>

            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <ErrorIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{error}</h3>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <InfoIcon className="h-5 w-5 text-blue-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">Magic Link là gì?</h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>Đăng nhập an toàn không cần nhớ mật khẩu</li>
                      <li>Chúng tôi sẽ gửi liên kết đặc biệt đến email của bạn</li>
                      <li>Nhấp vào liên kết để đăng nhập tự động</li>
                      <li>Liên kết có hiệu lực trong 15 phút</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <RefreshIcon className="animate-spin h-4 w-4 mr-2" />
                    ĐANG GỬI MAGIC LINK...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <MagicIcon className="h-4 w-4 mr-2" />
                    GỬI MAGIC LINK
                  </div>
                )}
              </button>
            </div>
            
            <div className="text-center space-y-2">
              <Link 
                href={`/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`} 
                className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center justify-center"
              >
                <ArrowBackIcon className="h-4 w-4 mr-1" />
                Đăng nhập bằng mật khẩu
              </Link>
              <div className="text-xs text-gray-500">
                <Link href="/backbone/oneid/auth/register" className="hover:text-indigo-500">
                  Chưa có tài khoản? Đăng ký ngay
                </Link>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function OneIDMagicLinkPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">Đang tải...</div>}>
      <OneIDMagicLinkForm />
    </Suspense>
  );
}
