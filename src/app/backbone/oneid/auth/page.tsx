"use client";

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  Login as LoginIcon,
  PersonAdd as RegisterIcon,
  AutoAwesome as MagicIcon,
  Help as HelpIcon,
  Security as SecurityIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  ArrowForward as ArrowForwardIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

// Default OneID logo
const DEFAULT_LOGO = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/dace.logo.1.png';

function OneIDAuthIndex() {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/';

  const authOptions = [
    {
      title: 'Đăng nhập',
      description: 'Đăng nhập với tài khoản hiện có',
      icon: LoginIcon,
      href: `/backbone/oneid/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`,
      color: 'bg-blue-600 hover:bg-blue-700',
      primary: true,
    },
    {
      title: 'Đăng ký',
      description: 'Tạo tài khoản OneID mới',
      icon: RegisterIcon,
      href: `/backbone/oneid/auth/register?callbackUrl=${encodeURIComponent(callbackUrl)}`,
      color: 'bg-green-600 hover:bg-green-700',
      primary: false,
    },
    {
      title: 'Magic Link',
      description: 'Đăng nhập không cần mật khẩu',
      icon: MagicIcon,
      href: `/backbone/oneid/auth/magic-link?callbackUrl=${encodeURIComponent(callbackUrl)}`,
      color: 'bg-purple-600 hover:bg-purple-700',
      primary: false,
    },
    {
      title: 'Quên mật khẩu',
      description: 'Đặt lại mật khẩu của bạn',
      icon: HelpIcon,
      href: `/backbone/oneid/auth/forgot-password?callbackUrl=${encodeURIComponent(callbackUrl)}`,
      color: 'bg-orange-600 hover:bg-orange-700',
      primary: false,
    },
  ];

  const userTypes = [
    {
      type: 'individual',
      title: 'Cá nhân',
      description: 'Tài khoản cá nhân cho người dùng độc lập',
      icon: PersonIcon,
      features: ['Truy cập ứng dụng cá nhân', 'Quản lý hồ sơ cá nhân', 'Bảo mật cao'],
    },
    {
      type: 'employee',
      title: 'Nhân viên',
      description: 'Tài khoản nhân viên thuộc công ty',
      icon: WorkIcon,
      features: ['Truy cập ứng dụng công ty', 'Quản lý bởi admin', 'Tích hợp với hệ thống HR'],
    },
    {
      type: 'company_admin',
      title: 'Quản trị công ty',
      description: 'Tài khoản quản trị để tạo và quản lý công ty',
      icon: BusinessIcon,
      features: ['Tạo và quản lý công ty', 'Quản lý nhân viên', 'Cấu hình hệ thống'],
    },
  ];

  return (
    <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
        <div className="max-w-4xl w-full space-y-8">
          {/* Header */}
          <div className="text-center bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
            <div className="flex justify-center mb-6">
              <Image 
                src={DEFAULT_LOGO} 
                alt="OneID Logo" 
                width={120} 
                height={120}
                priority
              />
            </div>
            <h1 className="text-4xl font-extrabold text-gray-900 mb-4">
              Chào mừng đến với OneID
            </h1>
            <p className="text-xl text-gray-600 mb-6">
              Hệ thống xác thực tập trung cho tất cả ứng dụng ABN Green
            </p>
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
              <SecurityIcon className="h-5 w-5" />
              <span>Bảo mật cao • Đăng nhập một lần • Quản lý tập trung</span>
            </div>
          </div>

          {/* Authentication Options */}
          <div className="bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Chọn phương thức xác thực
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {authOptions.map((option, index) => (
                <Link
                  key={index}
                  href={option.href}
                  className={`
                    group relative p-6 rounded-lg border-2 border-transparent hover:border-gray-300 
                    transition-all duration-200 hover:shadow-lg hover:scale-105
                    ${option.primary ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
                  `}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`
                      flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center text-white
                      ${option.color}
                    `}>
                      <option.icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {option.title}
                        {option.primary && (
                          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Khuyến nghị
                          </span>
                        )}
                      </h3>
                      <p className="text-gray-600 mt-1">{option.description}</p>
                    </div>
                    <ArrowForwardIcon className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* User Types Information */}
          <div className="bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Loại tài khoản OneID
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {userTypes.map((userType, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      <userType.icon className="h-6 w-6 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{userType.title}</h3>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4">{userType.description}</p>
                  <ul className="space-y-2">
                    {userType.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Information Panel */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start space-x-3">
              <InfoIcon className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  Về OneID Single Sign-On (SSO)
                </h3>
                <div className="text-blue-800 space-y-2">
                  <p>
                    OneID là hệ thống xác thực tập trung cho tất cả ứng dụng trong hệ sinh thái ABN Green. 
                    Với OneID, bạn chỉ cần đăng nhập một lần để truy cập tất cả các ứng dụng.
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li><strong>Bảo mật cao:</strong> Mã hóa end-to-end và xác thực đa yếu tố</li>
                    <li><strong>Tiện lợi:</strong> Đăng nhập một lần cho tất cả ứng dụng</li>
                    <li><strong>Quản lý tập trung:</strong> Quản lý tài khoản và quyền truy cập từ một nơi</li>
                    <li><strong>Hỗ trợ đa dạng:</strong> Hỗ trợ cá nhân, nhân viên và quản trị công ty</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center text-sm text-gray-600">
            <p>
              © 2024 ABN Green. Tất cả quyền được bảo lưu. |{' '}
              <Link href="/privacy" className="hover:text-blue-600">Chính sách bảo mật</Link> |{' '}
              <Link href="/terms" className="hover:text-blue-600">Điều khoản sử dụng</Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function OneIDAuthPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">Đang tải...</div>}>
      <OneIDAuthIndex />
    </Suspense>
  );
}
