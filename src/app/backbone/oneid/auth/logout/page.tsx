"use client";

import { useState, Suspense, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { OneIDService } from '../../services/OneIDService';
import {
  ExitToApp as LogoutIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material';

// Default OneID logo
const DEFAULT_LOGO = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/dace.logo.1.png';

function OneIDLogoutForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/';
  const autoLogout = searchParams?.get('auto') === 'true';

  const [isLoading, setIsLoading] = useState(autoLogout);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const oneIDService = OneIDService.getInstance();

  useEffect(() => {
    if (autoLogout) {
      performLogout();
    }
  }, [autoLogout]);

  const performLogout = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Get current session token
      const token = localStorage.getItem('oneid_token');
      
      if (token) {
        // Call logout API to invalidate session on server
        await oneIDService.logout(token);
      }

      // Clear local storage
      localStorage.removeItem('oneid_token');
      localStorage.removeItem('oneid_user');
      
      // Clear cookies
      document.cookie = 'oneid_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      document.cookie = 'oneid_remember=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      
      setSuccess(true);
      setIsLoading(false);

      // Auto redirect after 2 seconds if auto logout
      if (autoLogout) {
        setTimeout(() => {
          window.location.href = callbackUrl;
        }, 2000);
      }
    } catch (error) {
      console.error('[OneID LOGOUT] Logout error:', error);
      
      // Even if server logout fails, clear local data
      localStorage.removeItem('oneid_token');
      localStorage.removeItem('oneid_user');
      document.cookie = 'oneid_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      document.cookie = 'oneid_remember=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      
      setSuccess(true);
      setIsLoading(false);
      
      if (autoLogout) {
        setTimeout(() => {
          window.location.href = callbackUrl;
        }, 2000);
      }
    }
  };

  const handleLogout = async () => {
    await performLogout();
  };

  if (success) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Đăng xuất thành công!
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Bạn đã đăng xuất khỏi hệ thống OneID. Tất cả phiên đăng nhập đã được xóa.
              </p>
              {autoLogout && (
                <p className="mt-4 text-center text-xs text-gray-500">
                  Đang chuyển hướng...
                </p>
              )}
            </div>
            <div className="mt-6 space-y-4">
              <Link 
                href="/backbone/oneid/auth/login"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <ArrowForwardIcon className="h-4 w-4 mr-2" />
                Đăng nhập lại
              </Link>
              <Link 
                href={callbackUrl}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Về trang chủ
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl text-center">
            <div>
              <RefreshIcon className="animate-spin mx-auto h-16 w-16 text-indigo-500" />
              <h2 className="mt-6 text-center text-2xl font-extrabold text-gray-900">
                Đang đăng xuất...
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Vui lòng chờ trong khi chúng tôi xử lý yêu cầu đăng xuất
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
        <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
          <div>
            <div className="flex justify-center">
              <Image 
                src={DEFAULT_LOGO} 
                alt="OneID Logo" 
                width={120} 
                height={120}
                priority
              />
            </div>
            <div className="flex justify-center mt-4">
              <LogoutIcon className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Đăng xuất OneID
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Bạn có chắc chắn muốn đăng xuất khỏi hệ thống?
            </p>
          </div>
          
          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <ErrorIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">{error}</h3>
                </div>
              </div>
            </div>
          )}

          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="text-sm text-yellow-700">
              <strong>Lưu ý:</strong>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Đăng xuất sẽ xóa tất cả phiên đăng nhập trên thiết bị này</li>
                <li>Bạn sẽ cần đăng nhập lại để truy cập các ứng dụng</li>
                <li>Dữ liệu đã lưu cục bộ sẽ được xóa</li>
              </ul>
            </div>
          </div>

          <div className="space-y-4">
            <button
              onClick={handleLogout}
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <LogoutIcon className="h-4 w-4 mr-2" />
              XÁC NHẬN ĐĂNG XUẤT
            </button>
            
            <Link 
              href={callbackUrl}
              className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              HỦY BỎ
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function OneIDLogoutPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">Đang tải...</div>}>
      <OneIDLogoutForm />
    </Suspense>
  );
}
