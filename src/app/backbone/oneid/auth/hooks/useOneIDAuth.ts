"use client";

import { useState, useEffect, useCallback } from 'react';
import { OneIDService } from '../../services/OneIDService';
import { User, Session } from '../../core/types';

export interface OneIDAuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export interface OneIDAuthActions {
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (userData: {
    userType: 'individual' | 'employee' | 'company_admin';
    username: string;
    email?: string;
    firstName: string;
    lastName: string;
    password: string;
    companyName?: string;
    companyCode?: string;
  }) => Promise<boolean>;
  requestPasswordReset: (identifier: string) => Promise<boolean>;
  resetPassword: (token: string, newPassword: string) => Promise<boolean>;
  sendMagicLink: (identifier: string, callbackUrl: string) => Promise<boolean>;
  verifyMagicLink: (token: string) => Promise<boolean>;
  refreshSession: () => Promise<void>;
  clearError: () => void;
}

export function useOneIDAuth(): OneIDAuthState & OneIDAuthActions {
  const [state, setState] = useState<OneIDAuthState>({
    user: null,
    session: null,
    isLoading: true,
    isAuthenticated: false,
    error: null,
  });

  const oneIDService = OneIDService.getInstance();

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await oneIDService.initialize();
        
        // Check for existing session
        const token = localStorage.getItem('oneid_token');
        if (token) {
          const result = await oneIDService.validateSession(token);
          if (result.valid && result.user && result.session) {
            setState(prev => ({
              ...prev,
              user: result.user!,
              session: result.session!,
              isAuthenticated: true,
              isLoading: false,
            }));
            return;
          } else {
            // Invalid session, clear local storage
            localStorage.removeItem('oneid_token');
            localStorage.removeItem('oneid_user');
          }
        }
        
        setState(prev => ({
          ...prev,
          isLoading: false,
        }));
      } catch (error) {
        console.error('[OneID Auth Hook] Initialization error:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Không thể khởi tạo hệ thống xác thực',
        }));
      }
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (username: string, password: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await oneIDService.authenticate(username, password);
      
      if (result.success && result.user && result.session) {
        // Store session data
        localStorage.setItem('oneid_token', result.session.token);
        localStorage.setItem('oneid_user', JSON.stringify(result.user));
        
        // Set cookie for SSO
        document.cookie = `oneid_token=${result.session.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        
        setState(prev => ({
          ...prev,
          user: result.user!,
          session: result.session!,
          isAuthenticated: true,
          isLoading: false,
        }));
        
        return true;
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Đăng nhập thất bại',
        }));
        return false;
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Đã xảy ra lỗi trong quá trình đăng nhập',
      }));
      return false;
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const token = localStorage.getItem('oneid_token');
      if (token) {
        await oneIDService.logout(token);
      }
    } catch (error) {
      console.error('[OneID Auth Hook] Logout error:', error);
    } finally {
      // Clear local storage and cookies regardless of API call result
      localStorage.removeItem('oneid_token');
      localStorage.removeItem('oneid_user');
      document.cookie = 'oneid_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      document.cookie = 'oneid_remember=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      
      setState({
        user: null,
        session: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
      });
    }
  }, []);

  const register = useCallback(async (userData: {
    userType: 'individual' | 'employee' | 'company_admin';
    username: string;
    email?: string;
    firstName: string;
    lastName: string;
    password: string;
    companyName?: string;
    companyCode?: string;
  }): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await oneIDService.register(userData);
      
      if (result.success) {
        setState(prev => ({ ...prev, isLoading: false }));
        return true;
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Đăng ký thất bại',
        }));
        return false;
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Đã xảy ra lỗi trong quá trình đăng ký',
      }));
      return false;
    }
  }, []);

  const requestPasswordReset = useCallback(async (identifier: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await oneIDService.requestPasswordReset(identifier);
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: result.success ? null : result.error || 'Không thể gửi yêu cầu đặt lại mật khẩu'
      }));
      return result.success;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Đã xảy ra lỗi trong quá trình gửi yêu cầu',
      }));
      return false;
    }
  }, []);

  const resetPassword = useCallback(async (token: string, newPassword: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await oneIDService.resetPassword(token, newPassword);
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: result.success ? null : result.error || 'Không thể đặt lại mật khẩu'
      }));
      return result.success;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Đã xảy ra lỗi trong quá trình đặt lại mật khẩu',
      }));
      return false;
    }
  }, []);

  const sendMagicLink = useCallback(async (identifier: string, callbackUrl: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await oneIDService.sendMagicLink(identifier, callbackUrl);
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: result.success ? null : result.error || 'Không thể gửi magic link'
      }));
      return result.success;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Đã xảy ra lỗi trong quá trình gửi magic link',
      }));
      return false;
    }
  }, []);

  const verifyMagicLink = useCallback(async (token: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await oneIDService.verifyMagicLink(token);
      
      if (result.success && result.user && result.session) {
        // Store session data
        localStorage.setItem('oneid_token', result.session.token);
        localStorage.setItem('oneid_user', JSON.stringify(result.user));
        
        // Set cookie for SSO
        document.cookie = `oneid_token=${result.session.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        
        setState(prev => ({
          ...prev,
          user: result.user!,
          session: result.session!,
          isAuthenticated: true,
          isLoading: false,
        }));
        
        return true;
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Magic link không hợp lệ',
        }));
        return false;
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Đã xảy ra lỗi trong quá trình xác minh magic link',
      }));
      return false;
    }
  }, []);

  const refreshSession = useCallback(async (): Promise<void> => {
    const token = localStorage.getItem('oneid_token');
    if (!token) return;
    
    try {
      const result = await oneIDService.validateSession(token);
      if (result.valid && result.user && result.session) {
        setState(prev => ({
          ...prev,
          user: result.user!,
          session: result.session!,
          isAuthenticated: true,
        }));
      } else {
        await logout();
      }
    } catch (error) {
      console.error('[OneID Auth Hook] Session refresh error:', error);
      await logout();
    }
  }, [logout]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    login,
    logout,
    register,
    requestPasswordReset,
    resetPassword,
    sendMagicLink,
    verifyMagicLink,
    refreshSession,
    clearError,
  };
}
