// OneIDService - Wrapper for OneID System
// This provides a simplified interface for other applications to use OneID

import { OneIDSystem, getOneID, initializeOneID } from '../index';
import { User, Company, Session } from '../core/types';

export class OneIDService {
  private static instance: OneIDService | null = null;
  private oneIDSystem: OneIDSystem | null = null;

  private constructor() {}

  static getInstance(): OneIDService {
    if (!OneIDService.instance) {
      OneIDService.instance = new OneIDService();
    }
    return OneIDService.instance;
  }

  async initialize(): Promise<void> {
    if (!this.oneIDSystem) {
      this.oneIDSystem = await initializeOneID();
    }
  }

  private async getSystem(): Promise<OneIDSystem> {
    if (!this.oneIDSystem) {
      await this.initialize();
    }
    return this.oneIDSystem!;
  }

  // Authentication methods
  async authenticate(username: string, password: string): Promise<{ success: boolean; user?: User; session?: Session; error?: string }> {
    try {
      const system = await this.getSystem();
      const result = await system.auth.authenticate(username, password);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  async validateSession(token: string): Promise<{ valid: boolean; user?: User; session?: Session }> {
    try {
      const system = await this.getSystem();
      const result = await system.auth.validateSession(token);
      return result;
    } catch (error) {
      return { valid: false };
    }
  }

  async logout(token: string): Promise<{ success: boolean }> {
    try {
      const system = await this.getSystem();
      await system.auth.logout(token);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  }

  // Registration method
  async register(userData: {
    userType: 'individual' | 'employee' | 'company_admin';
    username: string;
    email?: string;
    firstName: string;
    lastName: string;
    password: string;
    companyName?: string;
    companyCode?: string;
  }): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      const system = await this.getSystem();

      // For company admin, create company first
      if (userData.userType === 'company_admin' && userData.companyName && userData.companyCode) {
        const companyResult = await system.company.createCompany({
          name: userData.companyName,
          code: userData.companyCode,
          status: 'active',
          settings: {
            allowEmployeeRegistration: true,
            requireEmailVerification: false,
            passwordPolicy: {
              minLength: 6,
              requireUppercase: false,
              requireLowercase: false,
              requireNumbers: false,
              requireSpecialChars: false
            },
            sessionTimeout: 7 * 24 * 60 * 60 * 1000, // 7 days
            maxConcurrentSessions: 5
          }
        });

        if (!companyResult) {
          return { success: false, error: 'Không thể tạo công ty' };
        }

        // Create admin user for the company
        const user = await system.user.createUser({
          username: userData.username,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          userType: userData.userType,
          password: userData.password,
          companyId: companyResult.id,
          status: 'active',
          emailVerified: true
        });

        return { success: true, user };
      } else {
        // For individual or employee users
        let companyId: string | undefined;

        if (userData.userType === 'employee' && userData.companyCode) {
          const company = await system.company.getCompanyByCode(userData.companyCode);
          if (!company) {
            return { success: false, error: 'Mã công ty không tồn tại' };
          }
          companyId = company.id;
        }

        const user = await system.user.createUser({
          username: userData.username,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          userType: userData.userType,
          password: userData.password,
          companyId,
          status: 'active',
          emailVerified: true
        });

        return { success: true, user };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Đã xảy ra lỗi trong quá trình đăng ký'
      };
    }
  }

  // Password reset methods
  async requestPasswordReset(identifier: string): Promise<{ success: boolean; error?: string }> {
    try {
      const system = await this.getSystem();

      // Find user by username or email
      let user = await system.user.getUserByUsername(identifier);
      if (!user && identifier.includes('@')) {
        user = await system.user.getUserByEmail(identifier);
      }

      if (!user) {
        // Don't reveal if user exists or not for security
        return { success: true };
      }

      if (!user.email) {
        return { success: false, error: 'Tài khoản không có email để gửi yêu cầu đặt lại mật khẩu' };
      }

      // Generate reset token and send email (simulated)
      const resetToken = await system.auth.generatePasswordResetToken(user.id);

      // In a real implementation, you would send an email here
      console.log(`[OneID] Password reset token for ${user.username}: ${resetToken}`);
      console.log(`[OneID] Reset link: /backbone/oneid/auth/reset-password?token=${resetToken}`);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Đã xảy ra lỗi trong quá trình gửi yêu cầu'
      };
    }
  }

  async verifyResetToken(token: string): Promise<{ success: boolean; error?: string }> {
    try {
      const system = await this.getSystem();
      const isValid = await system.auth.verifyPasswordResetToken(token);

      if (!isValid) {
        return { success: false, error: 'Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn' };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Không thể xác minh liên kết đặt lại mật khẩu'
      };
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
      const system = await this.getSystem();
      const result = await system.auth.resetPassword(token, newPassword);

      if (!result) {
        return { success: false, error: 'Không thể đặt lại mật khẩu. Liên kết có thể đã hết hạn.' };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Đã xảy ra lỗi trong quá trình đặt lại mật khẩu'
      };
    }
  }

  // Magic link methods
  async sendMagicLink(identifier: string, callbackUrl: string): Promise<{ success: boolean; error?: string }> {
    try {
      const system = await this.getSystem();

      // Find user by username or email
      let user = await system.user.getUserByUsername(identifier);
      if (!user && identifier.includes('@')) {
        user = await system.user.getUserByEmail(identifier);
      }

      if (!user) {
        // Don't reveal if user exists or not for security
        return { success: true };
      }

      if (!user.email) {
        return { success: false, error: 'Tài khoản không có email để gửi magic link' };
      }

      // Generate magic link token
      const magicToken = await system.auth.generateMagicLinkToken(user.id);

      // In a real implementation, you would send an email here
      const magicLink = `/backbone/oneid/auth/magic-link?token=${magicToken}&callbackUrl=${encodeURIComponent(callbackUrl)}`;
      console.log(`[OneID] Magic link for ${user.username}: ${magicLink}`);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Đã xảy ra lỗi trong quá trình gửi magic link'
      };
    }
  }

  async verifyMagicLink(token: string): Promise<{ success: boolean; user?: User; session?: Session; error?: string }> {
    try {
      const system = await this.getSystem();
      const result = await system.auth.verifyMagicLinkToken(token);

      if (!result.success || !result.user) {
        return { success: false, error: 'Magic link không hợp lệ hoặc đã hết hạn' };
      }

      // Create session for the user
      const session = await system.auth.createSession(result.user.id);

      return {
        success: true,
        user: result.user,
        session
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Đã xảy ra lỗi trong quá trình xác minh magic link'
      };
    }
  }

  // User methods
  async getUserById(userId: string): Promise<User | null> {
    try {
      const system = await this.getSystem();
      return await system.user.getUserById(userId);
    } catch (error) {
      return null;
    }
  }

  async getUserByUsername(username: string): Promise<User | null> {
    try {
      const system = await this.getSystem();
      return await system.user.getUserByUsername(username);
    } catch (error) {
      return null;
    }
  }

  async createUser(userData: Partial<User>): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      const system = await this.getSystem();
      const user = await system.user.createUser(userData);
      return { success: true, user };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create user'
      };
    }
  }

  // Company methods
  async getCompanyById(companyId: string): Promise<Company | null> {
    try {
      const system = await this.getSystem();
      return await system.company.getCompanyById(companyId);
    } catch (error) {
      return null;
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details: Record<string, any> }> {
    try {
      const system = await this.getSystem();
      return await system.healthCheck();
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  // Access to full system for advanced use
  async getFullSystem(): Promise<OneIDSystem> {
    return await this.getSystem();
  }
}

// Export singleton instance
export default OneIDService.getInstance();
