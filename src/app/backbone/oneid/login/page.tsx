'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  Business as BusinessIcon,
  Factory as FactoryIcon,
  Store as StoreIcon,
  School as SchoolIcon,
  LocalHospital as HospitalIcon,
  AccountBalance as BankIcon,
  Restaurant as RestaurantIcon,
  LocalShipping as LogisticsIcon,
  Apartment as RealEstateIcon,
  Computer as TechIcon,
  Phone as TelecomIcon,
  LocalGroceryStore as RetailIcon,
  LocalLibrary as EducationIcon,
  LocalPharmacy as PharmacyIcon,
  LocalGasStation as EnergyIcon,
  LocalAtm as FinanceIcon,
  LocalCafe as FoodIcon,
  LocalHotel as HospitalityIcon,
  LocalCarWash as AutomotiveIcon,
  LocalFlorist as AgricultureIcon,
  LocalMall as ShoppingIcon,
  LocalPostOffice as PostalIcon,
  LocalPrintshop as PrintingIcon,
  LocalSee as TourismIcon,
  LocalTaxi as TransportationIcon,
  Security as SecurityIcon,
  Lock as LockIcon,
  VpnKey as KeyIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Password as PasswordIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Help as HelpIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountCircleIcon,
  Group as GroupIcon,
  BusinessCenter as BusinessCenterIcon,
  Work as WorkIcon,
  Assignment as AssignmentIcon,
  Dashboard as DashboardIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { OneIDService } from '../services/OneIDService';
import { User, Session } from '../core/types';

// Default OneID logo
const DEFAULT_LOGO = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/dace.logo.1.png';

// Helper function to check if a URL is external
const isExternalUrl = (url: string): boolean => {
  return url.startsWith('http://') || url.startsWith('https://');
};

function OneIDLoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/';
  const errorParam = searchParams?.get('error');

  const [loginIdentifier, setLoginIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const oneIDService = OneIDService.getInstance();

  useEffect(() => {
    // Initialize OneID service
    oneIDService.initialize().catch(console.error);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setLoginError('');

    console.log('[OneID LOGIN] Attempting login with:', {
      identifier: loginIdentifier,
      callbackUrl
    });

    try {
      console.log('[OneID LOGIN] Calling OneID authenticate');
      const response = await fetch('/api/backbone/oneid/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: loginIdentifier,
          password: password,
          rememberMe: rememberMe
        }),
      });

      const result = await response.json();
      console.log('[OneID LOGIN] Authentication result:', {
        success: result.success,
        error: result.error,
        user: result.user ? { id: result.user.id, username: result.user.username } : null
      });

      if (!result.success || !result.user || !result.session) {
        console.log('[OneID LOGIN] Login failed with error:', result.error);
        setLoginError(result.error || 'Tài khoản hoặc mật khẩu không đúng');
        setIsLoading(false);
      } else {
        console.log('[OneID LOGIN] Login successful, setting session and redirecting to:', callbackUrl);

        // Store session token in localStorage and cookie for SSO
        if (result.session.token) {
          localStorage.setItem('oneid_token', result.session.token);
          localStorage.setItem('oneid_user', JSON.stringify(result.user));

          // Set cookie for cross-domain SSO
          document.cookie = `oneid_token=${result.session.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;

          // Set remember me cookie if selected
          if (rememberMe) {
            document.cookie = `oneid_remember=true; path=/; max-age=${30 * 24 * 60 * 60}; SameSite=Lax`;
          }
        }

        // Create a visible element to display the token for debugging
        if (result.session.token && typeof document !== 'undefined') {
          const tokenDisplay = document.createElement('div');
          tokenDisplay.style.position = 'fixed';
          tokenDisplay.style.top = '10px';
          tokenDisplay.style.right = '10px';
          tokenDisplay.style.backgroundColor = 'rgba(0,0,0,0.8)';
          tokenDisplay.style.color = 'white';
          tokenDisplay.style.padding = '10px';
          tokenDisplay.style.borderRadius = '5px';
          tokenDisplay.style.zIndex = '9999';
          tokenDisplay.style.maxWidth = '400px';
          tokenDisplay.style.overflow = 'auto';
          tokenDisplay.style.maxHeight = '200px';
          tokenDisplay.style.fontSize = '12px';
          tokenDisplay.style.fontFamily = 'monospace';
          tokenDisplay.innerHTML = `<strong>OneID Token Info:</strong><br/>
            <pre>${JSON.stringify({
              token: result.session.token,
              user: result.user,
              expiresAt: result.session.expiresAt
            }, null, 2)}</pre>`;
          document.body.appendChild(tokenDisplay);

          // Remove after 20 seconds
          setTimeout(() => {
            if (document.body.contains(tokenDisplay)) {
              document.body.removeChild(tokenDisplay);
            }
          }, 20000);
        }

        // Redirect to callback URL
        window.location.href = callbackUrl;
      }
    } catch (error) {
      console.error('[OneID LOGIN] Login error:', error);
      setLoginError('Đã xảy ra lỗi trong quá trình đăng nhập');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[url('https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/background.2.webp')] bg-cover bg-center bg-no-repeat">
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 backdrop-blur-sm">
        <div className="max-w-md w-full space-y-8 bg-white/80 backdrop-blur-md p-8 rounded-lg shadow-xl">
          <div>
            <div className="flex justify-center">
              <Image
                src={DEFAULT_LOGO}
                alt="OneID Logo"
                width={150}
                height={150}
                priority
              />
            </div>
          </div>

          {errorParam && (
            <div className="rounded-md bg-red-50 p-4 mb-4">
              <div className="flex">
                <ErrorIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    {errorParam === 'CredentialsSignin'
                      ? 'Tài khoản hoặc mật khẩu không đúng'
                      : 'Đã xảy ra lỗi trong quá trình đăng nhập'}
                  </h3>
                </div>
              </div>
            </div>
          )}

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="-space-y-px rounded-md shadow-sm">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <PersonIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="login-identifier"
                  name="identifier"
                  type="text"
                  autoComplete="username"
                  required
                  className="relative block w-full pl-10 pr-3 py-2 rounded-t-md border-0 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  placeholder="Tài khoản"
                  value={loginIdentifier}
                  onChange={(e) => setLoginIdentifier(e.target.value)}
                />
              </div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  className="relative block w-full pl-10 pr-10 py-2 rounded-b-md border-0 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  placeholder="Mật khẩu"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-600"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <VisibilityOffIcon className="h-5 w-5" />
                    ) : (
                      <VisibilityIcon className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {loginError && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <ErrorIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{loginError}</h3>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-600 border-gray-300 rounded"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                  Ghi nhớ đăng nhập
                </label>
              </div>

              <div className="text-sm">
                <Link href="/backbone/oneid/auth/forgot-password" className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center">
                  <HelpIcon className="h-4 w-4 mr-1" />
                  Quên mật khẩu?
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative flex w-full justify-center rounded-md border border-gray-300 py-2 px-3 text-sm font-semibold text-gray-900 hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 disabled:text-gray-400 disabled:border-gray-200"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <RefreshIcon className="animate-spin h-4 w-4 mr-2" />
                    ĐANG ĐĂNG NHẬP...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <ArrowForwardIcon className="h-4 w-4 mr-2" />
                    ĐĂNG NHẬP
                  </div>
                )}
              </button>
            </div>

            <div className="text-center">
              <Link href="/backbone/oneid/auth/register" className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center justify-center">
                <AddIcon className="h-4 w-4 mr-1" />
                Tạo tài khoản mới
              </Link>
            </div>

            <div className="mt-4 grid grid-cols-6 gap-2">
              <BusinessIcon className="text-gray-400" />
              <FactoryIcon className="text-gray-400" />
              <StoreIcon className="text-gray-400" />
              <SchoolIcon className="text-gray-400" />
              <HospitalIcon className="text-gray-400" />
              <BankIcon className="text-gray-400" />
              <RestaurantIcon className="text-gray-400" />
              <LogisticsIcon className="text-gray-400" />
              <RealEstateIcon className="text-gray-400" />
              <TechIcon className="text-gray-400" />
              <TelecomIcon className="text-gray-400" />
              <RetailIcon className="text-gray-400" />
              <EducationIcon className="text-gray-400" />
              <PharmacyIcon className="text-gray-400" />
              <EnergyIcon className="text-gray-400" />
              <FinanceIcon className="text-gray-400" />
              <FoodIcon className="text-gray-400" />
              <HospitalityIcon className="text-gray-400" />
              <AutomotiveIcon className="text-gray-400" />
              <AgricultureIcon className="text-gray-400" />
              <ShoppingIcon className="text-gray-400" />
              <PostalIcon className="text-gray-400" />
              <PrintingIcon className="text-gray-400" />
              <TourismIcon className="text-gray-400" />
            </div>
          </form>

        </div>
      </div>
    </div>
  );
}

export default function OneIDLoginPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">Đang tải...</div>}>
      <OneIDLoginForm />
    </Suspense>
  );
}
