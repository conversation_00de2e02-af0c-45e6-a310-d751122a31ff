'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Building2,
  Users,
  Activity,
  TrendingUp,
  DollarSign,
  Zap,
  Database,
  Shield,
  RefreshCw,
  Plus,
  ArrowUpRight,
  ArrowDownRight,
  Eye
} from 'lucide-react';
import StatisticsOverview from './components/dashboard/StatisticsOverview';
import CompanyMetrics from './components/dashboard/CompanyMetrics';
import UserMetrics from './components/dashboard/UserMetrics';
import ServiceMetrics from './components/dashboard/ServiceMetrics';
import ConsumptionCharts from './components/dashboard/ConsumptionCharts';
import Link from 'next/link';

interface DashboardStats {
  companies: {
    total: number;
    active: number;
    inactive: number;
    rootCompanies: number;
    maxHierarchyDepth: number;
    recentlyCreated: number;
  };
  users: {
    total: number;
    active: number;
    byType: {
      individual: number;
      employee: number;
      company_admin: number;
    };
    recentlyRegistered: number;
    activeToday: number;
  };
  services: {
    totalServices: number;
    activeSubscriptions: number;
    totalRevenue: number;
    averageUsage: number;
  };
  consumption: {
    apiCalls: number;
    storageUsed: number;
    bandwidthUsed: number;
    activeConnections: number;
  };
  growth: {
    companiesGrowth: number;
    usersGrowth: number;
    revenueGrowth: number;
    usageGrowth: number;
  };
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setRefreshing(true);

      // Fetch real data from the admin statistics API
      const response = await fetch('/api/backbone/oneid/admin/statistics');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setStats(result.data);
          return;
        }
      }

      // Fallback to mock data if API fails
      const mockStats: DashboardStats = {
        companies: {
          total: 10,
          active: 9,
          inactive: 1,
          rootCompanies: 5,
          maxHierarchyDepth: 1,
          recentlyCreated: 2
        },
        users: {
          total: 45,
          active: 42,
          byType: {
            individual: 15,
            employee: 25,
            company_admin: 5
          },
          recentlyRegistered: 8,
          activeToday: 28
        },
        services: {
          totalServices: 12,
          activeSubscriptions: 8,
          totalRevenue: 125000,
          averageUsage: 78.5
        },
        consumption: {
          apiCalls: 1250000,
          storageUsed: 2.4, // GB
          bandwidthUsed: 156.7, // GB
          activeConnections: 342
        },
        growth: {
          companiesGrowth: 15.2,
          usersGrowth: 23.8,
          revenueGrowth: 18.5,
          usageGrowth: 12.3
        }
      };

      setStats(mockStats);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    trend, 
    trendValue,
    color = 'blue',
    href
  }: any) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 text-${color}-600`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <div className="flex items-center justify-between mt-2">
          <p className="text-xs text-muted-foreground">{description}</p>
          {trend && (
            <div className={`flex items-center text-xs ${
              trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {trend === 'up' ? (
                <ArrowUpRight className="h-3 w-3 mr-1" />
              ) : (
                <ArrowDownRight className="h-3 w-3 mr-1" />
              )}
              {trendValue}%
            </div>
          )}
        </div>
        {href && (
          <Link href={href}>
            <Button variant="ghost" size="sm" className="mt-2 w-full">
              <Eye className="h-3 w-3 mr-1" />
              View Details
            </Button>
          </Link>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">
            Comprehensive overview of companies, users, and services in your organization
          </p>
        </div>
        <div className="flex space-x-2">
          <Link href="/backbone/oneid/admin/onboarding">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Company
            </Button>
          </Link>
          <Button variant="outline" onClick={fetchDashboardStats} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Quick Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Companies"
          value={stats?.companies.total}
          description={`${stats?.companies.active} active, ${stats?.companies.inactive} inactive`}
          icon={Building2}
          trend="up"
          trendValue={stats?.growth.companiesGrowth}
          color="blue"
          href="/backbone/oneid/admin/companies"
        />
        <StatCard
          title="Total Users"
          value={stats?.users.total}
          description={`${stats?.users.activeToday} active today`}
          icon={Users}
          trend="up"
          trendValue={stats?.growth.usersGrowth}
          color="green"
          href="/backbone/oneid/admin/users"
        />
        <StatCard
          title="Revenue"
          value={`$${(stats?.services.totalRevenue || 0).toLocaleString()}`}
          description={`${stats?.services.activeSubscriptions} active subscriptions`}
          icon={DollarSign}
          trend="up"
          trendValue={stats?.growth.revenueGrowth}
          color="emerald"
          href="/backbone/oneid/admin/services"
        />
        <StatCard
          title="API Calls"
          value={`${((stats?.consumption.apiCalls || 0) / 1000000).toFixed(1)}M`}
          description="This month"
          icon={Activity}
          trend="up"
          trendValue={stats?.growth.usageGrowth}
          color="purple"
          href="/backbone/oneid/admin/analytics/consumption"
        />
      </div>

      {/* Detailed Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="companies">Companies</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="consumption">Consumption</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <StatisticsOverview stats={stats} />
        </TabsContent>

        <TabsContent value="companies" className="space-y-4">
          <CompanyMetrics stats={stats?.companies} />
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <UserMetrics stats={stats?.users} />
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <ServiceMetrics stats={stats?.services} />
        </TabsContent>

        <TabsContent value="consumption" className="space-y-4">
          <ConsumptionCharts stats={stats?.consumption} />
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks and shortcuts for managing your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Link href="/backbone/oneid/admin/onboarding">
              <Button variant="outline" className="w-full h-20 flex flex-col">
                <Plus className="h-6 w-6 mb-2" />
                Create Company
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/users/assign">
              <Button variant="outline" className="w-full h-20 flex flex-col">
                <Users className="h-6 w-6 mb-2" />
                Assign Users
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/companies/hierarchy">
              <Button variant="outline" className="w-full h-20 flex flex-col">
                <Activity className="h-6 w-6 mb-2" />
                View Hierarchy
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/analytics/reports">
              <Button variant="outline" className="w-full h-20 flex flex-col">
                <TrendingUp className="h-6 w-6 mb-2" />
                Generate Report
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
