# Admin Management Portal

A comprehensive portal for managing companies, users, services, and analytics within the OneID system.

## 🏗️ Architecture

### Portal Structure
```
src/app/backbone/oneid/admin/
├── page.tsx                           # Main portal entry point
├── layout.tsx                         # Portal layout with navigation
├── components/                        # Reusable portal components
│   ├── navigation/                    # Navigation components
│   │   ├── PortalSidebar.tsx         # Main sidebar navigation
│   │   ├── PortalHeader.tsx          # Top header with breadcrumbs
│   │   └── PortalBreadcrumbs.tsx     # Breadcrumb navigation
│   ├── dashboard/                     # Dashboard components
│   │   ├── StatisticsOverview.tsx    # Main statistics dashboard
│   │   ├── CompanyMetrics.tsx        # Company-specific metrics
│   │   ├── UserMetrics.tsx           # User analytics
│   │   ├── ServiceMetrics.tsx        # Service usage metrics
│   │   └── ConsumptionCharts.tsx     # Resource consumption charts
│   ├── onboarding/                    # Onboarding workflow components
│   │   ├── OnboardingWizard.tsx      # Main onboarding wizard
│   │   ├── CompanyCreationStep.tsx   # Company creation step
│   │   ├── AdminUserStep.tsx         # Admin user setup step
│   │   ├── PermissionsStep.tsx       # Permissions configuration
│   │   └── ReviewStep.tsx            # Final review step
│   ├── management/                    # Management interfaces
│   │   ├── CompanyList.tsx           # Company listing with hierarchy
│   │   ├── CompanyDetails.tsx        # Detailed company view
│   │   ├── UserAssignment.tsx        # User assignment interface
│   │   ├── AccessRights.tsx          # Access rights management
│   │   └── ServiceManagement.tsx     # Service configuration
│   └── charts/                        # Data visualization components
│       ├── HierarchyTree.tsx         # Company hierarchy visualization
│       ├── UsageCharts.tsx           # Usage analytics charts
│       ├── GrowthCharts.tsx          # Growth trend charts
│       └── ComparisonCharts.tsx      # Comparison analytics
├── dashboard/                         # Dashboard pages
│   ├── page.tsx                      # Main dashboard
│   ├── companies/                    # Company management pages
│   │   ├── page.tsx                  # Company list
│   │   ├── [id]/                     # Individual company pages
│   │   │   ├── page.tsx              # Company details
│   │   │   ├── users/page.tsx        # Company users
│   │   │   ├── services/page.tsx     # Company services
│   │   │   └── analytics/page.tsx    # Company analytics
│   │   └── create/page.tsx           # Create new company
│   ├── users/                        # User management pages
│   │   ├── page.tsx                  # User list
│   │   ├── [id]/page.tsx             # User details
│   │   └── assign/page.tsx           # User assignment
│   ├── services/                     # Service management pages
│   │   ├── page.tsx                  # Service overview
│   │   ├── subscriptions/page.tsx    # Subscription management
│   │   └── usage/page.tsx            # Usage analytics
│   └── analytics/                    # Analytics pages
│       ├── page.tsx                  # Overview analytics
│       ├── consumption/page.tsx      # Resource consumption
│       ├── growth/page.tsx           # Growth analytics
│       └── reports/page.tsx          # Custom reports
├── onboarding/                       # Onboarding workflow pages
│   ├── page.tsx                      # Onboarding start
│   ├── company/page.tsx              # Company creation
│   ├── admin/page.tsx                # Admin user setup
│   ├── permissions/page.tsx          # Permissions setup
│   └── complete/page.tsx             # Completion page
├── api/                              # Portal-specific API endpoints
│   ├── statistics/route.ts           # Statistics aggregation
│   ├── onboarding/route.ts           # Onboarding workflow
│   ├── bulk-operations/route.ts      # Bulk operations
│   └── reports/route.ts              # Report generation
├── hooks/                            # Custom React hooks
│   ├── usePortalData.ts              # Portal data management
│   ├── useCompanyStats.ts            # Company statistics
│   ├── useOnboarding.ts              # Onboarding workflow
│   └── useConsumption.ts             # Consumption tracking
├── types/                            # TypeScript type definitions
│   ├── portal.ts                     # Portal-specific types
│   ├── statistics.ts                 # Statistics types
│   └── onboarding.ts                 # Onboarding types
└── utils/                            # Utility functions
    ├── statistics.ts                 # Statistics calculations
    ├── formatting.ts                 # Data formatting
    └── validation.ts                 # Form validation
```

## 🎯 Features

### 📊 Statistics Dashboard
- **Company Overview**: Total companies, hierarchy depth, active/inactive status
- **User Analytics**: User distribution, growth trends, activity metrics
- **Service Usage**: Service adoption, consumption patterns, performance metrics
- **Resource Consumption**: API calls, storage usage, bandwidth consumption
- **Financial Metrics**: Revenue, subscription status, billing analytics

### 🚀 Onboarding Workflow
- **Step-by-Step Process**: Guided company creation workflow
- **Company Setup**: Basic information, hierarchy placement, settings
- **Admin User Creation**: Primary administrator account setup
- **Permission Configuration**: Access rights, limits, and restrictions
- **Service Selection**: Available services and subscription options
- **Review & Activation**: Final review and company activation

### 👥 User Management
- **User Assignment**: Assign users to companies and roles
- **Hierarchy Access**: Multi-level access through company hierarchy
- **Permission Management**: Granular permission control
- **Bulk Operations**: Mass user operations and imports
- **Activity Tracking**: User activity and audit logs

### 🏢 Company Management
- **Hierarchy Visualization**: Interactive company hierarchy tree
- **Company Details**: Comprehensive company information
- **Settings Management**: Company-specific configurations
- **Service Configuration**: Enabled services and features
- **Analytics**: Company-specific performance metrics

### 🔐 Access Rights & Limits
- **Role-Based Access**: Hierarchical role management
- **Resource Quotas**: Usage limits and restrictions
- **API Rate Limiting**: Request rate controls
- **Feature Toggles**: Service-specific access controls
- **Compliance Tracking**: Audit trails and compliance reports

### 📈 Analytics & Reporting
- **Real-time Dashboards**: Live metrics and KPIs
- **Trend Analysis**: Growth and usage trends
- **Comparative Analytics**: Cross-company comparisons
- **Custom Reports**: Configurable reporting system
- **Export Capabilities**: Data export in multiple formats

## 🎨 Design Principles

### User Experience
- **Intuitive Navigation**: Clear, hierarchical navigation structure
- **Responsive Design**: Mobile-first, responsive layouts
- **Progressive Disclosure**: Information revealed progressively
- **Consistent UI**: Unified design language and components
- **Accessibility**: WCAG 2.1 AA compliance

### Performance
- **Lazy Loading**: Components loaded on demand
- **Data Virtualization**: Efficient handling of large datasets
- **Caching Strategy**: Intelligent data caching
- **Optimistic Updates**: Immediate UI feedback
- **Error Boundaries**: Graceful error handling

### Scalability
- **Modular Architecture**: Loosely coupled components
- **API-First Design**: Headless architecture approach
- **Horizontal Scaling**: Support for large datasets
- **Plugin System**: Extensible functionality
- **Multi-tenant Support**: Isolated tenant data

## 🔧 Technology Stack

### Frontend
- **React 18**: Modern React with hooks and suspense
- **Next.js 14**: App router and server components
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **shadcn/ui**: Consistent UI components
- **Recharts**: Data visualization library
- **React Hook Form**: Form management
- **Zod**: Schema validation

### Backend Integration
- **OneID System**: Authentication and user management
- **Company Hierarchy**: Multi-level company relationships
- **RESTful APIs**: Standard HTTP APIs
- **Real-time Updates**: WebSocket connections
- **File Uploads**: Document and image handling

### Data Management
- **React Query**: Server state management
- **Zustand**: Client state management
- **JSON Storage**: File-based data persistence
- **Data Validation**: Runtime type checking
- **Error Handling**: Comprehensive error management

## 🚀 Getting Started

### Prerequisites
- OneID system installed and configured
- Company hierarchy system enabled
- Required dependencies installed

### Installation
1. Navigate to the admin portal
2. Access via `/backbone/oneid/admin`
3. Authenticate with admin credentials
4. Begin using the portal features

### Configuration
- Portal settings in `data/apps/backbone/oneid/portal/`
- Company configurations in `data/apps/backbone/oneid/companies/`
- User permissions in `data/apps/backbone/oneid/permissions/`

## 📋 Usage Examples

### Creating a New Company
1. Navigate to Onboarding → New Company
2. Fill in company information
3. Set up admin user account
4. Configure permissions and limits
5. Review and activate company

### Managing User Access
1. Go to Users → Assignment
2. Select user and target company
3. Choose role and permissions
4. Set access limits and restrictions
5. Save and activate assignment

### Viewing Analytics
1. Access Dashboard → Analytics
2. Select time range and metrics
3. Filter by company or service
4. Export reports as needed
5. Set up automated reporting

This portal provides a comprehensive solution for managing companies, users, and services within the OneID ecosystem, with powerful analytics and streamlined workflows.
