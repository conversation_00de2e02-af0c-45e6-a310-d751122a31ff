'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  BarChart3,
  Building2,
  Users,
  Settings,
  Plus,
  TrendingUp,
  Shield,
  Activity,
  FileText,
  Zap,
  Database,
  UserPlus,
  Building,
  PieChart,
  LineChart,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PortalSidebarProps {
  open: boolean;
  onClose: () => void;
  currentPath: string;
}

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavigationItem[];
  badge?: string;
}

const navigation: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/backbone/oneid/admin',
    icon: BarChart3,
  },
  {
    name: 'Companies',
    href: '/backbone/oneid/admin/companies',
    icon: Building2,
    children: [
      {
        name: 'All Companies',
        href: '/backbone/oneid/admin/companies',
        icon: Building,
      },
      {
        name: 'Create Company',
        href: '/backbone/oneid/admin/companies/create',
        icon: Plus,
      },
      {
        name: 'Hierarchy View',
        href: '/backbone/oneid/admin/companies/hierarchy',
        icon: Activity,
      },
    ],
  },
  {
    name: 'Users',
    href: '/backbone/oneid/admin/users',
    icon: Users,
    children: [
      {
        name: 'All Users',
        href: '/backbone/oneid/admin/users',
        icon: Users,
      },
      {
        name: 'User Assignment',
        href: '/backbone/oneid/admin/users/assign',
        icon: UserPlus,
      },
      {
        name: 'Access Rights',
        href: '/backbone/oneid/admin/users/access',
        icon: Shield,
      },
    ],
  },
  {
    name: 'Services',
    href: '/backbone/oneid/admin/services',
    icon: Zap,
    children: [
      {
        name: 'Service Overview',
        href: '/backbone/oneid/admin/services',
        icon: Zap,
      },
      {
        name: 'Subscriptions',
        href: '/backbone/oneid/admin/services/subscriptions',
        icon: FileText,
      },
      {
        name: 'Usage Analytics',
        href: '/backbone/oneid/admin/services/usage',
        icon: TrendingUp,
      },
    ],
  },
  {
    name: 'Analytics',
    href: '/backbone/oneid/admin/analytics',
    icon: PieChart,
    children: [
      {
        name: 'Overview',
        href: '/backbone/oneid/admin/analytics',
        icon: PieChart,
      },
      {
        name: 'Consumption',
        href: '/backbone/oneid/admin/analytics/consumption',
        icon: Database,
      },
      {
        name: 'Growth Trends',
        href: '/backbone/oneid/admin/analytics/growth',
        icon: LineChart,
      },
      {
        name: 'Reports',
        href: '/backbone/oneid/admin/analytics/reports',
        icon: FileText,
      },
    ],
  },
  {
    name: 'Onboarding',
    href: '/backbone/oneid/admin/onboarding',
    icon: Plus,
    badge: 'New',
  },
];

export default function PortalSidebar({ open, onClose, currentPath }: PortalSidebarProps) {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === '/backbone/oneid/admin') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const hasActiveChild = (item: NavigationItem) => {
    if (!item.children) return false;
    return item.children.some(child => isActive(child.href));
  };

  return (
    <>
      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white border-r border-gray-200 px-6 pb-4">
          {/* Logo */}
          <div className="flex h-16 shrink-0 items-center">
            <Building2 className="h-8 w-8 text-blue-600" />
            <span className="ml-2 text-xl font-semibold text-gray-900">
              Company Portal
            </span>
          </div>

          {/* Navigation */}
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <div>
                        <Link
                          href={item.href}
                          className={cn(
                            'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold',
                            isActive(item.href) || hasActiveChild(item)
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-700 hover:text-blue-700 hover:bg-gray-50'
                          )}
                        >
                          <item.icon
                            className={cn(
                              'h-6 w-6 shrink-0',
                              isActive(item.href) || hasActiveChild(item)
                                ? 'text-blue-700'
                                : 'text-gray-400 group-hover:text-blue-700'
                            )}
                          />
                          {item.name}
                          {item.badge && (
                            <span className="ml-auto inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700">
                              {item.badge}
                            </span>
                          )}
                        </Link>
                        
                        {/* Sub-navigation */}
                        {item.children && (hasActiveChild(item) || isActive(item.href)) && (
                          <ul className="mt-1 px-2">
                            {item.children.map((child) => (
                              <li key={child.name}>
                                <Link
                                  href={child.href}
                                  className={cn(
                                    'group flex gap-x-3 rounded-md p-2 pl-8 text-sm leading-6',
                                    isActive(child.href)
                                      ? 'bg-blue-50 text-blue-700 font-semibold'
                                      : 'text-gray-600 hover:text-blue-700 hover:bg-gray-50'
                                  )}
                                >
                                  <child.icon
                                    className={cn(
                                      'h-4 w-4 shrink-0',
                                      isActive(child.href)
                                        ? 'text-blue-700'
                                        : 'text-gray-400 group-hover:text-blue-700'
                                    )}
                                  />
                                  {child.name}
                                </Link>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Mobile sidebar */}
      <div className={cn(
        'relative z-50 lg:hidden',
        open ? 'block' : 'hidden'
      )}>
        <div className="fixed inset-0 flex">
          <div className="relative mr-16 flex w-full max-w-xs flex-1">
            <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
              <button
                type="button"
                className="-m-2.5 p-2.5"
                onClick={onClose}
              >
                <span className="sr-only">Close sidebar</span>
                <X className="h-6 w-6 text-white" />
              </button>
            </div>

            <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4">
              {/* Mobile logo */}
              <div className="flex h-16 shrink-0 items-center">
                <Building2 className="h-8 w-8 text-blue-600" />
                <span className="ml-2 text-xl font-semibold text-gray-900">
                  Company Portal
                </span>
              </div>

              {/* Mobile navigation */}
              <nav className="flex flex-1 flex-col">
                <ul role="list" className="flex flex-1 flex-col gap-y-7">
                  <li>
                    <ul role="list" className="-mx-2 space-y-1">
                      {navigation.map((item) => (
                        <li key={item.name}>
                          <Link
                            href={item.href}
                            onClick={onClose}
                            className={cn(
                              'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold',
                              isActive(item.href)
                                ? 'bg-blue-50 text-blue-700'
                                : 'text-gray-700 hover:text-blue-700 hover:bg-gray-50'
                            )}
                          >
                            <item.icon
                              className={cn(
                                'h-6 w-6 shrink-0',
                                isActive(item.href)
                                  ? 'text-blue-700'
                                  : 'text-gray-400 group-hover:text-blue-700'
                              )}
                            />
                            {item.name}
                            {item.badge && (
                              <span className="ml-auto inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700">
                                {item.badge}
                              </span>
                            )}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
