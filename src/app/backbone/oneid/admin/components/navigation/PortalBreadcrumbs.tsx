'use client';

import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  name: string;
  href: string;
  current: boolean;
}

interface PortalBreadcrumbsProps {
  currentPath: string;
}

export default function PortalBreadcrumbs({ currentPath }: PortalBreadcrumbsProps) {
  const generateBreadcrumbs = (path: string): BreadcrumbItem[] => {
    const segments = path.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with home
    breadcrumbs.push({
      name: 'Admin Portal',
      href: '/backbone/oneid/admin',
      current: path === '/backbone/oneid/admin'
    });

    // Skip the first segments that are part of the base path
    const relevantSegments = segments.slice(3); // Skip 'backbone', 'oneid', 'admin'

    let currentHref = '/backbone/oneid/admin';

    relevantSegments.forEach((segment, index) => {
      currentHref += `/${segment}`;
      const isLast = index === relevantSegments.length - 1;

      // Convert segment to readable name
      let name = segment;
      switch (segment) {
        case 'companies':
          name = 'Companies';
          break;
        case 'users':
          name = 'Users';
          break;
        case 'services':
          name = 'Service Management';
          break;
        case 'analytics':
          name = 'Analytics';
          break;
        case 'onboarding':
          name = 'Onboarding';
          break;
        case 'create':
          name = 'Create New';
          break;
        case 'assign':
          name = 'User Assignment';
          break;
        case 'access':
          name = 'Access Rights';
          break;
        case 'hierarchy':
          name = 'Hierarchy View';
          break;
        case 'subscriptions':
          name = 'Subscriptions';
          break;
        case 'usage':
          name = 'Usage Analytics';
          break;
        case 'consumption':
          name = 'Resource Consumption';
          break;
        case 'growth':
          name = 'Growth Trends';
          break;
        case 'reports':
          name = 'Reports';
          break;
        default:
          // If it looks like an ID (starts with comp_ or user_), show as ID
          if (segment.startsWith('comp_') || segment.startsWith('user_')) {
            name = segment.toUpperCase();
          } else {
            // Capitalize first letter and replace hyphens with spaces
            name = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
          }
      }

      breadcrumbs.push({
        name,
        href: currentHref,
        current: isLast
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs(currentPath);

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-500">
      <nav className="flex" aria-label="Breadcrumb">
        <ol role="list" className="flex items-center space-x-2">
          {breadcrumbs.map((item, index) => (
            <li key={item.href}>
              <div className="flex items-center">
                {index > 0 && (
                  <ChevronRight className="h-4 w-4 text-gray-400 mr-2" />
                )}
                {index === 0 && (
                  <Home className="h-4 w-4 text-gray-400 mr-2" />
                )}
                {item.current ? (
                  <span className="text-gray-900 font-medium">
                    {item.name}
                  </span>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                      'hover:text-gray-700 transition-colors',
                      index === 0 ? 'text-blue-600 hover:text-blue-700' : ''
                    )}
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            </li>
          ))}
        </ol>
      </nav>
    </div>
  );
}
