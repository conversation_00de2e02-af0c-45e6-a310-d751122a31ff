'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Zap,
  DollarSign,
  TrendingUp,
  Activity,
  Eye,
  Settings,
  ArrowUpRight,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import Link from 'next/link';

interface ServiceMetricsProps {
  stats: any;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

export default function ServiceMetrics({ stats }: ServiceMetricsProps) {
  if (!stats) return null;

  const serviceAdoptionData = [
    { name: 'OneID Auth', adoption: 100, revenue: 25000, companies: 10 },
    { name: 'Company Portal', adoption: 80, revenue: 20000, companies: 8 },
    { name: 'User Management', adoption: 90, revenue: 18000, companies: 9 },
    { name: 'Analytics Suite', adoption: 60, revenue: 15000, companies: 6 },
    { name: 'API Gateway', adoption: 70, revenue: 12000, companies: 7 },
    { name: 'File Storage', adoption: 50, revenue: 10000, companies: 5 },
    { name: 'Notifications', adoption: 85, revenue: 8000, companies: 8 },
    { name: 'Backup Service', adoption: 40, revenue: 6000, companies: 4 },
  ];

  const revenueByService = [
    { name: 'OneID Auth', value: 25000, color: '#3B82F6' },
    { name: 'Company Portal', value: 20000, color: '#10B981' },
    { name: 'User Management', value: 18000, color: '#F59E0B' },
    { name: 'Analytics Suite', value: 15000, color: '#EF4444' },
    { name: 'Others', value: 47000, color: '#8B5CF6' },
  ];

  const usageTrends = [
    { month: 'Jan', apiCalls: 850000, storage: 1.8, users: 180 },
    { month: 'Feb', apiCalls: 920000, storage: 2.0, users: 195 },
    { month: 'Mar', apiCalls: 980000, storage: 2.1, users: 220 },
    { month: 'Apr', apiCalls: 1050000, storage: 2.2, users: 245 },
    { month: 'May', apiCalls: 1180000, storage: 2.3, users: 270 },
    { month: 'Jun', apiCalls: 1250000, storage: 2.4, users: 295 },
  ];

  const serviceStatus = [
    { name: 'OneID Authentication', status: 'operational', uptime: 99.9, lastIncident: '30 days ago' },
    { name: 'Company Portal', status: 'operational', uptime: 99.8, lastIncident: '15 days ago' },
    { name: 'User Management API', status: 'operational', uptime: 99.7, lastIncident: '7 days ago' },
    { name: 'Analytics Service', status: 'degraded', uptime: 98.5, lastIncident: '2 hours ago' },
    { name: 'File Storage', status: 'operational', uptime: 99.6, lastIncident: '12 days ago' },
    { name: 'Notification Service', status: 'operational', uptime: 99.4, lastIncident: '5 days ago' },
  ];

  return (
    <div className="space-y-6">
      {/* Service Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Services</CardTitle>
            <Zap className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalServices}</div>
            <p className="text-xs text-muted-foreground">
              Available services
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeSubscriptions}</div>
            <p className="text-xs text-muted-foreground">
              Across all companies
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(stats.totalRevenue || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Monthly recurring revenue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Usage</CardTitle>
            <Activity className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageUsage}%</div>
            <p className="text-xs text-muted-foreground">
              Service utilization
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Service Adoption */}
        <Card>
          <CardHeader>
            <CardTitle>Service Adoption</CardTitle>
            <CardDescription>
              Adoption rate and revenue by service
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={serviceAdoptionData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'adoption' ? `${value}%` : `$${value.toLocaleString()}`,
                    name === 'adoption' ? 'Adoption Rate' : 'Revenue'
                  ]}
                />
                <Bar dataKey="adoption" fill="#3B82F6" name="adoption" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Revenue Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue by Service</CardTitle>
            <CardDescription>
              Monthly revenue breakdown by service type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={revenueByService}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {revenueByService.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `$${value.toLocaleString()}`} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Usage Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Service Usage Trends</CardTitle>
          <CardDescription>
            API calls, storage usage, and active users over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={usageTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip 
                formatter={(value, name) => [
                  name === 'apiCalls' ? `${(value / 1000000).toFixed(1)}M` : 
                  name === 'storage' ? `${value}GB` : value,
                  name === 'apiCalls' ? 'API Calls' : 
                  name === 'storage' ? 'Storage' : 'Active Users'
                ]}
              />
              <Area
                type="monotone"
                dataKey="apiCalls"
                stackId="1"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.6}
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Service Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Service Status</CardTitle>
            <CardDescription>
              Current operational status and uptime for all services
            </CardDescription>
          </div>
          <Link href="/backbone/oneid/admin/services">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {serviceStatus.map((service) => (
              <div key={service.name} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full">
                    {service.status === 'operational' ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-yellow-600" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-semibold">{service.name}</h4>
                    <p className="text-sm text-gray-600">
                      Last incident: {service.lastIncident}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">
                    {service.uptime}% uptime
                  </div>
                  <Badge 
                    variant={service.status === 'operational' ? 'default' : 'secondary'}
                    className={service.status === 'operational' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}
                  >
                    {service.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Service Management Actions</CardTitle>
          <CardDescription>
            Quick actions for managing services and subscriptions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Link href="/backbone/oneid/admin/services/subscriptions">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <Settings className="h-5 w-5 mb-1" />
                Manage Subscriptions
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/services/usage">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <TrendingUp className="h-5 w-5 mb-1" />
                Usage Analytics
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/services">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <Zap className="h-5 w-5 mb-1" />
                Service Overview
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
