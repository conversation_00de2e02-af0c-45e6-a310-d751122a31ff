'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Users,
  UserPlus,
  Activity,
  TrendingUp,
  Eye,
  Shield,
  ArrowUpRight
} from 'lucide-react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import Link from 'next/link';

interface UserMetricsProps {
  stats: any;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B'];

export default function UserMetrics({ stats }: UserMetricsProps) {
  if (!stats) return null;

  const userTypeData = [
    { name: 'Individual', value: stats.byType.individual, color: '#3B82F6' },
    { name: 'Employee', value: stats.byType.employee, color: '#10B981' },
    { name: 'Company Admin', value: stats.byType.company_admin, color: '#F59E0B' },
  ];

  const userActivityData = [
    { day: 'Mon', active: 32, logins: 45 },
    { day: 'Tue', active: 35, logins: 52 },
    { day: 'Wed', active: 38, logins: 48 },
    { day: 'Thu', active: 42, logins: 55 },
    { day: 'Fri', active: 40, logins: 50 },
    { day: 'Sat', active: 25, logins: 28 },
    { day: 'Sun', active: 22, logins: 25 },
  ];

  const registrationTrends = [
    { month: 'Jan', individual: 3, employee: 8, admin: 1 },
    { month: 'Feb', individual: 2, employee: 6, admin: 1 },
    { month: 'Mar', individual: 4, employee: 9, admin: 1 },
    { month: 'Apr', individual: 3, employee: 7, admin: 1 },
    { month: 'May', individual: 2, employee: 8, admin: 1 },
    { month: 'Jun', individual: 1, employee: 5, admin: 0 },
  ];

  const topActiveUsers = [
    { name: 'John Smith', company: 'TechCorp Solutions', role: 'Company Admin', lastActive: '2 min ago', sessions: 45 },
    { name: 'Sarah Johnson', company: 'HealthPlus Medical', role: 'Company Admin', lastActive: '5 min ago', sessions: 38 },
    { name: 'Mike Chen', company: 'TechCorp AI', role: 'Employee', lastActive: '12 min ago', sessions: 32 },
    { name: 'Lisa Wang', company: 'RetailMax Online', role: 'Employee', lastActive: '18 min ago', sessions: 28 },
    { name: 'David Brown', company: 'Green Finance', role: 'Company Admin', lastActive: '25 min ago', sessions: 25 },
  ];

  return (
    <div className="space-y-6">
      {/* User Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.active} active users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Today</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeToday}</div>
            <p className="text-xs text-muted-foreground">
              {((stats.activeToday / stats.total) * 100).toFixed(1)}% of total users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Registrations</CardTitle>
            <UserPlus className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentlyRegistered}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Company Admins</CardTitle>
            <Shield className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.byType.company_admin}</div>
            <p className="text-xs text-muted-foreground">
              Managing {stats.byType.employee} employees
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* User Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>User Type Distribution</CardTitle>
            <CardDescription>
              Breakdown of users by their role type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={userTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {userTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Daily Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Daily User Activity</CardTitle>
            <CardDescription>
              Active users and login sessions this week
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={userActivityData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="active"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  name="Active Users"
                />
                <Line
                  type="monotone"
                  dataKey="logins"
                  stroke="#10B981"
                  strokeWidth={2}
                  name="Login Sessions"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Registration Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Registration Trends</CardTitle>
          <CardDescription>
            New user registrations by type over the last 6 months
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={registrationTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="individual" stackId="a" fill="#3B82F6" name="Individual" />
              <Bar dataKey="employee" stackId="a" fill="#10B981" name="Employee" />
              <Bar dataKey="admin" stackId="a" fill="#F59E0B" name="Admin" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Top Active Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Most Active Users</CardTitle>
            <CardDescription>
              Users with the highest activity and session counts
            </CardDescription>
          </div>
          <Link href="/backbone/oneid/admin/users">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View All Users
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topActiveUsers.map((user, index) => (
              <div key={user.name} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                    <span className="text-sm font-semibold text-blue-600">
                      {index + 1}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold">{user.name}</h4>
                    <p className="text-sm text-gray-600">
                      {user.role} at {user.company}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">
                    {user.sessions} sessions
                  </div>
                  <div className="text-sm text-gray-500">
                    Active {user.lastActive}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>User Management Actions</CardTitle>
          <CardDescription>
            Quick actions for managing users and their access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Link href="/backbone/oneid/admin/users/assign">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <UserPlus className="h-5 w-5 mb-1" />
                Assign Users
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/users/access">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <Shield className="h-5 w-5 mb-1" />
                Manage Access
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/users">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <Users className="h-5 w-5 mb-1" />
                View All Users
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
