'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Database,
  Activity,
  Wifi,
  HardDrive,
  Eye,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import Link from 'next/link';

interface ConsumptionChartsProps {
  stats: any;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

export default function ConsumptionCharts({ stats }: ConsumptionChartsProps) {
  if (!stats) return null;

  const dailyConsumption = [
    { day: 'Mon', apiCalls: 180000, storage: 2.1, bandwidth: 22.5, connections: 320 },
    { day: 'Tue', apiCalls: 195000, storage: 2.2, bandwidth: 24.8, connections: 342 },
    { day: 'Wed', apiCalls: 210000, storage: 2.3, bandwidth: 26.2, connections: 358 },
    { day: 'Thu', apiCalls: 225000, storage: 2.3, bandwidth: 28.1, connections: 375 },
    { day: 'Fri', apiCalls: 240000, storage: 2.4, bandwidth: 30.5, connections: 392 },
    { day: 'Sat', apiCalls: 165000, storage: 2.4, bandwidth: 20.8, connections: 285 },
    { day: 'Sun', apiCalls: 145000, storage: 2.4, bandwidth: 18.3, connections: 258 },
  ];

  const resourceDistribution = [
    { name: 'API Calls', value: 45, color: '#3B82F6' },
    { name: 'Storage', value: 25, color: '#10B981' },
    { name: 'Bandwidth', value: 20, color: '#F59E0B' },
    { name: 'Connections', value: 10, color: '#EF4444' },
  ];

  const companyConsumption = [
    { company: 'RetailMax Corp', apiCalls: 450000, storage: 0.8, bandwidth: 65.2, cost: 1250 },
    { company: 'TechCorp Solutions', apiCalls: 380000, storage: 0.6, bandwidth: 48.5, cost: 980 },
    { company: 'HealthPlus Medical', apiCalls: 220000, storage: 0.4, bandwidth: 28.3, cost: 650 },
    { company: 'Green Finance', apiCalls: 125000, storage: 0.3, bandwidth: 18.7, cost: 420 },
    { company: 'EduLearn Academy', apiCalls: 75000, storage: 0.3, bandwidth: 12.0, cost: 280 },
  ];

  const monthlyTrends = [
    { month: 'Jan', total: 850000, cost: 3200 },
    { month: 'Feb', total: 920000, cost: 3450 },
    { month: 'Mar', total: 980000, cost: 3680 },
    { month: 'Apr', total: 1050000, cost: 3920 },
    { month: 'May', total: 1180000, cost: 4350 },
    { month: 'Jun', total: 1250000, cost: 4650 },
  ];

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(0)}K`;
    return num.toString();
  };

  return (
    <div className="space-y-6">
      {/* Consumption Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Calls</CardTitle>
            <Activity className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.apiCalls)}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
            <div className="mt-2">
              <Badge variant="outline" className="text-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Within limits
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
            <HardDrive className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.storageUsed} GB</div>
            <p className="text-xs text-muted-foreground">
              Of 10 GB allocated
            </p>
            <div className="mt-2">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ width: `${(stats.storageUsed / 10) * 100}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bandwidth</CardTitle>
            <Wifi className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.bandwidthUsed} GB</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
            <div className="mt-2">
              <Badge variant="outline" className="text-yellow-600">
                <AlertTriangle className="h-3 w-3 mr-1" />
                80% of limit
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Connections</CardTitle>
            <Database className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeConnections}</div>
            <p className="text-xs text-muted-foreground">
              Current connections
            </p>
            <div className="mt-2">
              <Badge variant="outline" className="text-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Healthy
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Daily Consumption */}
        <Card>
          <CardHeader>
            <CardTitle>Daily Resource Consumption</CardTitle>
            <CardDescription>
              API calls and storage usage over the last week
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={dailyConsumption}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'apiCalls' ? formatNumber(value) : `${value}GB`,
                    name === 'apiCalls' ? 'API Calls' : 'Storage'
                  ]}
                />
                <Area
                  type="monotone"
                  dataKey="apiCalls"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Resource Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Resource Distribution</CardTitle>
            <CardDescription>
              Breakdown of resource usage by type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={resourceDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {resourceDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Consumption Trends</CardTitle>
          <CardDescription>
            Total resource consumption and associated costs over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={monthlyTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip 
                formatter={(value, name) => [
                  name === 'total' ? formatNumber(value) : `$${value}`,
                  name === 'total' ? 'Total Usage' : 'Cost'
                ]}
              />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="total"
                stroke="#3B82F6"
                strokeWidth={2}
                name="total"
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="cost"
                stroke="#10B981"
                strokeWidth={2}
                name="cost"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Company Consumption Breakdown */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Consumption by Company</CardTitle>
            <CardDescription>
              Resource usage and costs breakdown by company
            </CardDescription>
          </div>
          <Link href="/backbone/oneid/admin/analytics/consumption">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Detailed View
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {companyConsumption.map((company, index) => (
              <div key={company.company} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                    <span className="text-sm font-semibold text-blue-600">
                      {index + 1}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold">{company.company}</h4>
                    <p className="text-sm text-gray-600">
                      {formatNumber(company.apiCalls)} API calls, {company.storage}GB storage
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">
                    ${company.cost}
                  </div>
                  <div className="text-sm text-gray-500">
                    {company.bandwidth}GB bandwidth
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Consumption Management</CardTitle>
          <CardDescription>
            Tools for monitoring and managing resource consumption
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Link href="/backbone/oneid/admin/analytics/consumption">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <Database className="h-5 w-5 mb-1" />
                Detailed Analytics
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/analytics/reports">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <TrendingUp className="h-5 w-5 mb-1" />
                Usage Reports
              </Button>
            </Link>
            <Button variant="outline" className="w-full h-16 flex flex-col">
              <AlertTriangle className="h-5 w-5 mb-1" />
              Set Alerts
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
