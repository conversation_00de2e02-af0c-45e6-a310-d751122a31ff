'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  X,
  Building2,
  User,
  Shield,
  Zap,
  AlertCircle
} from 'lucide-react';
import CompanyCreationStep from './CompanyCreationStep';
import AdminUserStep from './AdminUserStep';
import PermissionsStep from './PermissionsStep';
import ServiceSelectionStep from './ServiceSelectionStep';
import ReviewStep from './ReviewStep';

interface OnboardingWizardProps {
  onComplete: () => void;
  onCancel: () => void;
}

interface OnboardingData {
  company: {
    name: string;
    displayName: string;
    description: string;
    industry: string;
    website: string;
    email: string;
    phone: string;
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      postalCode: string;
    };
    parentCompanyId?: string;
    logo?: string;
  };
  adminUser: {
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    temporaryPassword: boolean;
    sendWelcomeEmail: boolean;
  };
  permissions: {
    inheritFromParent: {
      passwordPolicy: boolean;
      sessionTimeout: boolean;
      employeeRegistration: boolean;
      emailVerification: boolean;
    };
    allowChildCompanies: boolean;
    maxChildCompanies: number;
    childCompanyPermissions: {
      canCreateSubsidiaries: boolean;
      canManageEmployees: boolean;
      canViewParentData: boolean;
      canInheritSettings: boolean;
    };
    customSettings: {
      passwordPolicy?: any;
      sessionTimeout?: number;
      allowEmployeeRegistration?: boolean;
      requireEmailVerification?: boolean;
    };
  };
  services: {
    selectedServices: string[];
    subscriptionTier: string;
    billingCycle: string;
    autoRenewal: boolean;
  };
}

const steps = [
  {
    id: 'company',
    title: 'Company Information',
    description: 'Basic company details and hierarchy placement',
    icon: Building2
  },
  {
    id: 'admin',
    title: 'Admin User Setup',
    description: 'Create the primary administrator account',
    icon: User
  },
  {
    id: 'permissions',
    title: 'Access & Permissions',
    description: 'Configure access rights and limitations',
    icon: Shield
  },
  {
    id: 'services',
    title: 'Service Selection',
    description: 'Choose available services and features',
    icon: Zap
  },
  {
    id: 'review',
    title: 'Review & Activate',
    description: 'Final review and company activation',
    icon: CheckCircle
  }
];

export default function OnboardingWizard({ onComplete, onCancel }: OnboardingWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    company: {
      name: '',
      displayName: '',
      description: '',
      industry: '',
      website: '',
      email: '',
      phone: '',
      address: {
        street: '',
        city: '',
        state: '',
        country: '',
        postalCode: ''
      }
    },
    adminUser: {
      username: '',
      email: '',
      firstName: '',
      lastName: '',
      temporaryPassword: true,
      sendWelcomeEmail: true
    },
    permissions: {
      inheritFromParent: {
        passwordPolicy: false,
        sessionTimeout: false,
        employeeRegistration: false,
        emailVerification: false
      },
      allowChildCompanies: true,
      maxChildCompanies: 5,
      childCompanyPermissions: {
        canCreateSubsidiaries: false,
        canManageEmployees: true,
        canViewParentData: false,
        canInheritSettings: true
      },
      customSettings: {}
    },
    services: {
      selectedServices: ['oneid-auth', 'admin-portal'],
      subscriptionTier: 'standard',
      billingCycle: 'monthly',
      autoRenewal: true
    }
  });

  const progressPercentage = (currentStep / (steps.length - 1)) * 100;

  const updateOnboardingData = (section: keyof OnboardingData, data: any) => {
    setOnboardingData(prev => ({
      ...prev,
      [section]: { ...prev[section], ...data }
    }));
    setErrors([]);
  };

  const validateCurrentStep = (): boolean => {
    const newErrors: string[] = [];

    switch (currentStep) {
      case 0: // Company Information
        if (!onboardingData.company.name) newErrors.push('Company name is required');
        if (!onboardingData.company.email) newErrors.push('Company email is required');
        if (!onboardingData.company.industry) newErrors.push('Industry is required');
        break;
      
      case 1: // Admin User
        if (!onboardingData.adminUser.username) newErrors.push('Username is required');
        if (!onboardingData.adminUser.email) newErrors.push('Email is required');
        if (!onboardingData.adminUser.firstName) newErrors.push('First name is required');
        if (!onboardingData.adminUser.lastName) newErrors.push('Last name is required');
        break;
      
      case 2: // Permissions
        // Permissions validation is optional as defaults are provided
        break;
      
      case 3: // Services
        if (onboardingData.services.selectedServices.length === 0) {
          newErrors.push('At least one service must be selected');
        }
        break;
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (!validateCurrentStep()) return;

    setIsSubmitting(true);
    try {
      // Here you would call the API to create the company
      const response = await fetch('/api/backbone/oneid/company-hierarchy/create-child', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          parentCompanyId: onboardingData.company.parentCompanyId,
          companyInfo: onboardingData.company,
          adminUser: onboardingData.adminUser,
          inheritSettings: onboardingData.permissions.inheritFromParent,
          customSettings: onboardingData.permissions.customSettings
        }),
      });

      if (response.ok) {
        onComplete();
      } else {
        const error = await response.json();
        setErrors([error.error?.message || 'Failed to create company']);
      }
    } catch (error) {
      setErrors(['Network error occurred. Please try again.']);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <CompanyCreationStep
            data={onboardingData.company}
            onChange={(data) => updateOnboardingData('company', data)}
          />
        );
      case 1:
        return (
          <AdminUserStep
            data={onboardingData.adminUser}
            onChange={(data) => updateOnboardingData('adminUser', data)}
          />
        );
      case 2:
        return (
          <PermissionsStep
            data={onboardingData.permissions}
            parentCompany={onboardingData.company.parentCompanyId}
            onChange={(data) => updateOnboardingData('permissions', data)}
          />
        );
      case 3:
        return (
          <ServiceSelectionStep
            data={onboardingData.services}
            onChange={(data) => updateOnboardingData('services', data)}
          />
        );
      case 4:
        return (
          <ReviewStep
            data={onboardingData}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Company Onboarding</h2>
            <p className="text-gray-600">Step {currentStep + 1} of {steps.length}</p>
          </div>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Progress */}
        <div className="px-6 py-4 border-b bg-gray-50">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              {steps[currentStep].title}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progressPercentage)}% Complete
            </span>
          </div>
          <Progress value={progressPercentage} className="w-full" />
          
          {/* Step indicators */}
          <div className="flex justify-between mt-4">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center space-x-2 ${
                  index <= currentStep ? 'text-blue-600' : 'text-gray-400'
                }`}
              >
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    index < currentStep
                      ? 'bg-blue-600 text-white'
                      : index === currentStep
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-200 text-gray-400'
                  }`}
                >
                  {index < currentStep ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <step.icon className="h-4 w-4" />
                  )}
                </div>
                <span className="text-xs font-medium hidden sm:block">
                  {step.title}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Errors */}
          {errors.length > 0 && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="font-medium text-red-800">Please fix the following errors:</span>
              </div>
              <ul className="list-disc list-inside text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Step Content */}
          {renderCurrentStep()}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-gray-50">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex space-x-2">
            <Button variant="ghost" onClick={onCancel}>
              Cancel
            </Button>
            {currentStep < steps.length - 1 ? (
              <Button onClick={handleNext}>
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button 
                onClick={handleSubmit} 
                disabled={isSubmitting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? 'Creating...' : 'Create Company'}
                <CheckCircle className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
