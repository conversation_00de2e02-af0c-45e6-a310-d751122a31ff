'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  User,
  Shield,
  Zap,
  CheckCircle,
  Mail,
  Phone,
  MapPin,
  Globe,
  Users,
  DollarSign,
  AlertCircle
} from 'lucide-react';

interface ReviewStepProps {
  data: any;
  onSubmit: () => void;
  isSubmitting: boolean;
}

export default function ReviewStep({ data, onSubmit, isSubmitting }: ReviewStepProps) {
  const formatAddress = (address: any) => {
    if (!address) return 'Not provided';
    const parts = [
      address.street,
      address.city,
      address.state,
      address.postalCode,
      address.country
    ].filter(Boolean);
    return parts.join(', ') || 'Not provided';
  };

  const getSelectedServices = () => {
    const serviceNames: Record<string, string> = {
      'oneid-auth': 'OneID Authentication',
      'admin-portal': 'Admin Management Portal',
      'analytics-suite': 'Analytics Suite',
      'api-gateway': 'API Gateway',
      'file-storage': 'File Storage Service',
      'notification-service': 'Notification Service',
      'document-management': 'Document Management',
      'backup-service': 'Backup & Recovery'
    };
    
    return data.services.selectedServices.map((id: string) => serviceNames[id] || id);
  };

  return (
    <div className="space-y-6">
      {/* Review Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span>Review & Confirm</span>
          </CardTitle>
          <CardDescription>
            Please review all information before creating the company
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">Important</span>
            </div>
            <p className="text-sm text-blue-800">
              Once you create the company, some settings may require administrative approval to change. 
              Please ensure all information is correct before proceeding.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Company Information Review */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5" />
            <span>Company Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium text-gray-700">Company Name</h4>
              <p className="text-lg font-semibold">{data.company.name}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Display Name</h4>
              <p>{data.company.displayName || 'Same as company name'}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Industry</h4>
              <p className="capitalize">{data.company.industry}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Website</h4>
              <p>{data.company.website || 'Not provided'}</p>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-700 mb-2">Description</h4>
            <p className="text-gray-600">{data.company.description || 'No description provided'}</p>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium text-gray-700 flex items-center space-x-1">
                <Mail className="h-4 w-4" />
                <span>Email</span>
              </h4>
              <p>{data.company.email}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 flex items-center space-x-1">
                <Phone className="h-4 w-4" />
                <span>Phone</span>
              </h4>
              <p>{data.company.phone || 'Not provided'}</p>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-700 flex items-center space-x-1 mb-2">
              <MapPin className="h-4 w-4" />
              <span>Address</span>
            </h4>
            <p className="text-gray-600">{formatAddress(data.company.address)}</p>
          </div>

          {data.company.parentCompanyId && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Hierarchy</h4>
              <Badge variant="outline">
                Child Company of {data.company.parentCompanyId}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Admin User Review */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Administrator Account</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium text-gray-700">Full Name</h4>
              <p className="text-lg">{data.adminUser.firstName} {data.adminUser.lastName}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Username</h4>
              <p className="font-mono">{data.adminUser.username}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Email</h4>
              <p>{data.adminUser.email}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Phone</h4>
              <p>{data.adminUser.phone || 'Not provided'}</p>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            {data.adminUser.temporaryPassword && (
              <Badge variant="default" className="bg-blue-100 text-blue-800">
                Temporary Password
              </Badge>
            )}
            {data.adminUser.sendWelcomeEmail && (
              <Badge variant="default" className="bg-green-100 text-green-800">
                Welcome Email
              </Badge>
            )}
            <Badge variant="default" className="bg-purple-100 text-purple-800">
              Company Admin
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Review */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Permissions & Settings</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Child Companies</h4>
              <div className="space-y-1">
                <Badge variant={data.permissions.allowChildCompanies ? "default" : "secondary"}>
                  {data.permissions.allowChildCompanies ? "Allowed" : "Not Allowed"}
                </Badge>
                {data.permissions.allowChildCompanies && (
                  <p className="text-sm text-gray-600">
                    Maximum: {data.permissions.maxChildCompanies} companies
                  </p>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Access Control</h4>
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <Badge variant={data.permissions.customSettings.allowEmployeeRegistration !== false ? "default" : "secondary"}>
                    Employee Registration
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={data.permissions.customSettings.requireEmailVerification !== false ? "default" : "secondary"}>
                    Email Verification
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {data.company.parentCompanyId && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Inherited Settings</h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(data.permissions.inheritFromParent).map(([key, value]) => (
                  value && (
                    <Badge key={key} variant="outline">
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Badge>
                  )
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Services Review */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Selected Services</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Services</h4>
            <div className="flex flex-wrap gap-2">
              {getSelectedServices().map((service, index) => (
                <Badge key={index} variant="default">
                  {service}
                </Badge>
              ))}
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <h4 className="font-medium text-gray-700">Subscription Tier</h4>
              <p className="capitalize">{data.services.subscriptionTier}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Billing Cycle</h4>
              <p className="capitalize">{data.services.billingCycle}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Auto Renewal</h4>
              <Badge variant={data.services.autoRenewal ? "default" : "secondary"}>
                {data.services.autoRenewal ? "Enabled" : "Disabled"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Final Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5" />
            <span>Ready to Create</span>
          </CardTitle>
          <CardDescription>
            Click the button below to create your company
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">What happens next?</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Company will be created in the system</li>
                <li>• Administrator account will be set up</li>
                <li>• Selected services will be activated</li>
                <li>• Welcome email will be sent (if enabled)</li>
                <li>• Company portal access will be granted</li>
              </ul>
            </div>

            <Button 
              onClick={onSubmit} 
              disabled={isSubmitting}
              className="w-full bg-green-600 hover:bg-green-700"
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating Company...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Create Company
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
