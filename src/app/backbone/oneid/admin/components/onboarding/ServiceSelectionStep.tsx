'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Zap,
  Shield,
  Users,
  BarChart3,
  Database,
  Mail,
  FileText,
  Cloud,
  DollarSign,
  CheckCircle,
  Star
} from 'lucide-react';

interface ServiceSelectionStepProps {
  data: any;
  onChange: (data: any) => void;
}

const availableServices = [
  {
    id: 'oneid-auth',
    name: 'OneID Authentication',
    description: 'Core authentication and user management system',
    icon: Shield,
    required: true,
    price: 0,
    features: ['Single Sign-On', 'User Management', 'Session Control', 'Magic Link Auth']
  },
  {
    id: 'admin-portal',
    name: 'Admin Management Portal',
    description: 'Comprehensive company and user management interface',
    icon: Users,
    required: true,
    price: 0,
    features: ['Company Dashboard', 'User Assignment', 'Hierarchy Management', 'Analytics']
  },
  {
    id: 'analytics-suite',
    name: 'Analytics Suite',
    description: 'Advanced analytics and reporting capabilities',
    icon: BarChart3,
    required: false,
    price: 49,
    features: ['Custom Reports', 'Real-time Analytics', 'Data Export', 'API Metrics']
  },
  {
    id: 'api-gateway',
    name: 'API Gateway',
    description: 'Secure API management and rate limiting',
    icon: Zap,
    required: false,
    price: 29,
    features: ['Rate Limiting', 'API Keys', 'Request Logging', 'Security Policies']
  },
  {
    id: 'file-storage',
    name: 'File Storage Service',
    description: 'Secure cloud file storage and management',
    icon: Database,
    required: false,
    price: 19,
    features: ['10GB Storage', 'File Sharing', 'Version Control', 'Backup & Recovery']
  },
  {
    id: 'notification-service',
    name: 'Notification Service',
    description: 'Email and SMS notification system',
    icon: Mail,
    required: false,
    price: 15,
    features: ['Email Templates', 'SMS Notifications', 'Delivery Tracking', 'Automation']
  },
  {
    id: 'document-management',
    name: 'Document Management',
    description: 'Document creation, storage, and collaboration',
    icon: FileText,
    required: false,
    price: 39,
    features: ['Document Editor', 'Collaboration', 'Templates', 'Digital Signatures']
  },
  {
    id: 'backup-service',
    name: 'Backup & Recovery',
    description: 'Automated backup and disaster recovery',
    icon: Cloud,
    required: false,
    price: 25,
    features: ['Daily Backups', 'Point-in-time Recovery', 'Cross-region Backup', 'Monitoring']
  }
];

const subscriptionTiers = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for small companies',
    discount: 0,
    features: ['Up to 25 users', 'Basic support', 'Standard features']
  },
  {
    id: 'standard',
    name: 'Standard',
    description: 'Great for growing businesses',
    discount: 10,
    features: ['Up to 100 users', 'Priority support', 'Advanced features', '10% discount']
  },
  {
    id: 'premium',
    name: 'Premium',
    description: 'For large organizations',
    discount: 20,
    features: ['Unlimited users', '24/7 support', 'All features', '20% discount']
  }
];

export default function ServiceSelectionStep({ data, onChange }: ServiceSelectionStepProps) {
  const handleServiceToggle = (serviceId: string, checked: boolean) => {
    const currentServices = data.selectedServices || [];
    const newServices = checked
      ? [...currentServices, serviceId]
      : currentServices.filter((id: string) => id !== serviceId);
    
    onChange({
      ...data,
      selectedServices: newServices
    });
  };

  const handleFieldChange = (field: string, value: string | boolean) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  const calculateTotal = () => {
    const selectedServiceIds = data.selectedServices || [];
    const baseTotal = selectedServiceIds.reduce((total: number, serviceId: string) => {
      const service = availableServices.find(s => s.id === serviceId);
      return total + (service?.price || 0);
    }, 0);

    const tier = subscriptionTiers.find(t => t.id === data.subscriptionTier);
    const discount = tier?.discount || 0;
    const discountAmount = (baseTotal * discount) / 100;
    
    return {
      baseTotal,
      discount: discountAmount,
      finalTotal: baseTotal - discountAmount
    };
  };

  const totals = calculateTotal();

  return (
    <div className="space-y-6">
      {/* Service Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Available Services</span>
          </CardTitle>
          <CardDescription>
            Choose the services you want to enable for your company
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            {availableServices.map((service) => {
              const isSelected = data.selectedServices?.includes(service.id) || service.required;
              const IconComponent = service.icon;
              
              return (
                <div
                  key={service.id}
                  className={`p-4 border rounded-lg ${
                    isSelected ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => handleServiceToggle(service.id, checked)}
                        disabled={service.required}
                      />
                      <div className={`p-2 rounded-lg ${
                        isSelected ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        <IconComponent className={`h-5 w-5 ${
                          isSelected ? 'text-blue-600' : 'text-gray-600'
                        }`} />
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-semibold">{service.name}</h4>
                        {service.required && (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            Required
                          </Badge>
                        )}
                        {service.price === 0 && (
                          <Badge variant="outline">Free</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{service.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {service.features.map((feature, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="font-semibold">
                        {service.price === 0 ? 'Free' : `$${service.price}/mo`}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Subscription Tier */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5" />
            <span>Subscription Tier</span>
          </CardTitle>
          <CardDescription>
            Choose your subscription tier for additional benefits
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            {subscriptionTiers.map((tier) => (
              <div
                key={tier.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  data.subscriptionTier === tier.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleFieldChange('subscriptionTier', tier.id)}
              >
                <div className="text-center">
                  <h4 className="font-semibold mb-1">{tier.name}</h4>
                  <p className="text-sm text-gray-600 mb-3">{tier.description}</p>
                  {tier.discount > 0 && (
                    <Badge variant="default" className="bg-green-100 text-green-800 mb-2">
                      {tier.discount}% OFF
                    </Badge>
                  )}
                  <ul className="text-xs space-y-1">
                    {tier.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-1">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Billing Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Billing Options</span>
          </CardTitle>
          <CardDescription>
            Configure billing and payment preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="billingCycle">Billing Cycle</Label>
            <Select value={data.billingCycle} onValueChange={(value) => handleFieldChange('billingCycle', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select billing cycle" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly (5% discount)</SelectItem>
                <SelectItem value="annually">Annually (15% discount)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="autoRenewal"
              checked={data.autoRenewal}
              onCheckedChange={(checked) => handleFieldChange('autoRenewal', checked)}
            />
            <Label htmlFor="autoRenewal">Enable auto-renewal</Label>
          </div>
        </CardContent>
      </Card>

      {/* Cost Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Cost Summary</span>
          </CardTitle>
          <CardDescription>
            Monthly cost breakdown for selected services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.selectedServices?.map((serviceId: string) => {
              const service = availableServices.find(s => s.id === serviceId);
              if (!service) return null;
              
              return (
                <div key={serviceId} className="flex justify-between items-center">
                  <span className="text-sm">{service.name}</span>
                  <span className="font-medium">
                    {service.price === 0 ? 'Free' : `$${service.price}`}
                  </span>
                </div>
              );
            })}
            
            <div className="border-t pt-3">
              <div className="flex justify-between items-center">
                <span>Subtotal</span>
                <span className="font-medium">${totals.baseTotal}</span>
              </div>
              
              {totals.discount > 0 && (
                <div className="flex justify-between items-center text-green-600">
                  <span>Tier Discount</span>
                  <span>-${totals.discount.toFixed(2)}</span>
                </div>
              )}
              
              <div className="flex justify-between items-center text-lg font-bold border-t pt-2">
                <span>Monthly Total</span>
                <span>${totals.finalTotal.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
