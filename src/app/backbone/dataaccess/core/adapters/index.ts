/**
 * Database adapters for the unified database access layer
 */

export * from './BaseAdapter';
export * from './JsonAdapter';
export * from './PostgreSQLAdapter';

import { DatabaseAdapter, StorageType, ConnectionConfig } from '../interfaces';
import { JsonAdapter } from './JsonAdapter';
import { PostgreSQLAdapter } from './PostgreSQLAdapter';

/**
 * Factory function to create database adapters
 */
export function createAdapter(type: StorageType, config?: ConnectionConfig): DatabaseAdapter {
  switch (type) {
    case 'json':
      return new JsonAdapter(config);
    
    case 'mongodb':
      return new MongoAdapter(config);
    
    case 'postgresql':
      return new PostgreSQLAdapter(config);
    
    case 'sqlite':
      // TODO: Implement SQLite adapter
      throw new Error('SQLite adapter not yet implemented');
    
    case 'memory':
      // TODO: Implement Memory adapter
      throw new Error('Memory adapter not yet implemented');
    
    default:
      throw new Error(`Unsupported storage type: ${type}`);
  }
}

/**
 * Get list of available adapter types
 */
export function getAvailableAdapterTypes(): StorageType[] {
  return ['json', 'mongodb', 'postgresql']; // Add 'sqlite', 'memory' when implemented
}

/**
 * Check if an adapter type is available
 */
export function isAdapterTypeAvailable(type: StorageType): boolean {
  return getAvailableAdapterTypes().includes(type);
}

/**
 * Get adapter information
 */
export function getAdapterInfo(type: StorageType): { name: string; description: string; features: string[] } {
  const adapterInfo: Record<StorageType, { name: string; description: string; features: string[] }> = {
    json: {
      name: 'JSON File Adapter',
      description: 'File-based storage using JSON files',
      features: ['Simple setup', 'No external dependencies', 'Human readable', 'Version control friendly']
    },
    mongodb: {
      name: 'MongoDB Adapter',
      description: 'Document database for complex data structures',
      features: ['Schema flexibility', 'Horizontal scaling', 'Rich queries', 'Aggregation pipeline']
    },
    postgresql: {
      name: 'PostgreSQL Adapter',
      description: 'Relational database for structured data',
      features: ['ACID compliance', 'Complex queries', 'Data integrity', 'Performance optimization']
    },
    sqlite: {
      name: 'SQLite Adapter',
      description: 'Embedded database for local storage',
      features: ['Zero configuration', 'Single file', 'ACID compliance', 'Cross-platform']
    },
    memory: {
      name: 'Memory Adapter',
      description: 'In-memory storage for testing and caching',
      features: ['Ultra fast', 'No persistence', 'Testing friendly', 'Cache layer']
    }
  };
  
  return adapterInfo[type] || { name: 'Unknown', description: 'Unknown adapter type', features: [] };
}

/**
 * Validate adapter configuration
 */
export function validateAdapterConfig(type: StorageType, config: ConnectionConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  switch (type) {
    case 'json':
      if (!config.path) {
        errors.push('JSON adapter requires a path configuration');
      }
      break;
    
    case 'mongodb':
      if (!config.uri && (!config.host || !config.database)) {
        errors.push('MongoDB adapter requires either uri or host+database configuration');
      }
      break;
    
    case 'postgresql':
      if (!config.uri && (!config.host || !config.database)) {
        errors.push('PostgreSQL adapter requires either uri or host+database configuration');
      }
      break;
    
    case 'sqlite':
      if (!config.path) {
        errors.push('SQLite adapter requires a path configuration');
      }
      break;
    
    case 'memory':
      // Memory adapter doesn't require specific configuration
      break;
    
    default:
      errors.push(`Unknown adapter type: ${type}`);
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Get recommended configuration for an adapter type
 */
export function getRecommendedConfig(type: StorageType, appName: string): ConnectionConfig {
  const configs: Record<StorageType, ConnectionConfig> = {
    json: {
      path: `/data/apps/backbone/dataaccess/${appName}`
    },
    mongodb: {
      uri: `mongodb://localhost:27017/${appName}`,
      host: 'localhost',
      port: 27017,
      database: appName
    },
    postgresql: {
      uri: `postgresql://postgres:password@localhost:5432/${appName}`,
      host: 'localhost',
      port: 5432,
      database: appName,
      username: 'postgres',
      password: ''
    },
    sqlite: {
      path: `/data/apps/backbone/dataaccess/${appName}/${appName}.db`
    },
    memory: {}
  };
  
  return configs[type] || {};
}
