import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join, dirname } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { path, data } = body;

    if (!path || !data) {
      return NextResponse.json(
        { error: 'Path and data are required' },
        { status: 400 }
      );
    }

    // Security check: ensure path is within allowed directories
    const allowedPaths = [
      'data/apps/platforms/',
      'data/apps/backbone/'
    ];

    const isAllowed = allowedPaths.some(allowedPath => 
      path.startsWith(allowedPath)
    );

    if (!isAllowed) {
      return NextResponse.json(
        { error: 'Access denied to this path' },
        { status: 403 }
      );
    }

    // Validate JSON data
    let jsonString: string;
    try {
      jsonString = JSON.stringify(data, null, 2);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid JSON data provided' },
        { status: 422 }
      );
    }

    const fullPath = join(process.cwd(), path);
    const dirPath = dirname(fullPath);

    // Ensure directory exists
    if (!existsSync(dirPath)) {
      await mkdir(dirPath, { recursive: true });
    }

    // Create backup of existing file
    if (existsSync(fullPath)) {
      const backupPath = `${fullPath}.backup.${Date.now()}`;
      try {
        const existingData = await import('fs/promises').then(fs => fs.readFile(fullPath, 'utf-8'));
        await writeFile(backupPath, existingData);
      } catch (backupError) {
        console.warn('Failed to create backup:', backupError);
      }
    }

    // Write the new data
    await writeFile(fullPath, jsonString, 'utf-8');

    return NextResponse.json({ 
      success: true,
      message: 'Object data saved successfully'
    });

  } catch (error) {
    console.error('Error saving object data:', error);
    return NextResponse.json(
      { error: 'Failed to save object data' },
      { status: 500 }
    );
  }
}