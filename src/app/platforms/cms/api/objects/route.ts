import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');

    if (!path) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      );
    }

    // Security check: ensure path is within allowed directories
    const allowedPaths = [
      'data/apps/platforms/',
      'data/apps/backbone/'
    ];

    const isAllowed = allowedPaths.some(allowedPath => 
      path.startsWith(allowedPath)
    );

    if (!isAllowed) {
      return NextResponse.json(
        { error: 'Access denied to this path' },
        { status: 403 }
      );
    }

    const fullPath = join(process.cwd(), path);
    const data = await readFile(fullPath, 'utf-8');
    const jsonData = JSON.parse(data);

    return NextResponse.json(jsonData);
  } catch (error) {
    console.error('Error reading object data:', error);
    
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: 'Invalid JSON format in file' },
        { status: 422 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to read object data' },
      { status: 500 }
    );
  }
}