# Content Management System (CMS)

A universal CMS platform for managing JSON assets across the ABN.GREEN ecosystem with rule-based access control integrated with OneID authentication.

## Features

- **Rule-Based Access Control**: Sophisticated permission system with user assignments and role-based rules
- **OneID Integration**: Seamless authentication using the existing OneID system
- **JSON Asset Management**: View and edit JSON objects with validation and backup
- **Multi-Tenant Support**: Department-based filtering and access control
- **Real-time Editing**: Direct JSON editing with syntax validation
- **Audit Trail**: Automatic backup creation on file modifications

## Architecture

### Directory Structure
```
src/app/platforms/cms/
├── components/           # React components
│   ├── CMSDashboard.tsx # Main dashboard component
│   ├── ObjectList.tsx   # List of accessible objects
│   └── ObjectEditor.tsx # JSON editor interface
├── services/            # Business logic
│   ├── AccessControlService.ts # Permission checking
│   └── CMSDataService.ts      # Data management
├── types/               # TypeScript definitions
│   └── index.ts        # Core interfaces
├── api/                # API routes
│   ├── objects/        # Object data retrieval
│   └── save-object/    # Object data persistence
├── layout.tsx          # Layout with OneID integration
├── page.tsx           # Main page component
└── README.md          # This documentation
```

### Data Structure
```
data/apps/platforms/cms/
├── rules/              # Access control rules
│   └── cms-rules.json # Rule definitions
├── users/             # User management
│   └── cms-users.json # User profiles (demo data)
├── objects/           # Object registry
│   └── sample-objects.json # Available objects
└── assignments/       # User-object assignments
    └── user-assignments.json # Assignment mappings
```

## Access Control System

### Rule Types

1. **Admin Full Access**: Administrators have complete access to all objects
2. **User Object Access**: Users can access only assigned objects
3. **Department Data Access**: Managers can access department-specific objects
4. **Read Only Access**: Viewers have read-only permissions

### Permission Levels

- **read**: View object data
- **write**: Modify object data
- **delete**: Remove objects (future feature)

### Rule Evaluation

The system evaluates access in this order:
1. Direct user assignments (highest priority)
2. Role-based rules with conditions
3. Department-based filtering
4. Default deny (lowest priority)

## OneID Integration

### User Mapping

OneID users are automatically mapped to CMS users:

```typescript
OneID UserType → CMS Role
├── company_admin → admin
├── employee → user
├── individual → viewer
└── default → user
```

### Authentication Flow

1. User logs in via OneID
2. User data is mapped to CMS structure
3. Access control rules are evaluated
4. Available objects are filtered based on permissions

## API Routes

### GET /platforms/cms/api/objects
Retrieve object data with security validation.

**Parameters:**
- `path`: File path to JSON object

**Security:**
- Path must start with `data/apps/platforms/` or `data/apps/backbone/`
- Returns 403 for unauthorized paths

### POST /platforms/cms/api/save-object
Save object data with backup creation.

**Body:**
```json
{
  "path": "data/apps/platforms/example/data.json",
  "data": { ... }
}
```

**Features:**
- JSON validation
- Automatic backup creation
- Directory creation if needed

## Usage

### Accessing the CMS

1. Navigate to `/platforms/cms`
2. Log in via OneID if not authenticated
3. View your assigned objects in the dashboard
4. Click on an object to view/edit its data

### Managing Objects

1. **Viewing**: Click on any object card to open the editor
2. **Editing**: Click "Edit" button (requires write permission)
3. **Saving**: Click "Save" after making changes
4. **Validation**: JSON syntax is validated before saving

### Adding New Objects

To add new objects to the CMS:

1. Add object definition to `data/apps/platforms/cms/objects/sample-objects.json`
2. Create user assignment in `data/apps/platforms/cms/assignments/user-assignments.json`
3. Or define access rules in `data/apps/platforms/cms/rules/cms-rules.json`

## Example Configurations

### Object Definition
```json
{
  "id": "obj-004",
  "name": "Customer Data",
  "type": "customers",
  "path": "data/apps/platforms/retail/customers.json",
  "description": "Customer management data",
  "metadata": {
    "department": "Sales",
    "lastModified": "2025-07-20T00:00:00Z",
    "size": "156KB"
  },
  "tags": ["customers", "sales", "retail"]
}
```

### Access Rule
```json
{
  "id": "rule-005",
  "name": "Sales Team Access",
  "description": "Sales team can access customer data",
  "conditions": {
    "user.department": "Sales"
  },
  "permissions": ["read", "write"],
  "objectFilters": {
    "type": "customers"
  }
}
```

### User Assignment
```json
{
  "id": "assign-004",
  "userId": "user-004",
  "objectId": "obj-004",
  "permissions": ["read", "write"],
  "assignedBy": "user-001",
  "assignedAt": "2025-07-20T00:00:00Z",
  "status": "active"
}
```

## Security Features

- **Path Validation**: Prevents access to unauthorized directories
- **JSON Validation**: Ensures data integrity
- **Backup Creation**: Automatic backups before modifications
- **Permission Checking**: Granular access control
- **Session Management**: OneID integration for secure sessions

## Development

### Running the CMS

```bash
npm run dev
```

Navigate to `http://localhost:3000/platforms/cms`

### Testing Access Control

1. Modify user data in OneID
2. Update CMS rules or assignments
3. Test different permission scenarios
4. Verify object filtering works correctly

## Future Enhancements

- **Object History**: Track all changes with timestamps
- **Bulk Operations**: Multi-object editing capabilities
- **Advanced Search**: Full-text search across objects
- **Schema Validation**: Custom JSON schemas for different object types
- **Workflow Approval**: Review process for sensitive changes
- **Real-time Collaboration**: Multiple users editing simultaneously