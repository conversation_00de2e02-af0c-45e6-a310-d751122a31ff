export interface CMSUser {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'manager' | 'user' | 'viewer';
  department: string;
  status: 'active' | 'inactive';
  createdAt: string;
}

export interface CMSObject {
  id: string;
  name: string;
  type: string;
  path: string;
  description: string;
  metadata: {
    department?: string;
    lastModified: string;
    size: string;
    [key: string]: any;
  };
  tags: string[];
}

export interface CMSRule {
  id: string;
  name: string;
  description: string;
  conditions: {
    [key: string]: any;
  };
  permissions: ('read' | 'write' | 'delete')[];
  objectFilters: {
    all?: boolean;
    assignedToUser?: boolean;
    department?: string;
    [key: string]: any;
  };
}

export interface CMSAssignment {
  id: string;
  userId: string;
  objectId: string;
  permissions: ('read' | 'write' | 'delete')[];
  assignedBy: string;
  assignedAt: string;
  status: 'active' | 'inactive';
}

export interface AccessResult {
  hasAccess: boolean;
  permissions: ('read' | 'write' | 'delete')[];
  reason: string;
}

export interface CMSData {
  version: string;
  [key: string]: any;
}