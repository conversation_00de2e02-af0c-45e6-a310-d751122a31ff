'use client';

import React from 'react';
import { UniversalOneIDProvider } from '@/app/backbone/oneid/hooks/useUniversalAuth.client';

export default function CMSLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <UniversalOneIDProvider>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <h1 className="text-2xl font-bold text-gray-900">Content Management System</h1>
            </div>
          </div>
        </header>
        <main>
          {children}
        </main>
      </div>
    </UniversalOneIDProvider>
  );
}