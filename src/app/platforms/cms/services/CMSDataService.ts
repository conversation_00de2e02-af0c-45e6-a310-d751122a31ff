import { CMS<PERSON>ser, CMS<PERSON>bject, CMSRule, CMSAssignment, CMSData } from '../types';
import { AccessControlService } from './AccessControlService';

export class CMSDataService {
  private basePath = '/data/apps/platforms/cms';
  private accessControl: AccessControlService;

  constructor() {
    this.accessControl = new AccessControlService([], []);
    this.loadData();
  }

  private async loadData() {
    try {
      const [rules, assignments] = await Promise.all([
        this.loadRules(),
        this.loadAssignments()
      ]);
      this.accessControl = new AccessControlService(rules, assignments);
    } catch (error) {
      console.error('Failed to load CMS data:', error);
    }
  }

  async loadRules(): Promise<CMSRule[]> {
    try {
      const response = await fetch(`${this.basePath}/rules/cms-rules.json`);
      const data = await response.json();
      return data.rules || [];
    } catch (error) {
      console.error('Failed to load rules:', error);
      return [];
    }
  }

  async loadUsers(): Promise<CMSUser[]> {
    try {
      const response = await fetch(`${this.basePath}/users/cms-users.json`);
      const data = await response.json();
      return data.users || [];
    } catch (error) {
      console.error('Failed to load users:', error);
      return [];
    }
  }

  async loadObjects(): Promise<CMSObject[]> {
    try {
      const response = await fetch(`${this.basePath}/objects/sample-objects.json`);
      const data = await response.json();
      return data.objects || [];
    } catch (error) {
      console.error('Failed to load objects:', error);
      return [];
    }
  }

  async loadAssignments(): Promise<CMSAssignment[]> {
    try {
      const response = await fetch(`${this.basePath}/assignments/user-assignments.json`);
      const data = await response.json();
      return data.assignments || [];
    } catch (error) {
      console.error('Failed to load assignments:', error);
      return [];
    }
  }

  async getUserAccessibleObjects(user: CMSUser): Promise<Array<CMSObject & { permissions: string[] }>> {
    const objects = await this.loadObjects();
    return this.accessControl.getUserAccessibleObjects(user, objects);
  }

  async loadObjectData(path: string): Promise<CMSData | null> {
    try {
      const response = await fetch(`/platforms/cms/api/objects?path=${encodeURIComponent(path)}`);
      if (!response.ok) {
        throw new Error(`Failed to load object data: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to load object data:', error);
      return null;
    }
  }

  async saveObjectData(path: string, data: CMSData): Promise<boolean> {
    try {
      const response = await fetch('/platforms/cms/api/save-object', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ path, data }),
      });
      
      return response.ok;
    } catch (error) {
      console.error('Failed to save object data:', error);
      return false;
    }
  }

  checkUserAccess(user: CMSUser, object: CMSObject) {
    return this.accessControl.checkAccess(user, object);
  }

  hasPermission(user: CMSUser, object: CMSObject, permission: 'read' | 'write' | 'delete'): boolean {
    return this.accessControl.hasPermission(user, object, permission);
  }
}