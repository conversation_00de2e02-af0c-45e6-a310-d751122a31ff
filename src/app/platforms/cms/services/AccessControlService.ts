import { CMS<PERSON><PERSON>, CMSObject, CMSRule, CMSAssignment, AccessResult } from '../types';

export class AccessControlService {
  private rules: CMSRule[] = [];
  private assignments: CMSAssignment[] = [];

  constructor(rules: CMSRule[], assignments: CMSAssignment[]) {
    this.rules = rules;
    this.assignments = assignments;
  }

  checkAccess(user: CMSU<PERSON>, object: CMSObject): AccessResult {
    // Check user assignments first
    const userAssignment = this.assignments.find(
      assignment => 
        assignment.userId === user.id && 
        assignment.objectId === object.id && 
        assignment.status === 'active'
    );

    if (userAssignment) {
      return {
        hasAccess: true,
        permissions: userAssignment.permissions,
        reason: 'Direct assignment'
      };
    }

    // Check rules
    for (const rule of this.rules) {
      if (this.evaluateRuleConditions(rule, user)) {
        if (this.evaluateObjectFilters(rule, user, object)) {
          return {
            hasAccess: true,
            permissions: rule.permissions,
            reason: `Rule: ${rule.name}`
          };
        }
      }
    }

    return {
      hasAccess: false,
      permissions: [],
      reason: 'No matching rules or assignments'
    };
  }

  private evaluateRuleConditions(rule: CMSRule, user: CMSUser): boolean {
    for (const [key, value] of Object.entries(rule.conditions)) {
      const userValue = this.getNestedProperty(user, key);
      if (userValue !== value) {
        return false;
      }
    }
    return true;
  }

  private evaluateObjectFilters(rule: CMSRule, user: CMSUser, object: CMSObject): boolean {
    const filters = rule.objectFilters;

    // Check if rule applies to all objects
    if (filters.all) {
      return true;
    }

    // Check if object is assigned to user
    if (filters.assignedToUser) {
      const hasAssignment = this.assignments.some(
        assignment => 
          assignment.userId === user.id && 
          assignment.objectId === object.id && 
          assignment.status === 'active'
      );
      if (hasAssignment) {
        return true;
      }
    }

    // Check department filter
    if (filters.department) {
      const departmentFilter = this.interpolateVariables(filters.department, user);
      if (object.metadata.department === departmentFilter) {
        return true;
      }
    }

    // If assignedToUser filter is set but no assignment found, return false
    if (filters.assignedToUser && !filters.all && !filters.department) {
      return false;
    }

    return false;
  }

  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private interpolateVariables(template: string, user: CMSUser): string {
    return template.replace(/\{([^}]+)\}/g, (match, path) => {
      return this.getNestedProperty(user, path) || match;
    });
  }

  getUserAccessibleObjects(user: CMSUser, objects: CMSObject[]): Array<CMSObject & { permissions: string[] }> {
    const accessibleObjects: Array<CMSObject & { permissions: string[] }> = [];

    for (const object of objects) {
      const access = this.checkAccess(user, object);
      if (access.hasAccess) {
        accessibleObjects.push({
          ...object,
          permissions: access.permissions
        });
      }
    }

    return accessibleObjects;
  }

  hasPermission(user: CMSUser, object: CMSObject, permission: 'read' | 'write' | 'delete'): boolean {
    const access = this.checkAccess(user, object);
    return access.hasAccess && access.permissions.includes(permission);
  }
}