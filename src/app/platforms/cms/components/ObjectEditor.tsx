'use client';

import React, { useState, useEffect } from 'react';
import { CMSUser, CMSObject, CMSData } from '../types';
import { CMSDataService } from '../services/CMSDataService';

interface ObjectEditorProps {
  object: CMSObject;
  user: CMSUser;
  onClose: () => void;
  cmsService: CMSDataService;
}

export function ObjectEditor({ object, user, onClose, cmsService }: ObjectEditorProps) {
  const [data, setData] = useState<CMSData | null>(null);
  const [editedData, setEditedData] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const canWrite = cmsService.hasPermission(user, object, 'write');
  const canDelete = cmsService.hasPermission(user, object, 'delete');

  useEffect(() => {
    loadObjectData();
  }, [object]);

  const loadObjectData = async () => {
    try {
      setLoading(true);
      setError(null);
      const objectData = await cmsService.loadObjectData(object.path);
      if (objectData) {
        setData(objectData);
        setEditedData(JSON.stringify(objectData, null, 2));
      } else {
        setError('Failed to load object data');
      }
    } catch (err) {
      setError('Error loading object data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!canWrite) {
      setError('You do not have permission to edit this object');
      return;
    }

    try {
      setSaving(true);
      setError(null);
      
      const parsedData = JSON.parse(editedData);
      const success = await cmsService.saveObjectData(object.path, parsedData);
      
      if (success) {
        setData(parsedData);
        setIsEditing(false);
      } else {
        setError('Failed to save changes');
      }
    } catch (err) {
      if (err instanceof SyntaxError) {
        setError('Invalid JSON format. Please check your syntax.');
      } else {
        setError('Error saving data');
      }
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (data) {
      setEditedData(JSON.stringify(data, null, 2));
    }
    setIsEditing(false);
    setError(null);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">{object.name}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
        </div>
        <div className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading object data...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{object.name}</h2>
            <p className="text-sm text-gray-600 mt-1">{object.description}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ✕
          </button>
        </div>
        
        <div className="flex items-center gap-4 mt-4 text-sm text-gray-600">
          <span>Type: <strong>{object.type}</strong></span>
          <span>Size: <strong>{object.metadata.size}</strong></span>
          <span>Department: <strong>{object.metadata.department || 'N/A'}</strong></span>
        </div>

        <div className="flex flex-wrap gap-2 mt-3">
          {object.tags.map((tag) => (
            <span
              key={tag}
              className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">JSON Data</h3>
          <div className="flex gap-2">
            {!isEditing && canWrite && (
              <button
                onClick={() => setIsEditing(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Edit
              </button>
            )}
            {isEditing && (
              <>
                <button
                  onClick={handleCancel}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Save'}
                </button>
              </>
            )}
          </div>
        </div>

        {data && (
          <div className="relative">
            {isEditing ? (
              <textarea
                value={editedData}
                onChange={(e) => setEditedData(e.target.value)}
                className="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter valid JSON..."
              />
            ) : (
              <pre className="w-full h-96 p-4 bg-gray-50 border border-gray-300 rounded-lg overflow-auto text-sm font-mono">
                {JSON.stringify(data, null, 2)}
              </pre>
            )}
          </div>
        )}

        {/* Permissions Info */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Your Permissions</h4>
          <div className="flex gap-2">
            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
              read
            </span>
            {canWrite && (
              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                write
              </span>
            )}
            {canDelete && (
              <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                delete
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}