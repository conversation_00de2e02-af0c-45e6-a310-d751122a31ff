'use client';

import React, { useState } from 'react';
import { CMSObject } from '../types';

interface ObjectListProps {
  objects: Array<CMSObject & { permissions: string[] }>;
  onObjectSelect: (object: CMSObject) => void;
}

export function ObjectList({ objects, onObjectSelect }: ObjectListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  const filteredObjects = objects.filter(obj => {
    const matchesSearch = obj.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         obj.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         obj.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = filterType === 'all' || obj.type === filterType;
    
    return matchesSearch && matchesType;
  });

  const objectTypes = [...new Set(objects.map(obj => obj.type))];

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">Your Assigned Objects</h2>
        <p className="text-gray-600 mt-1">
          You have access to {objects.length} object{objects.length !== 1 ? 's' : ''}
        </p>
      </div>

      {/* Filters */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search objects..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div>
            <select
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="all">All Types</option>
              {objectTypes.map(type => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Object List */}
      <div className="p-6">
        {filteredObjects.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-500">
              {objects.length === 0 
                ? "No objects assigned to you yet." 
                : "No objects match your search criteria."
              }
            </div>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredObjects.map((object) => (
              <div
                key={object.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onObjectSelect(object)}
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-gray-900 truncate">{object.name}</h3>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full ml-2">
                    {object.type}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {object.description}
                </p>

                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  <span>Size: {object.metadata.size}</span>
                  <span>Dept: {object.metadata.department || 'N/A'}</span>
                </div>

                <div className="flex flex-wrap gap-1 mb-3">
                  {object.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                  {object.tags.length > 3 && (
                    <span className="text-xs text-gray-500">
                      +{object.tags.length - 3} more
                    </span>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex gap-1">
                    {object.permissions.map((permission) => (
                      <span
                        key={permission}
                        className={`text-xs px-2 py-1 rounded ${
                          permission === 'read' 
                            ? 'bg-green-100 text-green-800'
                            : permission === 'write'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Open →
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}