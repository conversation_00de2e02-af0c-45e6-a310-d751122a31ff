'use client';

import React, { useState, useEffect } from 'react';
import { CMSUser, CMSObject } from '../types';
import { CMSDataService } from '../services/CMSDataService';
import { ObjectList } from './ObjectList';
import { ObjectEditor } from './ObjectEditor';
import { useUniversalAuth } from '@/app/backbone/oneid/hooks/useUniversalAuth.client';

export function CMSDashboard() {
  const { user: oneIDUser, loading: authLoading, error: authError } = useUniversalAuth();
  const [cmsUser, setCmsUser] = useState<CMSUser | null>(null);
  const [objects, setObjects] = useState<Array<CMSObject & { permissions: string[] }>>([]);
  const [selectedObject, setSelectedObject] = useState<CMSObject | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const cmsService = new CMSDataService();

  useEffect(() => {
    if (oneIDUser && !authLoading) {
      loadUserData();
    }
  }, [oneIDUser, authLoading]);

  const loadUserData = async () => {
    if (!oneIDUser) return;
    
    try {
      setLoading(true);
      
      // Map OneID user to CMS user structure
      const mappedUser: CMSUser = {
        id: oneIDUser.id,
        email: oneIDUser.email || `${oneIDUser.username}@abnasia.org`,
        name: oneIDUser.name || `${oneIDUser.firstName} ${oneIDUser.lastName}`,
        role: mapUserTypeToRole(oneIDUser.userType),
        department: oneIDUser.companyName || 'General',
        status: oneIDUser.status === 'active' ? 'active' : 'inactive',
        createdAt: oneIDUser.createdAt
      };
      
      setCmsUser(mappedUser);
      const accessibleObjects = await cmsService.getUserAccessibleObjects(mappedUser);
      setObjects(accessibleObjects);
    } catch (err) {
      setError('Failed to load user data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const mapUserTypeToRole = (userType: string): CMSUser['role'] => {
    switch (userType) {
      case 'company_admin':
        return 'admin';
      case 'employee':
        return 'user';
      case 'individual':
        return 'viewer';
      default:
        return 'user';
    }
  };

  const handleObjectSelect = (object: CMSObject) => {
    setSelectedObject(object);
  };

  const handleObjectClose = () => {
    setSelectedObject(null);
  };

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (authError || error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">{authError || error}</div>
        </div>
      </div>
    );
  }

  if (!oneIDUser) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="text-yellow-800">Please log in to access the CMS.</div>
          <button 
            className="mt-2 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            onClick={() => window.location.href = '/platforms/oneid/login'}
          >
            Login
          </button>
        </div>
      </div>
    );
  }

  if (!cmsUser) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="text-yellow-800">Loading user profile...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* User Info */}
      <div className="mb-8 bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Welcome, {cmsUser.name}</h2>
        <div className="text-sm text-gray-600">
          <p>Role: <span className="font-medium">{cmsUser.role}</span></p>
          <p>Department: <span className="font-medium">{cmsUser.department}</span></p>
          <p>Email: <span className="font-medium">{cmsUser.email}</span></p>
          <p>OneID User: <span className="font-medium">{oneIDUser.username}</span></p>
        </div>
      </div>

      {/* Content Area */}
      {selectedObject ? (
        <ObjectEditor
          object={selectedObject}
          user={cmsUser}
          onClose={handleObjectClose}
          cmsService={cmsService}
        />
      ) : (
        <ObjectList
          objects={objects}
          onObjectSelect={handleObjectSelect}
        />
      )}
    </div>
  );
}