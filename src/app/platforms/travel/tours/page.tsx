'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Search,
  Filter,
  MapPin,
  Clock,
  Users,
  Star,
  Heart,
  Camera
} from 'lucide-react';
import { formatPrice } from '../utils';
import Link from 'next/link';

interface Tour {
  id: string;
  name: string;
  destination: string;
  duration: string;
  price: number;
  originalPrice?: number;
  rating: number;
  reviews: number;
  image: string;
  highlights: string[];
  includes: string[];
  category: string;
  groupSize: number;
  difficulty: string;
  discount?: number;
  available: boolean;
}

const mockTours: Tour[] = [
  {
    id: '1',
    name: 'Tour Hạ Long - Sapa 4N3Đ',
    destination: 'Hạ Long - Sapa',
    duration: '4 ngày 3 đêm',
    price: 4500000,
    originalPrice: 5500000,
    rating: 4.8,
    reviews: 245,
    image: '/api/placeholder/400/250',
    highlights: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>h<PERSON><PERSON>'],
    includes: ['<PERSON>hách sạn 4*', 'Ăn uống', 'Vé tham quan', 'Hướng dẫn viên'],
    category: 'Thiên nhiên',
    groupSize: 20,
    difficulty: 'Dễ',
    discount: 18,
    available: true
  },
  {
    id: '2',
    name: 'Tour Phú Quốc 3N2Đ',
    destination: 'Phú Quốc',
    duration: '3 ngày 2 đêm',
    price: 3200000,
    rating: 4.6,
    reviews: 189,
    image: '/api/placeholder/400/250',
    highlights: ['Cáp treo Hòn Thơm', 'Sunset Sanato', 'Chợ đêm Dinh Cậu', 'Bãi Sao'],
    includes: ['Resort 5*', 'Ăn sáng', 'Xe đưa đón', 'Tour 4 đảo'],
    category: 'Biển đảo',
    groupSize: 15,
    difficulty: 'Dễ',
    available: true
  },
  {
    id: '3',
    name: 'Tour Đà Lạt - Nha Trang 5N4Đ',
    destination: 'Đà Lạt - Nha Trang',
    duration: '5 ngày 4 đêm',
    price: 5800000,
    originalPrice: 6800000,
    rating: 4.7,
    reviews: 167,
    image: '/api/placeholder/400/250',
    highlights: ['Thác Elephant', 'Làng hoa Vạn Thành', 'Vinpearl Land', 'Tháp Bà Ponagar'],
    includes: ['Khách sạn 4*', 'Ăn uống', 'Vé tham quan', 'Xe riêng'],
    category: 'Kết hợp',
    groupSize: 12,
    difficulty: 'Trung bình',
    discount: 15,
    available: true
  }
];

export default function ToursPage() {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [favorites, setFavorites] = useState<string[]>([]);
  const [destination, setDestination] = useState('');
  const [duration, setDuration] = useState('all');
  const [category, setCategory] = useState('all');

  // Load tours data
  React.useEffect(() => {
    const loadTours = async () => {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTours(mockTours);
      setLoading(false);
    };
    loadTours();
  }, []);

  const handleSearch = async () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      let filtered = mockTours.filter(tour => {
        const matchesSearch = !searchQuery ||
          tour.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          tour.destination.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesDestination = !destination ||
          tour.destination.toLowerCase().includes(destination.toLowerCase());

        const matchesCategory = category === 'all' ||
          tour.category.toLowerCase() === category.toLowerCase();

        return matchesSearch && matchesDestination && matchesCategory;
      });

      setTours(filtered);
      setLoading(false);
    }, 1000);
  };

  const handleFavorite = (tourId: string) => {
    setFavorites(prev => 
      prev.includes(tourId) 
        ? prev.filter(id => id !== tourId)
        : [...prev, tourId]
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Tour du lịch</h1>
              <p className="text-gray-600 mt-1">Khám phá những tour du lịch hấp dẫn</p>
            </div>
            <Link href="/platforms/travel/start">
              <Button variant="outline">Quay lại trang chính</Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Search Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  Tìm kiếm tour
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Tìm kiếm</label>
                  <Input
                    placeholder="Tên tour, điểm đến..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Điểm đến</label>
                  <Input
                    placeholder="Nhập điểm đến..."
                    value={destination}
                    onChange={(e) => setDestination(e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Thời gian</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                  >
                    <option value="all">Tất cả</option>
                    <option value="1-2">1-2 ngày</option>
                    <option value="3-4">3-4 ngày</option>
                    <option value="5+">5+ ngày</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Loại tour</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                  >
                    <option value="all">Tất cả</option>
                    <option value="thiên nhiên">Thiên nhiên</option>
                    <option value="biển đảo">Biển đảo</option>
                    <option value="văn hóa">Văn hóa</option>
                    <option value="kết hợp">Kết hợp</option>
                  </select>
                </div>

                <Button onClick={handleSearch} className="w-full" disabled={loading}>
                  {loading ? 'Đang tìm...' : 'Tìm tour'}
                </Button>
              </CardContent>
            </Card>

            {/* Filters */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Bộ lọc
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Mức giá</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      Dưới 3 triệu
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      3-5 triệu
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      Trên 5 triệu
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Độ khó</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      Dễ
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      Trung bình
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      Khó
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tours List */}
          <div className="lg:col-span-3">
            <div className="flex items-center justify-between mb-6">
              <p className="text-gray-600">
                Tìm thấy {tours.length} tour
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {tours.map((tour) => (
                <Card key={tour.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <img 
                      src={tour.image} 
                      alt={tour.name}
                      className="w-full h-48 object-cover"
                    />
                    {tour.discount && (
                      <Badge className="absolute top-2 left-2 bg-red-500">
                        -{tour.discount}%
                      </Badge>
                    )}
                    <Button
                      onClick={() => handleFavorite(tour.id)}
                      variant="ghost"
                      size="sm"
                      className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                    >
                      <Heart 
                        className={`h-4 w-4 ${
                          favorites.includes(tour.id) ? 'fill-current text-red-500' : ''
                        }`} 
                      />
                    </Button>
                  </div>

                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="font-semibold text-lg">{tour.name}</h3>
                        <div className="flex items-center gap-1 mt-1">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{tour.destination}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-1 mb-1">
                          <Star className="h-4 w-4 fill-current text-yellow-400" />
                          <span className="text-sm font-medium">{tour.rating}</span>
                          <span className="text-xs text-gray-500">({tour.reviews})</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 mb-3 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {tour.duration}
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        {tour.groupSize} người
                      </div>
                    </div>

                    {/* Highlights */}
                    <div className="mb-3">
                      <p className="text-sm font-medium mb-1">Điểm nổi bật:</p>
                      <div className="flex flex-wrap gap-1">
                        {tour.highlights.slice(0, 2).map((highlight) => (
                          <Badge key={highlight} variant="secondary" className="text-xs">
                            {highlight}
                          </Badge>
                        ))}
                        {tour.highlights.length > 2 && (
                          <Badge variant="secondary" className="text-xs">
                            +{tour.highlights.length - 2}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Includes */}
                    <div className="mb-4">
                      <p className="text-sm font-medium mb-1">Bao gồm:</p>
                      <div className="grid grid-cols-2 gap-1 text-xs text-gray-600">
                        {tour.includes.slice(0, 4).map((item) => (
                          <div key={item} className="flex items-center gap-1">
                            <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                            {item}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Price */}
                    <div className="flex items-center justify-between">
                      <div>
                        {tour.originalPrice && (
                          <div className="text-gray-500 line-through text-sm">
                            {formatPrice(tour.originalPrice)}
                          </div>
                        )}
                        <div className="text-xl font-bold text-blue-600">
                          {formatPrice(tour.price)}
                        </div>
                        <div className="text-sm text-gray-500">/ khách</div>
                      </div>
                      <Button>
                        Đặt tour
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {tours.length === 0 && !loading && (
              <div className="text-center py-12">
                <div className="text-gray-500 mb-4">
                  <Camera className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Không tìm thấy tour nào phù hợp</p>
                  <p className="text-sm">Thử thay đổi tiêu chí tìm kiếm</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}