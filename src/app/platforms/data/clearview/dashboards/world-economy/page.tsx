'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface WorldEconomyData {
  globalOverview: {
    worldGDP: number;
    gdpGrowth: number;
    globalInflation: number;
    globalUnemployment: number;
    globalTrade: number;
    tradeGrowth: number;
  };
  majorEconomies: Array<{
    country: string;
    gdp: number;
    gdpGrowth: number;
    inflation: number;
    unemployment: number;
    interestRate: number;
    currency: string;
    population: number;
    gdpPerCapita: number;
  }>;
  economicIndicators: {
    commodities: Array<{
      name: string;
      price: number;
      change: number;
      changePercent: number;
      unit: string;
    }>;
    indices: Array<{
      name: string;
      value: number;
      change: number;
      changePercent: number;
      country: string;
    }>;
    bonds: Array<{
      country: string;
      yield: number;
      change: number;
      maturity: string;
    }>;
  };
  regionalData: Array<{
    region: string;
    countries: number;
    totalGDP: number;
    averageGrowth: number;
    averageInflation: number;
    majorChallenges: string[];
  }>;
  globalRisks: Array<{
    risk: string;
    probability: 'low' | 'medium' | 'high';
    impact: 'low' | 'medium' | 'high';
    description: string;
    affectedRegions: string[];
  }>;
  tradeData: {
    topExporters: Array<{
      country: string;
      exports: number;
      mainProducts: string[];
    }>;
    topImporters: Array<{
      country: string;
      imports: number;
      mainProducts: string[];
    }>;
    tradeRoutes: Array<{
      route: string;
      volume: number;
      growth: number;
    }>;
  };
  developmentMetrics: Array<{
    country: string;
    hdi: number;
    gini: number;
    lifeExpectancy: number;
    educationIndex: number;
    healthcareIndex: number;
  }>;
  lastUpdated: string;
}

export default function WorldEconomyDashboard() {
  const router = useRouter();
  const [data, setData] = useState<WorldEconomyData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/data/apps/platforms/data/clearview/world-economy.json');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error loading world economy data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-gray-600">Loading World Economy...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-red-600">Error loading world economy data</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">World Economy</h1>
              <p className="text-gray-600 mt-2">Global economic indicators and regional analysis</p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => router.back()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Export Report
              </button>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Last updated: {new Date(data.lastUpdated).toLocaleString()}
          </div>
        </div>

        {/* Global Overview */}
        <div className="bg-white p-6 rounded-lg shadow-sm border mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Global Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">${(data.globalOverview.worldGDP / 1000).toFixed(1)}T</div>
              <div className="text-sm text-gray-500">World GDP</div>
              <div className={`text-xs mt-1 ${data.globalOverview.gdpGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {data.globalOverview.gdpGrowth >= 0 ? '+' : ''}{data.globalOverview.gdpGrowth.toFixed(1)}% growth
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{data.globalOverview.globalInflation.toFixed(1)}%</div>
              <div className="text-sm text-gray-500">Global Inflation</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{data.globalOverview.globalUnemployment.toFixed(1)}%</div>
              <div className="text-sm text-gray-500">Global Unemployment</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">${(data.globalOverview.globalTrade / 1000).toFixed(1)}T</div>
              <div className="text-sm text-gray-500">Global Trade</div>
              <div className={`text-xs mt-1 ${data.globalOverview.tradeGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {data.globalOverview.tradeGrowth >= 0 ? '+' : ''}{data.globalOverview.tradeGrowth.toFixed(1)}% growth
              </div>
            </div>
          </div>
        </div>

        {/* Major Economies */}
        <div className="bg-white rounded-lg shadow-sm border mb-8">
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">Major Economies</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GDP</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Growth</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inflation</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unemployment</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interest Rate</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GDP per Capita</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.majorEconomies.map((economy, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{economy.country}</div>
                      <div className="text-xs text-gray-500">{economy.currency} • {(economy.population / 1000000).toFixed(0)}M people</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${(economy.gdp / 1000).toFixed(1)}T
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm ${economy.gdpGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {economy.gdpGrowth >= 0 ? '+' : ''}{economy.gdpGrowth.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {economy.inflation.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {economy.unemployment.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {economy.interestRate.toFixed(2)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${economy.gdpPerCapita.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Economic Indicators */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Commodities</h2>
            <div className="space-y-3">
              {data.economicIndicators.commodities.map((commodity, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{commodity.name}</div>
                    <div className="text-sm text-gray-500">{commodity.unit}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">${commodity.price.toLocaleString()}</div>
                    <div className={`text-sm ${commodity.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {commodity.changePercent >= 0 ? '+' : ''}{commodity.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Stock Indices</h2>
            <div className="space-y-3">
              {data.economicIndicators.indices.map((index, idx) => (
                <div key={idx} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{index.name}</div>
                    <div className="text-sm text-gray-500">{index.country}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{index.value.toLocaleString()}</div>
                    <div className={`text-sm ${index.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {index.changePercent >= 0 ? '+' : ''}{index.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Government Bonds</h2>
            <div className="space-y-3">
              {data.economicIndicators.bonds.map((bond, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{bond.country}</div>
                    <div className="text-sm text-gray-500">{bond.maturity}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{bond.yield.toFixed(2)}%</div>
                    <div className={`text-sm ${bond.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {bond.change >= 0 ? '+' : ''}{bond.change.toFixed(2)}bp
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Regional Analysis */}
        <div className="bg-white p-6 rounded-lg shadow-sm border mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Regional Analysis</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {data.regionalData.map((region, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">{region.region}</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Countries:</span>
                    <span className="font-medium">{region.countries}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total GDP:</span>
                    <span className="font-medium">${(region.totalGDP / 1000).toFixed(1)}T</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Growth:</span>
                    <span className={`font-medium ${region.averageGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {region.averageGrowth >= 0 ? '+' : ''}{region.averageGrowth.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Inflation:</span>
                    <span className="font-medium">{region.averageInflation.toFixed(1)}%</span>
                  </div>
                  <div className="mt-3">
                    <div className="text-gray-600 text-xs mb-1">Major Challenges:</div>
                    <div className="flex flex-wrap gap-1">
                      {region.majorChallenges.map((challenge, idx) => (
                        <span key={idx} className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                          {challenge}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Global Risks */}
        <div className="bg-white p-6 rounded-lg shadow-sm border mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Global Economic Risks</h2>
          <div className="space-y-4">
            {data.globalRisks.map((risk, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-gray-900">{risk.risk}</h3>
                  <div className="flex space-x-2">
                    <span className={`px-2 py-1 text-xs rounded ${
                      risk.probability === 'high' ? 'bg-red-100 text-red-800' :
                      risk.probability === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {risk.probability.toUpperCase()} PROB
                    </span>
                    <span className={`px-2 py-1 text-xs rounded ${
                      risk.impact === 'high' ? 'bg-red-100 text-red-800' :
                      risk.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {risk.impact.toUpperCase()} IMPACT
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">{risk.description}</p>
                <div className="flex flex-wrap gap-1">
                  {risk.affectedRegions.map((region, idx) => (
                    <span key={idx} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                      {region}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Trade Data */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Exporters</h2>
            <div className="space-y-3">
              {data.tradeData.topExporters.map((exporter, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded">
                  <div className="flex justify-between items-center mb-2">
                    <div className="font-medium text-gray-900">{exporter.country}</div>
                    <div className="font-semibold text-green-600">${(exporter.exports / 1000).toFixed(1)}T</div>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {exporter.mainProducts.map((product, idx) => (
                      <span key={idx} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                        {product}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Importers</h2>
            <div className="space-y-3">
              {data.tradeData.topImporters.map((importer, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded">
                  <div className="flex justify-between items-center mb-2">
                    <div className="font-medium text-gray-900">{importer.country}</div>
                    <div className="font-semibold text-red-600">${(importer.imports / 1000).toFixed(1)}T</div>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {importer.mainProducts.map((product, idx) => (
                      <span key={idx} className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">
                        {product}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Development Metrics */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">Development Metrics</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HDI</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gini Index</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Life Expectancy</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Education</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Healthcare</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.developmentMetrics.map((metric, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {metric.country}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${
                        metric.hdi >= 0.8 ? 'text-green-600' :
                        metric.hdi >= 0.7 ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {metric.hdi.toFixed(3)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {metric.gini.toFixed(1)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {metric.lifeExpectancy.toFixed(1)} years
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${
                        metric.educationIndex >= 0.8 ? 'text-green-600' :
                        metric.educationIndex >= 0.7 ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {metric.educationIndex.toFixed(3)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${
                        metric.healthcareIndex >= 0.8 ? 'text-green-600' :
                        metric.healthcareIndex >= 0.7 ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {metric.healthcareIndex.toFixed(3)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}