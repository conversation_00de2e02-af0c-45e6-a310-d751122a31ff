'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface UserEngagementData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    returningUsers: number;
    avgSessionDuration: number;
    bounceRate: number;
  };
  engagement: {
    pageViews: number;
    uniquePageViews: number;
    avgTimeOnPage: number;
    pagesPerSession: number;
    exitRate: number;
  };
  userBehavior: {
    topPages: Array<{
      page: string;
      views: number;
      uniqueViews: number;
      avgTime: number;
    }>;
    userFlow: Array<{
      step: string;
      users: number;
      dropoffRate: number;
    }>;
  };
  demographics: {
    ageGroups: Array<{
      range: string;
      percentage: number;
      users: number;
    }>;
    locations: Array<{
      country: string;
      users: number;
      percentage: number;
    }>;
    devices: Array<{
      type: string;
      users: number;
      percentage: number;
    }>;
  };
  conversion: {
    goals: Array<{
      name: string;
      completions: number;
      conversionRate: number;
      value: number;
    }>;
    funnels: Array<{
      stage: string;
      users: number;
      conversionRate: number;
    }>;
  };
  lastUpdated: string;
}

export default function UserEngagementDashboard() {
  const router = useRouter();
  const [data, setData] = useState<UserEngagementData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/data/apps/platforms/data/clearview/user-engagement.json');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error loading user engagement data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-gray-600">Loading User Engagement Dashboard...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-red-600">Error loading dashboard data</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">User Engagement Dashboard</h1>
              <p className="text-gray-600 mt-2">Monitor user behavior and engagement metrics</p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => router.back()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Export Report
              </button>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Last updated: {new Date(data.lastUpdated).toLocaleString()}
          </div>
        </div>

        {/* Overview Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Users</h3>
            <p className="text-2xl font-bold text-gray-900">{data.overview.totalUsers.toLocaleString()}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Active Users</h3>
            <p className="text-2xl font-bold text-green-600">{data.overview.activeUsers.toLocaleString()}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">New Users</h3>
            <p className="text-2xl font-bold text-blue-600">{data.overview.newUsers.toLocaleString()}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Returning Users</h3>
            <p className="text-2xl font-bold text-purple-600">{data.overview.returningUsers.toLocaleString()}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Avg Session Duration</h3>
            <p className="text-2xl font-bold text-orange-600">{data.overview.avgSessionDuration}m</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Bounce Rate</h3>
            <p className="text-2xl font-bold text-red-600">{data.overview.bounceRate}%</p>
          </div>
        </div>

        {/* Engagement Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Engagement Metrics</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Page Views</span>
                <span className="font-semibold">{data.engagement.pageViews.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Unique Page Views</span>
                <span className="font-semibold">{data.engagement.uniquePageViews.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Avg Time on Page</span>
                <span className="font-semibold">{data.engagement.avgTimeOnPage}s</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Pages per Session</span>
                <span className="font-semibold">{data.engagement.pagesPerSession}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Exit Rate</span>
                <span className="font-semibold">{data.engagement.exitRate}%</span>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Pages</h2>
            <div className="space-y-3">
              {data.userBehavior.topPages.map((page, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{page.page}</div>
                    <div className="text-sm text-gray-500">{page.views} views • {page.avgTime}s avg time</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-blue-600">{page.uniqueViews}</div>
                    <div className="text-xs text-gray-500">unique</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Demographics and Conversion */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Demographics</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Age Groups</h3>
                <div className="space-y-2">
                  {data.demographics.ageGroups.map((group, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-gray-600">{group.range}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${group.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{group.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Top Locations</h3>
                <div className="space-y-2">
                  {data.demographics.locations.slice(0, 5).map((location, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-gray-600">{location.country}</span>
                      <span className="text-sm font-medium">{location.users} ({location.percentage}%)</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Conversion Goals</h2>
            <div className="space-y-4">
              {data.conversion.goals.map((goal, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium text-gray-900">{goal.name}</h3>
                    <span className="text-sm font-semibold text-green-600">{goal.conversionRate}%</span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>{goal.completions} completions</span>
                    <span>${goal.value.toLocaleString()} value</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* User Flow */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">User Flow</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {data.userBehavior.userFlow.map((step, index) => (
              <div key={index} className="text-center">
                <div className="bg-blue-100 p-4 rounded-lg mb-2">
                  <div className="text-2xl font-bold text-blue-600">{step.users.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">{step.step}</div>
                </div>
                {index < data.userBehavior.userFlow.length - 1 && (
                  <div className="text-sm text-red-600 font-medium">
                    {step.dropoffRate}% dropoff
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}