'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface ForexMonitorData {
  majorPairs: Array<{
    pair: string;
    rate: number;
    change: number;
    changePercent: number;
    bid: number;
    ask: number;
    spread: number;
    high24h: number;
    low24h: number;
    volume: number;
  }>;
  crossRates: Array<{
    base: string;
    rates: Record<string, number>;
  }>;
  commodityCurrencies: Array<{
    pair: string;
    rate: number;
    change: number;
    changePercent: number;
    commodity: string;
  }>;
  centralBankRates: Array<{
    country: string;
    currency: string;
    rate: number;
    lastChange: string;
    nextMeeting: string;
  }>;
  economicCalendar: Array<{
    date: string;
    time: string;
    currency: string;
    event: string;
    importance: 'low' | 'medium' | 'high';
    forecast: string;
    previous: string;
  }>;
  marketSentiment: {
    riskOn: boolean;
    vixLevel: number;
    dollarIndex: number;
    goldPrice: number;
    oilPrice: number;
  };
  technicalAnalysis: Array<{
    pair: string;
    trend: 'bullish' | 'bearish' | 'neutral';
    support: number;
    resistance: number;
    rsi: number;
    macd: string;
    recommendation: 'buy' | 'sell' | 'hold';
  }>;
  news: Array<{
    title: string;
    summary: string;
    impact: 'low' | 'medium' | 'high';
    currency: string;
    timestamp: string;
  }>;
  lastUpdated: string;
}

export default function ForexMonitorDashboard() {
  const router = useRouter();
  const [data, setData] = useState<ForexMonitorData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/data/apps/platforms/data/clearview/forex-monitor.json');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error loading forex monitor data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-gray-600">Loading Forex Monitor...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-red-600">Error loading forex data</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Forex Monitor</h1>
              <p className="text-gray-600 mt-2">Real-time currency exchange rates and market analysis</p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => router.back()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Set Alert
              </button>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Last updated: {new Date(data.lastUpdated).toLocaleString()}
          </div>
        </div>

        {/* Market Sentiment */}
        <div className="bg-white p-6 rounded-lg shadow-sm border mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Market Sentiment</h2>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="text-center">
              <div className={`text-2xl font-bold ${data.marketSentiment.riskOn ? 'text-green-600' : 'text-red-600'}`}>
                {data.marketSentiment.riskOn ? 'RISK ON' : 'RISK OFF'}
              </div>
              <div className="text-sm text-gray-500">Market Mode</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{data.marketSentiment.vixLevel}</div>
              <div className="text-sm text-gray-500">VIX Level</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{data.marketSentiment.dollarIndex.toFixed(2)}</div>
              <div className="text-sm text-gray-500">Dollar Index</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">${data.marketSentiment.goldPrice.toLocaleString()}</div>
              <div className="text-sm text-gray-500">Gold (oz)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">${data.marketSentiment.oilPrice.toFixed(2)}</div>
              <div className="text-sm text-gray-500">Oil (bbl)</div>
            </div>
          </div>
        </div>

        {/* Major Currency Pairs */}
        <div className="bg-white rounded-lg shadow-sm border mb-8">
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">Major Currency Pairs</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pair</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bid/Ask</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spread</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">24h Range</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Volume</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.majorPairs.map((pair, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{pair.pair}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {pair.rate.toFixed(5)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm ${pair.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {pair.changePercent >= 0 ? '+' : ''}{pair.change.toFixed(5)}
                      </div>
                      <div className={`text-xs ${pair.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {pair.changePercent >= 0 ? '+' : ''}{pair.changePercent.toFixed(2)}%
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{pair.bid.toFixed(5)}</div>
                      <div className="text-xs text-gray-500">{pair.ask.toFixed(5)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {pair.spread.toFixed(1)} pips
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{pair.high24h.toFixed(5)}</div>
                      <div className="text-xs text-gray-500">{pair.low24h.toFixed(5)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {(pair.volume / 1000000).toFixed(1)}M
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Cross Rates and Technical Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Cross Rates Matrix</h2>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr>
                    <th className="text-left p-2 font-medium text-gray-500">Base</th>
                    <th className="text-center p-2 font-medium text-gray-500">USD</th>
                    <th className="text-center p-2 font-medium text-gray-500">EUR</th>
                    <th className="text-center p-2 font-medium text-gray-500">GBP</th>
                    <th className="text-center p-2 font-medium text-gray-500">JPY</th>
                  </tr>
                </thead>
                <tbody>
                  {data.crossRates.map((cross, index) => (
                    <tr key={index} className="border-t">
                      <td className="p-2 font-medium text-gray-900">{cross.base}</td>
                      <td className="p-2 text-center">{cross.rates.USD?.toFixed(4) || '-'}</td>
                      <td className="p-2 text-center">{cross.rates.EUR?.toFixed(4) || '-'}</td>
                      <td className="p-2 text-center">{cross.rates.GBP?.toFixed(4) || '-'}</td>
                      <td className="p-2 text-center">{cross.rates.JPY?.toFixed(2) || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Technical Analysis</h2>
            <div className="space-y-4">
              {data.technicalAnalysis.map((analysis, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded">
                  <div className="flex justify-between items-start mb-2">
                    <div className="font-medium text-gray-900">{analysis.pair}</div>
                    <div className={`px-2 py-1 text-xs rounded ${
                      analysis.recommendation === 'buy' ? 'bg-green-100 text-green-800' :
                      analysis.recommendation === 'sell' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {analysis.recommendation.toUpperCase()}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">Trend</div>
                      <div className={`font-medium ${
                        analysis.trend === 'bullish' ? 'text-green-600' :
                        analysis.trend === 'bearish' ? 'text-red-600' :
                        'text-gray-600'
                      }`}>
                        {analysis.trend.charAt(0).toUpperCase() + analysis.trend.slice(1)}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">RSI</div>
                      <div className="font-medium">{analysis.rsi}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Support</div>
                      <div className="font-medium">{analysis.support.toFixed(5)}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Resistance</div>
                      <div className="font-medium">{analysis.resistance.toFixed(5)}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Central Bank Rates and Commodity Currencies */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Central Bank Rates</h2>
            <div className="space-y-4">
              {data.centralBankRates.map((bank, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{bank.country}</div>
                    <div className="text-sm text-gray-500">{bank.currency}</div>
                    <div className="text-xs text-gray-400">Next: {new Date(bank.nextMeeting).toLocaleDateString()}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">{bank.rate.toFixed(2)}%</div>
                    <div className="text-xs text-gray-500">{new Date(bank.lastChange).toLocaleDateString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Commodity Currencies</h2>
            <div className="space-y-4">
              {data.commodityCurrencies.map((currency, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{currency.pair}</div>
                    <div className="text-sm text-gray-500">{currency.commodity}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{currency.rate.toFixed(5)}</div>
                    <div className={`text-sm ${currency.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {currency.changePercent >= 0 ? '+' : ''}{currency.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Economic Calendar */}
        <div className="bg-white p-6 rounded-lg shadow-sm border mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Economic Calendar</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Time</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impact</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Forecast</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Previous</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.economicCalendar.map((event, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{new Date(event.date).toLocaleDateString()}</div>
                      <div className="text-xs text-gray-500">{event.time}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                        {event.currency}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">{event.event}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${
                        event.importance === 'high' ? 'bg-red-100 text-red-800' :
                        event.importance === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {event.importance.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{event.forecast}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{event.previous}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Market News */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Market News</h2>
          <div className="space-y-4">
            {data.news.map((article, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-gray-900">{article.title}</h3>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded ${
                      article.impact === 'high' ? 'bg-red-100 text-red-800' :
                      article.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {article.impact.toUpperCase()}
                    </span>
                    <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                      {article.currency}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">{article.summary}</p>
                <div className="text-xs text-gray-400">
                  {new Date(article.timestamp).toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}