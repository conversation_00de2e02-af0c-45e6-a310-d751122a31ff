'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface GlobalMarketsData {
  overview: {
    totalMarketCap: number;
    dailyVolume: number;
    activeMarkets: number;
    marketSentiment: 'bullish' | 'bearish' | 'neutral';
  };
  majorIndices: Array<{
    name: string;
    symbol: string;
    value: number;
    change: number;
    changePercent: number;
    country: string;
    currency: string;
  }>;
  currencies: Array<{
    pair: string;
    rate: number;
    change: number;
    changePercent: number;
    volume: number;
  }>;
  commodities: Array<{
    name: string;
    symbol: string;
    price: number;
    change: number;
    changePercent: number;
    unit: string;
  }>;
  bonds: Array<{
    name: string;
    yield: number;
    change: number;
    maturity: string;
    country: string;
  }>;
  economicIndicators: {
    globalGDP: number;
    globalGDPGrowth: number;
    globalInflation: number;
    globalUnemployment: number;
    oilPrice: number;
    goldPrice: number;
  };
  marketNews: Array<{
    title: string;
    source: string;
    timestamp: string;
    impact: 'high' | 'medium' | 'low';
    region: string;
  }>;
  regionalPerformance: Array<{
    region: string;
    performance: number;
    volume: number;
    topGainer: string;
    topLoser: string;
  }>;
  lastUpdated: string;
}

export default function GlobalMarketsDashboard() {
  const router = useRouter();
  const [data, setData] = useState<GlobalMarketsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/data/apps/platforms/data/clearview/global-markets.json');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error loading global markets data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-gray-600">Loading Global Markets...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-red-600">Error loading markets data</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Global Markets</h1>
              <p className="text-gray-600 mt-2">Worldwide stock markets and economic indicators</p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => router.back()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Export Data
              </button>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Last updated: {new Date(data.lastUpdated).toLocaleString()}
          </div>
        </div>

        {/* Market Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Market Cap</h3>
            <p className="text-2xl font-bold text-gray-900">${(data.overview.totalMarketCap / 1e12).toFixed(1)}T</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Daily Volume</h3>
            <p className="text-2xl font-bold text-gray-900">${(data.overview.dailyVolume / 1e12).toFixed(1)}T</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Active Markets</h3>
            <p className="text-2xl font-bold text-gray-900">{data.overview.activeMarkets}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Market Sentiment</h3>
            <p className={`text-2xl font-bold capitalize ${
              data.overview.marketSentiment === 'bullish' ? 'text-green-600' :
              data.overview.marketSentiment === 'bearish' ? 'text-red-600' : 'text-yellow-600'
            }`}>
              {data.overview.marketSentiment}
            </p>
          </div>
        </div>

        {/* Major Indices */}
        <div className="bg-white rounded-lg shadow-sm border mb-8">
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">Major Stock Indices</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Index</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">% Change</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.majorIndices.map((index, i) => (
                  <tr key={i} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{index.name}</div>
                        <div className="text-sm text-gray-500">{index.symbol}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {index.value.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm ${index.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {index.change >= 0 ? '+' : ''}{index.change.toFixed(2)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm ${index.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {index.changePercent >= 0 ? '+' : ''}{index.changePercent.toFixed(2)}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {index.country}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Currencies and Commodities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Major Currencies</h2>
            <div className="space-y-3">
              {data.currencies.map((currency, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{currency.pair}</div>
                    <div className="text-sm text-gray-500">Vol: ${(currency.volume / 1e9).toFixed(1)}B</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{currency.rate.toFixed(4)}</div>
                    <div className={`text-sm ${currency.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {currency.changePercent >= 0 ? '+' : ''}{currency.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Commodities</h2>
            <div className="space-y-3">
              {data.commodities.map((commodity, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{commodity.name}</div>
                    <div className="text-sm text-gray-500">{commodity.symbol}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">${commodity.price.toLocaleString()}/{commodity.unit}</div>
                    <div className={`text-sm ${commodity.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {commodity.changePercent >= 0 ? '+' : ''}{commodity.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Economic Indicators and Bonds */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Economic Indicators</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Global GDP</span>
                <span className="font-semibold">${(data.economicIndicators.globalGDP / 1e12).toFixed(1)}T</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">GDP Growth</span>
                <span className={`font-semibold ${data.economicIndicators.globalGDPGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {data.economicIndicators.globalGDPGrowth >= 0 ? '+' : ''}{data.economicIndicators.globalGDPGrowth.toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Global Inflation</span>
                <span className="font-semibold">{data.economicIndicators.globalInflation.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Unemployment</span>
                <span className="font-semibold">{data.economicIndicators.globalUnemployment.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Oil Price</span>
                <span className="font-semibold">${data.economicIndicators.oilPrice.toFixed(2)}/bbl</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Gold Price</span>
                <span className="font-semibold">${data.economicIndicators.goldPrice.toLocaleString()}/oz</span>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Government Bonds</h2>
            <div className="space-y-3">
              {data.bonds.map((bond, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{bond.name}</div>
                    <div className="text-sm text-gray-500">{bond.country} • {bond.maturity}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{bond.yield.toFixed(2)}%</div>
                    <div className={`text-sm ${bond.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {bond.change >= 0 ? '+' : ''}{bond.change.toFixed(2)}bp
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Regional Performance */}
        <div className="bg-white p-6 rounded-lg shadow-sm border mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Regional Performance</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {data.regionalPerformance.map((region, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-2">{region.region}</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Performance</span>
                    <span className={`text-sm font-semibold ${region.performance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {region.performance >= 0 ? '+' : ''}{region.performance.toFixed(2)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Volume</span>
                    <span className="text-sm font-semibold">${(region.volume / 1e9).toFixed(1)}B</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    Top: {region.topGainer} | Bottom: {region.topLoser}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Market News */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Market News</h2>
          <div className="space-y-4">
            {data.marketNews.map((news, index) => (
              <div key={index} className="border-l-4 border-blue-500 pl-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{news.title}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-sm text-gray-500">{news.source}</span>
                      <span className="text-sm text-gray-400">•</span>
                      <span className="text-sm text-gray-500">{news.region}</span>
                      <span className="text-sm text-gray-400">•</span>
                      <span className="text-sm text-gray-500">{new Date(news.timestamp).toLocaleString()}</span>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    news.impact === 'high' ? 'bg-red-100 text-red-800' :
                    news.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {news.impact} impact
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}