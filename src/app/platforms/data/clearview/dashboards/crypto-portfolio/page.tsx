'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface CryptoPortfolioData {
  portfolio: {
    totalValue: number;
    totalInvested: number;
    totalGainLoss: number;
    totalGainLossPercent: number;
    dayChange: number;
    dayChangePercent: number;
  };
  holdings: Array<{
    symbol: string;
    name: string;
    amount: number;
    currentPrice: number;
    value: number;
    invested: number;
    gainLoss: number;
    gainLossPercent: number;
    dayChange: number;
    dayChangePercent: number;
    allocation: number;
  }>;
  markets: {
    totalMarketCap: number;
    btcDominance: number;
    fearGreedIndex: number;
    activeCoins: number;
    totalVolume24h: number;
  };
  topGainers: Array<{
    symbol: string;
    name: string;
    price: number;
    change24h: number;
  }>;
  topLosers: Array<{
    symbol: string;
    name: string;
    price: number;
    change24h: number;
  }>;
  news: Array<{
    title: string;
    source: string;
    timestamp: string;
    sentiment: 'positive' | 'negative' | 'neutral';
  }>;
  lastUpdated: string;
}

export default function CryptoPortfolioDashboard() {
  const router = useRouter();
  const [data, setData] = useState<CryptoPortfolioData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/data/apps/platforms/data/clearview/crypto-portfolio.json');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error loading crypto portfolio data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-gray-600">Loading Crypto Portfolio...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-red-600">Error loading portfolio data</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Crypto Portfolio</h1>
              <p className="text-gray-600 mt-2">Track your cryptocurrency investments and market trends</p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => router.back()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Add Transaction
              </button>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Last updated: {new Date(data.lastUpdated).toLocaleString()}
          </div>
        </div>

        {/* Portfolio Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Portfolio Value</h3>
            <p className="text-2xl font-bold text-gray-900">${data.portfolio.totalValue.toLocaleString()}</p>
            <p className={`text-sm mt-1 ${data.portfolio.dayChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.portfolio.dayChangePercent >= 0 ? '+' : ''}{data.portfolio.dayChangePercent.toFixed(2)}% today
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Invested</h3>
            <p className="text-2xl font-bold text-gray-900">${data.portfolio.totalInvested.toLocaleString()}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Gain/Loss</h3>
            <p className={`text-2xl font-bold ${data.portfolio.totalGainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.portfolio.totalGainLoss >= 0 ? '+' : ''}${data.portfolio.totalGainLoss.toLocaleString()}
            </p>
            <p className={`text-sm mt-1 ${data.portfolio.totalGainLossPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.portfolio.totalGainLossPercent >= 0 ? '+' : ''}{data.portfolio.totalGainLossPercent.toFixed(2)}%
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">24h Change</h3>
            <p className={`text-2xl font-bold ${data.portfolio.dayChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.portfolio.dayChange >= 0 ? '+' : ''}${data.portfolio.dayChange.toLocaleString()}
            </p>
          </div>
        </div>

        {/* Holdings Table */}
        <div className="bg-white rounded-lg shadow-sm border mb-8">
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">Your Holdings</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">24h Change</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Allocation</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.holdings.map((holding, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{holding.symbol}</div>
                        <div className="text-sm text-gray-500">{holding.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {holding.amount.toFixed(6)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${holding.currentPrice.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${holding.value.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm ${holding.dayChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {holding.dayChangePercent >= 0 ? '+' : ''}{holding.dayChangePercent.toFixed(2)}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm ${holding.gainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {holding.gainLoss >= 0 ? '+' : ''}${holding.gainLoss.toLocaleString()}
                      </div>
                      <div className={`text-xs ${holding.gainLossPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {holding.gainLossPercent >= 0 ? '+' : ''}{holding.gainLossPercent.toFixed(2)}%
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${holding.allocation}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">{holding.allocation.toFixed(1)}%</span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Market Overview and Movers */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Market Overview</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Market Cap</span>
                <span className="font-semibold">${(data.markets.totalMarketCap / 1e12).toFixed(2)}T</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">BTC Dominance</span>
                <span className="font-semibold">{data.markets.btcDominance.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Fear & Greed Index</span>
                <span className={`font-semibold ${
                  data.markets.fearGreedIndex > 75 ? 'text-red-600' :
                  data.markets.fearGreedIndex > 50 ? 'text-yellow-600' :
                  data.markets.fearGreedIndex > 25 ? 'text-blue-600' : 'text-green-600'
                }`}>
                  {data.markets.fearGreedIndex}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">24h Volume</span>
                <span className="font-semibold">${(data.markets.totalVolume24h / 1e9).toFixed(1)}B</span>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Gainers</h2>
            <div className="space-y-3">
              {data.topGainers.map((coin, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <div className="font-medium text-gray-900">{coin.symbol}</div>
                    <div className="text-sm text-gray-500">{coin.name}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">${coin.price.toLocaleString()}</div>
                    <div className="text-sm text-green-600">+{coin.change24h.toFixed(2)}%</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Losers</h2>
            <div className="space-y-3">
              {data.topLosers.map((coin, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <div className="font-medium text-gray-900">{coin.symbol}</div>
                    <div className="text-sm text-gray-500">{coin.name}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">${coin.price.toLocaleString()}</div>
                    <div className="text-sm text-red-600">{coin.change24h.toFixed(2)}%</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* News Feed */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Crypto News</h2>
          <div className="space-y-4">
            {data.news.map((article, index) => (
              <div key={index} className="border-l-4 border-blue-500 pl-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{article.title}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-sm text-gray-500">{article.source}</span>
                      <span className="text-sm text-gray-400">•</span>
                      <span className="text-sm text-gray-500">{new Date(article.timestamp).toLocaleString()}</span>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    article.sentiment === 'positive' ? 'bg-green-100 text-green-800' :
                    article.sentiment === 'negative' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {article.sentiment}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}