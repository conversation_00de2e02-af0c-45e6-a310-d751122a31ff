'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface InvestmentTrackerData {
  portfolio: {
    totalValue: number;
    totalInvested: number;
    totalReturn: number;
    totalReturnPercent: number;
    dayChange: number;
    dayChangePercent: number;
    cashBalance: number;
  };
  assetAllocation: Array<{
    category: string;
    value: number;
    percentage: number;
    change: number;
    changePercent: number;
  }>;
  holdings: Array<{
    symbol: string;
    name: string;
    type: 'stock' | 'bond' | 'etf' | 'mutual_fund' | 'reit';
    shares: number;
    currentPrice: number;
    marketValue: number;
    costBasis: number;
    unrealizedGainLoss: number;
    unrealizedGainLossPercent: number;
    dayChange: number;
    dayChangePercent: number;
    dividendYield: number;
    sector: string;
  }>;
  performance: {
    ytdReturn: number;
    oneYearReturn: number;
    threeYearReturn: number;
    fiveYearReturn: number;
    sharpeRatio: number;
    beta: number;
    volatility: number;
  };
  dividends: {
    totalDividends: number;
    monthlyDividends: number;
    dividendYield: number;
    nextPayment: {
      amount: number;
      date: string;
      symbol: string;
    };
  };
  recentTransactions: Array<{
    date: string;
    type: 'buy' | 'sell' | 'dividend';
    symbol: string;
    quantity: number;
    price: number;
    amount: number;
  }>;
  watchlist: Array<{
    symbol: string;
    name: string;
    price: number;
    change: number;
    changePercent: number;
    targetPrice: number;
  }>;
  lastUpdated: string;
}

export default function InvestmentTrackerDashboard() {
  const router = useRouter();
  const [data, setData] = useState<InvestmentTrackerData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/data/apps/platforms/data/clearview/investment-tracker.json');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error loading investment tracker data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-gray-600">Loading Investment Tracker...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-red-600">Error loading investment data</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Investment Tracker</h1>
              <p className="text-gray-600 mt-2">Portfolio performance and investment analytics</p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => router.back()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Add Transaction
              </button>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Last updated: {new Date(data.lastUpdated).toLocaleString()}
          </div>
        </div>

        {/* Portfolio Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Portfolio Value</h3>
            <p className="text-2xl font-bold text-gray-900">${data.portfolio.totalValue.toLocaleString()}</p>
            <p className={`text-sm mt-1 ${data.portfolio.dayChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.portfolio.dayChangePercent >= 0 ? '+' : ''}{data.portfolio.dayChangePercent.toFixed(2)}% today
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Return</h3>
            <p className={`text-2xl font-bold ${data.portfolio.totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.portfolio.totalReturn >= 0 ? '+' : ''}${data.portfolio.totalReturn.toLocaleString()}
            </p>
            <p className={`text-sm mt-1 ${data.portfolio.totalReturnPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.portfolio.totalReturnPercent >= 0 ? '+' : ''}{data.portfolio.totalReturnPercent.toFixed(2)}%
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Cash Balance</h3>
            <p className="text-2xl font-bold text-gray-900">${data.portfolio.cashBalance.toLocaleString()}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Dividend Yield</h3>
            <p className="text-2xl font-bold text-blue-600">{data.dividends.dividendYield.toFixed(2)}%</p>
            <p className="text-sm mt-1 text-gray-600">${data.dividends.monthlyDividends.toLocaleString()}/month</p>
          </div>
        </div>

        {/* Asset Allocation and Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Asset Allocation</h2>
            <div className="space-y-4">
              {data.assetAllocation.map((asset, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 rounded-full bg-blue-600" style={{ backgroundColor: `hsl(${index * 60}, 70%, 50%)` }}></div>
                    <span className="font-medium text-gray-900">{asset.category}</span>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">${asset.value.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">{asset.percentage.toFixed(1)}%</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Performance Metrics</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">YTD Return</span>
                <span className={`font-semibold ${data.performance.ytdReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {data.performance.ytdReturn >= 0 ? '+' : ''}{data.performance.ytdReturn.toFixed(2)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">1 Year Return</span>
                <span className={`font-semibold ${data.performance.oneYearReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {data.performance.oneYearReturn >= 0 ? '+' : ''}{data.performance.oneYearReturn.toFixed(2)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">3 Year Return</span>
                <span className={`font-semibold ${data.performance.threeYearReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {data.performance.threeYearReturn >= 0 ? '+' : ''}{data.performance.threeYearReturn.toFixed(2)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Sharpe Ratio</span>
                <span className="font-semibold">{data.performance.sharpeRatio.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Beta</span>
                <span className="font-semibold">{data.performance.beta.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Volatility</span>
                <span className="font-semibold">{data.performance.volatility.toFixed(2)}%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Holdings Table */}
        <div className="bg-white rounded-lg shadow-sm border mb-8">
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">Holdings</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shares</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Market Value</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Day Change</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Return</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dividend</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.holdings.map((holding, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{holding.symbol}</div>
                        <div className="text-sm text-gray-500">{holding.name}</div>
                        <div className="text-xs text-gray-400">{holding.sector}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {holding.shares.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${holding.currentPrice.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${holding.marketValue.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm ${holding.dayChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {holding.dayChangePercent >= 0 ? '+' : ''}${holding.dayChange.toFixed(2)}
                      </div>
                      <div className={`text-xs ${holding.dayChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {holding.dayChangePercent >= 0 ? '+' : ''}{holding.dayChangePercent.toFixed(2)}%
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm ${holding.unrealizedGainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {holding.unrealizedGainLoss >= 0 ? '+' : ''}${holding.unrealizedGainLoss.toLocaleString()}
                      </div>
                      <div className={`text-xs ${holding.unrealizedGainLossPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {holding.unrealizedGainLossPercent >= 0 ? '+' : ''}{holding.unrealizedGainLossPercent.toFixed(2)}%
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {holding.dividendYield.toFixed(2)}%
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Recent Transactions and Watchlist */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Transactions</h2>
            <div className="space-y-3">
              {data.recentTransactions.map((transaction, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs rounded ${
                        transaction.type === 'buy' ? 'bg-green-100 text-green-800' :
                        transaction.type === 'sell' ? 'bg-red-100 text-red-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {transaction.type.toUpperCase()}
                      </span>
                      <span className="font-medium text-gray-900">{transaction.symbol}</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {transaction.quantity} shares @ ${transaction.price.toFixed(2)}
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(transaction.date).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`font-semibold ${
                      transaction.type === 'buy' ? 'text-red-600' :
                      transaction.type === 'sell' ? 'text-green-600' :
                      'text-blue-600'
                    }`}>
                      {transaction.type === 'buy' ? '-' : '+'}${transaction.amount.toLocaleString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Watchlist</h2>
            <div className="space-y-3">
              {data.watchlist.map((stock, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{stock.symbol}</div>
                    <div className="text-sm text-gray-500">{stock.name}</div>
                    <div className="text-xs text-gray-400">Target: ${stock.targetPrice.toFixed(2)}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">${stock.price.toFixed(2)}</div>
                    <div className={`text-sm ${stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Dividend Information */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Dividend Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">${data.dividends.totalDividends.toLocaleString()}</div>
              <div className="text-sm text-gray-500">Total Dividends (YTD)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">${data.dividends.monthlyDividends.toLocaleString()}</div>
              <div className="text-sm text-gray-500">Monthly Average</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{data.dividends.dividendYield.toFixed(2)}%</div>
              <div className="text-sm text-gray-500">Portfolio Yield</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-orange-600">${data.dividends.nextPayment.amount.toFixed(2)}</div>
              <div className="text-sm text-gray-500">{data.dividends.nextPayment.symbol}</div>
              <div className="text-xs text-gray-400">{new Date(data.dividends.nextPayment.date).toLocaleDateString()}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}