'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button.1';

interface SalesData {
  revenue: {
    total: number;
    monthly: number;
    growth: number;
  };
  conversions: {
    rate: number;
    total: number;
    trend: number;
  };
  team: {
    totalReps: number;
    topPerformer: string;
    avgDealsPerRep: number;
  };
  pipeline: {
    totalValue: number;
    deals: number;
    avgDealSize: number;
  };
  products: Array<{
    name: string;
    revenue: number;
    units: number;
    growth: number;
  }>;
  regions: Array<{
    name: string;
    revenue: number;
    growth: number;
    reps: number;
  }>;
}

export default function SalesAnalyticsDashboard() {
  const router = useRouter();
  const [data, setData] = useState<SalesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  useEffect(() => {
    const loadData = async () => {
      try {
        const salesData = await import('/data/apps/platforms/data/clearview/sales-analytics.json');
        setData((salesData as any).default || salesData);
        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error loading sales data:', error);
        // Fallback data
        setData({
          revenue: { total: 1250000, monthly: 125000, growth: 12.5 },
          conversions: { rate: 23.4, total: 1250, trend: 5.2 },
          team: { totalReps: 15, topPerformer: "Sarah Johnson", avgDealsPerRep: 8.3 },
          pipeline: { totalValue: 2500000, deals: 45, avgDealSize: 55555 },
          products: [],
          regions: []
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatCurrency = (value: number) => `$${value.toLocaleString()}`;
  const formatPercent = (value: number) => `${value.toFixed(1)}%`;

  if (loading || !data) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white text-black font-mono text-xs">
      {/* Header */}
      <div className="border-b border-gray-300 p-2 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/data/clearview')}>
            <ArrowLeft className="h-3 w-3 mr-1" />
            Back to ClearView
          </Button>
          <div>
            <span className="font-bold">Sales Analytics Dashboard</span>
            <span className="ml-4 text-gray-600">Last Update: {formatTime(lastUpdate)}</span>
          </div>
        </div>
        <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
          <TrendingUp className="h-3 w-3 mr-1" />
          Refresh
        </Button>
      </div>

      <div className="p-4 grid grid-cols-6 gap-4">
        {/* Revenue Section */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Revenue Metrics</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Total Revenue</span>
              <span className="font-bold">{formatCurrency(data.revenue.total)}</span>
            </div>
            <div className="flex justify-between">
              <span>Monthly Revenue</span>
              <span className="font-bold">{formatCurrency(data.revenue.monthly)}</span>
            </div>
            <div className="flex justify-between">
              <span>Growth Rate</span>
              <span className="font-bold text-green-600">+{formatPercent(data.revenue.growth)}</span>
            </div>
          </div>
        </div>

        {/* Conversions Section */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Conversion Metrics</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Conversion Rate</span>
              <span className="font-bold">{formatPercent(data.conversions.rate)}</span>
            </div>
            <div className="flex justify-between">
              <span>Total Conversions</span>
              <span className="font-bold">{data.conversions.total.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>Trend</span>
              <span className="font-bold text-green-600">+{formatPercent(data.conversions.trend)}</span>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Team Performance</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Total Sales Reps</span>
              <span className="font-bold">{data.team.totalReps}</span>
            </div>
            <div className="flex justify-between">
              <span>Top Performer</span>
              <span className="font-bold text-blue-600">{data.team.topPerformer}</span>
            </div>
            <div className="flex justify-between">
              <span>Avg Deals/Rep</span>
              <span className="font-bold">{data.team.avgDealsPerRep}</span>
            </div>
          </div>
        </div>

        {/* Pipeline Section */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Sales Pipeline</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Total Pipeline Value</span>
              <span className="font-bold">{formatCurrency(data.pipeline.totalValue)}</span>
            </div>
            <div className="flex justify-between">
              <span>Active Deals</span>
              <span className="font-bold">{data.pipeline.deals}</span>
            </div>
            <div className="flex justify-between">
              <span>Average Deal Size</span>
              <span className="font-bold">{formatCurrency(data.pipeline.avgDealSize)}</span>
            </div>
          </div>
        </div>

        {/* Product Performance */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Product Performance</div>
          <div className="space-y-1">
            {data.products.map((product, i) => (
              <div key={i} className="flex justify-between text-xs">
                <span className="truncate w-32">{product.name}</span>
                <span className="font-bold">{formatCurrency(product.revenue)}</span>
                <span className={`font-bold ${
                  product.growth >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {product.growth >= 0 ? '+' : ''}{formatPercent(product.growth)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Regional Performance */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Regional Performance</div>
          <div className="space-y-1">
            {data.regions.map((region, i) => (
              <div key={i} className="flex justify-between text-xs">
                <span className="truncate w-24">{region.name}</span>
                <span className="font-bold">{formatCurrency(region.revenue)}</span>
                <span className="font-bold">{region.reps} reps</span>
                <span className={`font-bold ${
                  region.growth >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {region.growth >= 0 ? '+' : ''}{formatPercent(region.growth)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Summary */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Performance Summary</div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Key Metrics</div>
              <div className="flex justify-between">
                <span>Revenue per Rep</span>
                <span className="font-bold">{formatCurrency(data.revenue.total / data.team.totalReps)}</span>
              </div>
              <div className="flex justify-between">
                <span>Pipeline Coverage</span>
                <span className="font-bold">{formatPercent((data.pipeline.totalValue / data.revenue.total) * 100)}</span>
              </div>
              <div className="flex justify-between">
                <span>Win Rate</span>
                <span className="font-bold">{formatPercent(data.conversions.rate)}</span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Targets</div>
              <div className="flex justify-between">
                <span>Monthly Target</span>
                <span className="font-bold">{formatCurrency(150000)}</span>
              </div>
              <div className="flex justify-between">
                <span>Achievement</span>
                <span className="font-bold text-green-600">{formatPercent((data.revenue.monthly / 150000) * 100)}</span>
              </div>
              <div className="flex justify-between">
                <span>Forecast</span>
                <span className="font-bold">{formatCurrency(data.revenue.monthly * 1.125)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}