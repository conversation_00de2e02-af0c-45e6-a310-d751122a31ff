'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTheme, useThemeStyles, ThemeToggleButton } from '@/themes/ThemeProvider';
import { AuthGuard, useABNSalesOpsAuth } from './components/AuthGuard';
import { 
  BarChart3, 
  Users, 
  Settings, 
  Menu, 
  X,
  Home,
  Target,
  UserPlus,
  Handshake,
  TrendingUp,
  MessageSquare,
  CheckSquare,
  Brain,
  HeadphonesIcon,
  Mail,
  Facebook,
  Instagram,
  Phone,
  Zap,
  ChevronDown,
  ChevronRight,
  LogOut,
  User
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/abnsalesops', icon: Home },
  { name: 'Territory Planning', href: '/abnsalesops/territory-planning', icon: Target },
  { name: 'Leads Management', href: '/abnsalesops/leads-management', icon: UserPlus },
  { name: 'Sales Management', href: '/abnsalesops/sales-management', icon: BarChart3 },
  { name: 'Deal Registration', href: '/abnsalesops/deal-registration', icon: Handshake },
  { name: 'Referrals', href: '/abnsalesops/referrals', icon: Users },
  { name: 'Marketing Management', href: '/abnsalesops/marketing-management', icon: TrendingUp },
  { name: 'Relationship Management', href: '/abnsalesops/relationship-management', icon: MessageSquare },
  { name: 'Tasks Pool', href: '/abnsalesops/tasks-pool', icon: CheckSquare },
  { name: 'Crowd Intelligence', href: '/abnsalesops/crowd-intelligence', icon: Brain },
  { name: 'Customer Data', href: '/abnsalesops/customer-data', icon: Users },
  { name: 'ELSA Enhanced', href: '/abnsalesops/elsa-enhanced', icon: Brain },
  { 
    name: 'OneTalk', 
    href: '/abnsalesops/onetalk', 
    icon: MessageSquare,
    subItems: [
      { name: 'Dashboard', href: '/abnsalesops/onetalk/dashboard', icon: Home },
      { name: 'Email Inbox', href: '/abnsalesops/onetalk/email', icon: Mail },
      { name: 'Facebook', href: '/abnsalesops/onetalk/facebook', icon: Facebook },
      { name: 'Instagram', href: '/abnsalesops/onetalk/instagram', icon: Instagram },
      { name: 'WhatsApp', href: '/abnsalesops/onetalk/whatsapp', icon: Phone },
      { name: 'Zalo', href: '/abnsalesops/onetalk/zalo', icon: Zap },
    ]
  },
  { name: 'Support Center', href: '/abnsalesops/supports', icon: HeadphonesIcon },
];

export default function ABNSalesOpsLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>(['OneTalk']);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const pathname = usePathname();
  const { currentTheme, isLoading } = useTheme();
  const { getButtonClass, getCardClass, getHeadingClass, isPaperTheme } = useThemeStyles();
  const { user, logout } = useABNSalesOpsAuth();
  const userMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(item => item !== itemName)
        : [...prev, itemName]
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  const baseLayoutClass = isPaperTheme 
    ? 'min-h-screen bg-[#f8f8f8] font-["Comic_Sans_MS",cursive,sans-serif]'
    : 'min-h-screen bg-gradient-to-br from-indigo-50 to-blue-50';

  const headerClass = isPaperTheme
    ? 'bg-white border-b-2 border-black shadow-[0_2px_4px_rgba(0,0,0,0.1)]'
    : 'bg-white shadow-sm border-b border-gray-200';

  const sidebarClass = isPaperTheme
    ? 'bg-white border-r-2 border-black'
    : 'bg-white border-r border-gray-200';

  return (
    <AuthGuard>
      <div className={baseLayoutClass}>
        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <div 
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        <div className="flex min-h-screen">
        {/* Sidebar */}
        <div className={`
          fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          ${sidebarClass}
        `}>
          <div className="flex h-full flex-col">
            {/* Sidebar header */}
            <div className={`flex h-16 items-center justify-between px-4 ${isPaperTheme ? 'border-b-2 border-black' : 'border-b border-gray-200'}`}>
              <h2 className={getHeadingClass(3)}>Sales Ops</h2>
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 space-y-1 px-2 py-4">
              {navigation.map((item) => {
                const isActive = pathname === item.href || (item.subItems && item.subItems.some(sub => pathname === sub.href));
                const isExpanded = expandedItems.includes(item.name);
                const Icon = item.icon;
                
                const linkClass = isPaperTheme
                  ? `group flex items-center px-3 py-2 text-sm font-bold uppercase tracking-wide rounded-lg transition-all duration-300 border-2 ${
                      isActive 
                        ? 'bg-black text-white border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]' 
                        : 'bg-white text-black border-black hover:bg-gray-200 hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-0.5'
                    }`
                  : `group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      isActive 
                        ? 'bg-indigo-100 text-indigo-700 border-l-4 border-indigo-500' 
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    }`;

                return (
                  <div key={item.name}>
                    {item.subItems ? (
                      <>
                        <button
                          onClick={() => toggleExpanded(item.name)}
                          className={`${linkClass} w-full justify-between`}
                        >
                          <div className="flex items-center">
                            <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
                            {item.name}
                          </div>
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </button>
                        {isExpanded && (
                          <div className="ml-8 mt-1 space-y-1">
                            {item.subItems.map((subItem) => {
                              const isSubActive = pathname === subItem.href;
                              const SubIcon = subItem.icon;
                              
                              const subLinkClass = isPaperTheme
                                ? `group flex items-center px-3 py-2 text-xs font-bold uppercase tracking-wide rounded-lg transition-all duration-300 border-2 ${
                                    isSubActive 
                                      ? 'bg-gray-800 text-white border-gray-800 shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]' 
                                      : 'bg-gray-100 text-black border-gray-400 hover:bg-gray-200 hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-0.5'
                                  }`
                                : `group flex items-center px-3 py-2 text-xs font-medium rounded-lg transition-colors duration-200 ${
                                    isSubActive 
                                      ? 'bg-indigo-50 text-indigo-600 border-l-2 border-indigo-400' 
                                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
                                  }`;

                              return (
                                <Link
                                  key={subItem.name}
                                  href={subItem.href}
                                  className={subLinkClass}
                                >
                                  <SubIcon className="mr-3 h-4 w-4 flex-shrink-0" />
                                  {subItem.name}
                                </Link>
                              );
                            })}
                          </div>
                        )}
                      </>
                    ) : (
                      <Link
                        href={item.href}
                        className={linkClass}
                      >
                        <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
                        {item.name}
                      </Link>
                    )}
                  </div>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1">
          {/* Top header */}
          <header className={`sticky top-0 z-30 flex items-center justify-between px-4 sm:px-6 lg:px-8 ${headerClass} ${
            isPaperTheme ? 'h-20 py-3' : 'h-16'
          }`}>
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden"
              >
                <Menu className="h-6 w-6" />
              </button>
              <h1 className={`ml-4 lg:ml-0 ${isPaperTheme ? getHeadingClass(4) : getHeadingClass(2)}`}>
                ABN Sales Operations
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              <ThemeToggleButton className="hidden sm:block" />
              
              {/* User Menu */}
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className={`flex items-center space-x-2 ${getButtonClass('outline')}`}
                >
                  <User className="h-4 w-4" />
                  <span className="hidden sm:block">
                    {user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : user?.username || 'User'}
                  </span>
                  <ChevronDown className="h-4 w-4" />
                </button>

                {userMenuOpen && (
                  <div className={`absolute right-0 mt-2 w-48 rounded-md shadow-lg z-50 ${
                    isPaperTheme 
                      ? 'bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]' 
                      : 'bg-white border border-gray-200'
                  }`}>
                    <div className="py-1">
                      <div className={`px-4 py-2 text-sm ${isPaperTheme ? 'border-b-2 border-black' : 'border-b border-gray-200'}`}>
                        <p className="font-medium">{user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : user?.username}</p>
                        <p className="text-gray-500 text-xs">{user?.email}</p>
                      </div>
                      
                      <Link
                        href="/abnsalesops/settings"
                        className={`block px-4 py-2 text-sm ${
                          isPaperTheme 
                            ? 'hover:bg-gray-100 font-bold uppercase tracking-wide' 
                            : 'hover:bg-gray-100 text-gray-700 hover:text-gray-900'
                        }`}
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <Settings className="inline w-4 h-4 mr-2" />
                        Settings
                      </Link>
                      
                      <button
                        onClick={() => {
                          logout();
                          setUserMenuOpen(false);
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm ${
                          isPaperTheme 
                            ? 'hover:bg-red-100 text-red-600 font-bold uppercase tracking-wide' 
                            : 'hover:bg-red-50 text-red-600 hover:text-red-700'
                        }`}
                      >
                        <LogOut className="inline w-4 h-4 mr-2" />
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </header>

          {/* Page content */}
          <main className="flex-1">
            {children}
          </main>
        </div>
        </div>
      </div>
    </AuthGuard>
  );
}
