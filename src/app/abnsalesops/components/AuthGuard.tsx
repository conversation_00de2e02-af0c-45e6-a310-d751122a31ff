'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Loader2 } from 'lucide-react';

interface User {
  id: string;
  username: string;
  email?: string;
  firstName?: string;
  lastName?: string;
}

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  const isAuthPage = pathname?.startsWith('/abnsalesops/auth/');

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('oneid_token');
        
        if (!token) {
          if (!isAuthPage) {
            router.push('/abnsalesops/auth/login');
          }
          setLoading(false);
          return;
        }

        const response = await fetch('/api/backbone/oneid/auth/validate', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.user) {
            setUser(data.user);
            
            // If user is authenticated and on auth page, redirect to dashboard
            if (isAuthPage) {
              router.push('/abnsalesops');
            }
          } else {
            localStorage.removeItem('oneid_token');
            if (!isAuthPage) {
              router.push('/abnsalesops/auth/login');
            }
          }
        } else {
          localStorage.removeItem('oneid_token');
          if (!isAuthPage) {
            router.push('/abnsalesops/auth/login');
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        localStorage.removeItem('oneid_token');
        if (!isAuthPage) {
          router.push('/abnsalesops/auth/login');
        }
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router, pathname, isAuthPage]);

  // Show loading spinner during auth check
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Allow auth pages to render without authentication
  if (isAuthPage) {
    return <>{children}</>;
  }

  // Protect non-auth pages - user must be authenticated
  if (!user) {
    return null; // Redirect is handled in useEffect
  }

  return <>{children}</>;
}

// Hook to access user data
export function useABNSalesOpsAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('oneid_token');
        if (!token) {
          setLoading(false);
          return;
        }

        const response = await fetch('/api/backbone/oneid/auth/validate', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.user) {
            setUser(data.user);
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const logout = () => {
    localStorage.removeItem('oneid_token');
    setUser(null);
    window.location.href = '/abnsalesops/auth/login';
  };

  return { user, loading, logout };
}