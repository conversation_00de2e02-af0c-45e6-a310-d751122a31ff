'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Eye, EyeOff, User, Building, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function ABNSalesOpsRegister() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'individual' | 'company'>('individual');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const [individualData, setIndividualData] = useState({
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    acceptTerms: false
  });

  const [companyData, setCompanyData] = useState({
    companyName: '',
    companyEmail: '',
    companyWebsite: '',
    industry: '',
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    acceptTerms: false
  });

  const handleIndividualChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setIndividualData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleCompanyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setCompanyData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent, type: 'individual' | 'company') => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const endpoint = type === 'individual' 
      ? '/api/backbone/oneid/auth/register/individual'
      : '/api/backbone/oneid/auth/register/company';

    const data = type === 'individual' ? individualData : companyData;

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        router.push('/abnsalesops/auth/login?message=registration-success');
      } else {
        setError(result.error || 'Registration failed');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const IndividualForm = () => (
    <form onSubmit={(e) => handleSubmit(e, 'individual')} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="firstName" className="text-sm font-medium">
            First Name *
          </label>
          <Input
            id="firstName"
            name="firstName"
            value={individualData.firstName}
            onChange={handleIndividualChange}
            placeholder="John"
            required
            disabled={loading}
          />
        </div>
        <div className="space-y-2">
          <label htmlFor="lastName" className="text-sm font-medium">
            Last Name *
          </label>
          <Input
            id="lastName"
            name="lastName"
            value={individualData.lastName}
            onChange={handleIndividualChange}
            placeholder="Doe"
            required
            disabled={loading}
          />
        </div>
      </div>

      <div className="space-y-2">
        <label htmlFor="username" className="text-sm font-medium">
          Username *
        </label>
        <Input
          id="username"
          name="username"
          value={individualData.username}
          onChange={handleIndividualChange}
          placeholder="john_salesrep"
          required
          disabled={loading}
        />
      </div>

      <div className="space-y-2">
        <label htmlFor="email" className="text-sm font-medium">
          Email (Optional)
        </label>
        <Input
          id="email"
          name="email"
          type="email"
          value={individualData.email}
          onChange={handleIndividualChange}
          placeholder="<EMAIL>"
          disabled={loading}
        />
      </div>

      <div className="space-y-2">
        <label htmlFor="password" className="text-sm font-medium">
          Password *
        </label>
        <div className="relative">
          <Input
            id="password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            value={individualData.password}
            onChange={handleIndividualChange}
            placeholder="Enter a secure password"
            required
            disabled={loading}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            disabled={loading}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <input
          id="acceptTerms"
          name="acceptTerms"
          type="checkbox"
          checked={individualData.acceptTerms}
          onChange={handleIndividualChange}
          required
          disabled={loading}
          className="rounded border-gray-300"
        />
        <label htmlFor="acceptTerms" className="text-sm text-gray-600">
          I accept the{' '}
          <a href="/terms" className="text-blue-600 hover:text-blue-800 underline">
            Terms and Conditions
          </a>
        </label>
      </div>

      <Button
        type="submit"
        className="w-full bg-blue-600 hover:bg-blue-700"
        disabled={loading || !individualData.acceptTerms}
      >
        {loading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Creating Account...
          </>
        ) : (
          'Create Sales Rep Account'
        )}
      </Button>
    </form>
  );

  const CompanyForm = () => (
    <form onSubmit={(e) => handleSubmit(e, 'company')} className="space-y-4">
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900">Company Information</h4>
        
        <div className="space-y-2">
          <label htmlFor="companyName" className="text-sm font-medium">
            Company Name *
          </label>
          <Input
            id="companyName"
            name="companyName"
            value={companyData.companyName}
            onChange={handleCompanyChange}
            placeholder="Acme Sales Corporation"
            required
            disabled={loading}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="companyEmail" className="text-sm font-medium">
              Company Email
            </label>
            <Input
              id="companyEmail"
              name="companyEmail"
              type="email"
              value={companyData.companyEmail}
              onChange={handleCompanyChange}
              placeholder="<EMAIL>"
              disabled={loading}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="industry" className="text-sm font-medium">
              Industry
            </label>
            <Input
              id="industry"
              name="industry"
              value={companyData.industry}
              onChange={handleCompanyChange}
              placeholder="Technology, Retail, etc."
              disabled={loading}
            />
          </div>
        </div>

        <div className="space-y-2">
          <label htmlFor="companyWebsite" className="text-sm font-medium">
            Website
          </label>
          <Input
            id="companyWebsite"
            name="companyWebsite"
            type="url"
            value={companyData.companyWebsite}
            onChange={handleCompanyChange}
            placeholder="https://acme.com"
            disabled={loading}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Sales Manager Information</h4>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="firstName" className="text-sm font-medium">
              First Name *
            </label>
            <Input
              id="firstName"
              name="firstName"
              value={companyData.firstName}
              onChange={handleCompanyChange}
              placeholder="John"
              required
              disabled={loading}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="lastName" className="text-sm font-medium">
              Last Name *
            </label>
            <Input
              id="lastName"
              name="lastName"
              value={companyData.lastName}
              onChange={handleCompanyChange}
              placeholder="Doe"
              required
              disabled={loading}
            />
          </div>
        </div>

        <div className="space-y-2">
          <label htmlFor="username" className="text-sm font-medium">
            Username *
          </label>
          <Input
            id="username"
            name="username"
            value={companyData.username}
            onChange={handleCompanyChange}
            placeholder="john_manager"
            required
            disabled={loading}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            Email (Optional)
          </label>
          <Input
            id="email"
            name="email"
            type="email"
            value={companyData.email}
            onChange={handleCompanyChange}
            placeholder="<EMAIL>"
            disabled={loading}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="password" className="text-sm font-medium">
            Password *
          </label>
          <div className="relative">
            <Input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={companyData.password}
              onChange={handleCompanyChange}
              placeholder="Enter a secure password"
              required
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              disabled={loading}
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <input
          id="acceptTerms"
          name="acceptTerms"
          type="checkbox"
          checked={companyData.acceptTerms}
          onChange={handleCompanyChange}
          required
          disabled={loading}
          className="rounded border-gray-300"
        />
        <label htmlFor="acceptTerms" className="text-sm text-gray-600">
          I accept the{' '}
          <a href="/terms" className="text-blue-600 hover:text-blue-800 underline">
            Terms and Conditions
          </a>
        </label>
      </div>

      <Button
        type="submit"
        className="w-full bg-blue-600 hover:bg-blue-700"
        disabled={loading || !companyData.acceptTerms}
      >
        {loading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Creating Company...
          </>
        ) : (
          'Create Company Account'
        )}
      </Button>
    </form>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <div className="mb-6">
          <Link 
            href="/abnsalesops" 
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to ABN Sales Ops
          </Link>
        </div>

        <Card className="shadow-xl">
          <CardHeader className="space-y-1 text-center">
            <div className="mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
              <span className="text-white font-bold text-xl">ABS</span>
            </div>
            <CardTitle className="text-2xl font-bold">Join ABN Sales Ops</CardTitle>
            <CardDescription>
              Create your account to access the sales operations platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'individual' | 'company')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="individual" className="flex items-center gap-2">
                  <User size={16} />
                  Sales Rep
                </TabsTrigger>
                <TabsTrigger value="company" className="flex items-center gap-2">
                  <Building size={16} />
                  Sales Team/Company
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="individual" className="mt-6">
                <IndividualForm />
              </TabsContent>
              
              <TabsContent value="company" className="mt-6">
                <CompanyForm />
              </TabsContent>
            </Tabs>

            <div className="text-center mt-6">
              <div className="text-sm text-gray-600">
                Already have an account?{' '}
                <Link
                  href="/abnsalesops/auth/login"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Sign in
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="mt-6 text-center text-xs text-gray-500">
          Powered by ABN OneID Authentication System
        </div>
      </div>
    </div>
  );
}