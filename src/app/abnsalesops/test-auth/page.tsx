'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useABNSalesOpsAuth } from '../components/AuthGuard';
import { CheckCircle, User, Mail, Calendar } from 'lucide-react';

export default function TestAuthPage() {
  const { user, loading } = useABNSalesOpsAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading user information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Authentication Test</h1>
        <p className="text-gray-600 mt-2">Testing OneID integration with ABN Sales Ops</p>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-600">
            <CheckCircle className="w-5 h-5" />
            Authentication Successful
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <User className="w-5 h-5 text-blue-600" />
              <div>
                <label className="block text-sm font-medium text-gray-700">Username</label>
                <p className="text-gray-900">{user?.username || 'N/A'}</p>
              </div>
            </div>

            {user?.firstName && (
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <User className="w-5 h-5 text-blue-600" />
                <div>
                  <label className="block text-sm font-medium text-gray-700">Full Name</label>
                  <p className="text-gray-900">
                    {user.firstName} {user.lastName || ''}
                  </p>
                </div>
              </div>
            )}

            {user?.email && (
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Mail className="w-5 h-5 text-blue-600" />
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <p className="text-gray-900">{user.email}</p>
                </div>
              </div>
            )}

            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Calendar className="w-5 h-5 text-blue-600" />
              <div>
                <label className="block text-sm font-medium text-gray-700">Session Status</label>
                <p className="text-green-600 font-medium">Active</p>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="text-sm font-medium text-green-800 mb-2">Integration Status</h3>
            <ul className="text-sm text-green-700 space-y-1">
              <li>✅ OneID authentication working</li>
              <li>✅ User data retrieved successfully</li>
              <li>✅ Session validation active</li>
              <li>✅ ABN Sales Ops protected routes functional</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-500">
          This page is protected and only accessible to authenticated users
        </p>
      </div>
    </div>
  );
}