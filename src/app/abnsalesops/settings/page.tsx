'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useThemeStyles } from '@/themes/ThemeProvider';
import { useABNSalesOpsAuth } from '../components/AuthGuard';
import { User, Shield, Bell, Globe } from 'lucide-react';

export default function ABNSalesOpsSettings() {
  const { getCardClass, getHeadingClass, getButtonClass } = useThemeStyles();
  const { user } = useABNSalesOpsAuth();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className={getHeadingClass(1)}>Settings</h1>
        <p className="text-gray-600 mt-2">Manage your ABN Sales Ops account settings and preferences</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Profile Settings */}
        <Card className={getCardClass()}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Profile Information
            </CardTitle>
            <CardDescription>
              Update your personal information and profile details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Username</label>
              <p className="text-sm text-gray-600">{user?.username}</p>
            </div>
            {user?.firstName && (
              <div>
                <label className="block text-sm font-medium mb-1">Full Name</label>
                <p className="text-sm text-gray-600">
                  {user.firstName} {user.lastName || ''}
                </p>
              </div>
            )}
            {user?.email && (
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <p className="text-sm text-gray-600">{user.email}</p>
              </div>
            )}
            <Button className={getButtonClass('primary')} size="sm">
              Edit Profile
            </Button>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card className={getCardClass()}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Security
            </CardTitle>
            <CardDescription>
              Manage your account security and authentication
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Button className={getButtonClass('outline')} size="sm" disabled>
                Change Password
              </Button>
              <Button className={getButtonClass('outline')} size="sm" disabled>
                Enable Two-Factor Authentication
              </Button>
              <Button className={getButtonClass('outline')} size="sm" disabled>
                View Active Sessions
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              Security features are managed through ABN OneID
            </p>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card className={getCardClass()}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Notifications
            </CardTitle>
            <CardDescription>
              Configure how you receive notifications and updates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Email notifications</span>
                <input type="checkbox" defaultChecked className="rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Deal alerts</span>
                <input type="checkbox" defaultChecked className="rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Task reminders</span>
                <input type="checkbox" defaultChecked className="rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Weekly reports</span>
                <input type="checkbox" className="rounded" />
              </div>
            </div>
            <Button className={getButtonClass('primary')} size="sm">
              Save Preferences
            </Button>
          </CardContent>
        </Card>

        {/* Application Settings */}
        <Card className={getCardClass()}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              Application
            </CardTitle>
            <CardDescription>
              Customize your ABN Sales Ops experience
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium mb-1">Language</label>
                <select className="w-full p-2 border rounded text-sm">
                  <option>English</option>
                  <option>Vietnamese</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Timezone</label>
                <select className="w-full p-2 border rounded text-sm">
                  <option>Asia/Ho_Chi_Minh</option>
                  <option>UTC</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Dashboard Layout</label>
                <select className="w-full p-2 border rounded text-sm">
                  <option>Grid View</option>
                  <option>List View</option>
                  <option>Compact View</option>
                </select>
              </div>
            </div>
            <Button className={getButtonClass('primary')} size="sm">
              Apply Settings
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className={getHeadingClass(3)}>About ABN Sales Ops</h3>
        <p className="text-sm text-gray-600 mt-2">
          Version 1.0.0 - Powered by ABN OneID Authentication System
        </p>
        <p className="text-xs text-gray-500 mt-1">
          For support or feature requests, contact your system administrator.
        </p>
      </div>
    </div>
  );
}