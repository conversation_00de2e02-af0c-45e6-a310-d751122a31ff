[{"id": "job_hh_001", "title": "Senior Software Engineer - AI/ML", "company": {"name": "VinTech Solutions", "size": "large", "industry": "Technology", "location": "Ho Chi Minh City", "logo": "/logos/vintech.png"}, "description": "We are looking for a Senior Software Engineer specializing in AI/ML to join our growing team in Ho Chi Minh City. You will be working on cutting-edge machine learning projects for our fintech platform.", "requirements": ["5+ years of software development experience", "3+ years in AI/ML development", "Proficiency in Python, TensorFlow, PyTorch", "Experience with cloud platforms (AWS, GCP)", "Strong background in data science and statistics"], "responsibilities": ["Design and implement ML models for financial applications", "Collaborate with data scientists and engineers", "Optimize model performance and scalability", "Mentor junior developers", "Stay current with AI/ML trends and technologies"], "salary": {"currency": "USD", "min": 4000, "max": 8000, "period": "monthly", "negotiable": true}, "jobLevel": "senior", "jobType": "full-time", "location": "Ho Chi Minh City", "remoteWork": true, "relocationAssistance": true, "skills": [{"name": "Python", "level": "advanced", "required": true}, {"name": "Machine Learning", "level": "advanced", "required": true}, {"name": "TensorFlow", "level": "intermediate", "required": true}, {"name": "AWS", "level": "intermediate", "required": false}, {"name": "<PERSON>er", "level": "intermediate", "required": false}], "benefits": ["Competitive salary and equity", "Health insurance for family", "Annual performance bonus", "Professional development budget", "Flexible working hours", "Stock options"], "applicationDeadline": "2025-02-28", "startDate": "2025-03-15", "urgency": "high", "placementFee": {"percentage": 25, "currency": "USD", "guaranteePeriod": 6}, "clientContact": {"name": "<PERSON><PERSON>", "title": "Head of Engineering", "email": "<EMAIL>", "phone": "+84 9111 222 333"}, "postedBy": "hh_001", "postedAt": "2025-01-15T09:00:00.000Z", "status": "active", "applicationsCount": 12, "viewsCount": 89, "tags": ["ai", "ml", "fintech", "senior", "python", "remote"]}, {"id": "job_hh_002", "title": "Manufacturing Operations Director", "company": {"name": "Thaco Auto", "size": "enterprise", "industry": "Manufacturing", "location": "<PERSON><PERSON>", "logo": "/logos/thaco.png"}, "description": "We are seeking an experienced Manufacturing Operations Director to oversee our automotive production facilities. This role requires deep expertise in lean manufacturing and operational excellence.", "requirements": ["15+ years in manufacturing operations", "10+ years in automotive industry", "MBA or Engineering degree preferred", "Lean Six Sigma Black Belt certification", "Experience managing 500+ employees", "Fluent in English and Vietnamese"], "responsibilities": ["Lead manufacturing operations across multiple facilities", "Implement lean manufacturing principles", "Drive operational excellence initiatives", "Manage production budgets and P&L", "Ensure quality and safety standards", "Dev<PERSON>p and mentor management team"], "salary": {"currency": "USD", "min": 12000, "max": 20000, "period": "monthly", "negotiable": true}, "jobLevel": "executive", "jobType": "full-time", "location": "<PERSON><PERSON>", "remoteWork": false, "relocationAssistance": true, "skills": [{"name": "Lean Manufacturing", "level": "expert", "required": true}, {"name": "Automotive Industry", "level": "expert", "required": true}, {"name": "Operations Management", "level": "expert", "required": true}, {"name": "Six Sigma", "level": "advanced", "required": true}, {"name": "P&L Management", "level": "advanced", "required": true}], "benefits": ["Executive compensation package", "Company car and driver", "Comprehensive health insurance", "Annual bonus (3-6 months)", "Stock options", "Housing allowance", "International training opportunities"], "applicationDeadline": "2025-03-15", "startDate": "2025-04-01", "urgency": "medium", "placementFee": {"percentage": 30, "currency": "USD", "guaranteePeriod": 12}, "clientContact": {"name": "<PERSON><PERSON>", "title": "Chief Executive Officer", "email": "<EMAIL>", "phone": "+84 9222 333 444"}, "postedBy": "hh_002", "postedAt": "2025-01-10T14:30:00.000Z", "status": "active", "applicationsCount": 5, "viewsCount": 45, "tags": ["manufacturing", "automotive", "operations", "executive", "lean", "six-sigma"]}, {"id": "job_hh_003", "title": "Creative Director - Digital Marketing", "company": {"name": "Mindshare Vietnam", "size": "medium", "industry": "Marketing", "location": "Ho Chi Minh City", "logo": "/logos/mindshare.png"}, "description": "Join our creative team as Creative Director to lead innovative digital marketing campaigns for major brands. We're looking for someone with a strong portfolio and leadership experience.", "requirements": ["8+ years in creative/advertising industry", "5+ years in leadership roles", "Strong portfolio in digital campaigns", "Experience with major brand campaigns", "Proficiency in Adobe Creative Suite", "Understanding of digital marketing trends"], "responsibilities": ["Lead creative strategy for client campaigns", "Manage creative team of 15+ designers and copywriters", "Present creative concepts to clients", "Collaborate with account management and strategy teams", "Ensure brand consistency across all touchpoints", "Stay ahead of creative and digital trends"], "salary": {"currency": "USD", "min": 3000, "max": 6000, "period": "monthly", "negotiable": true}, "jobLevel": "senior", "jobType": "full-time", "location": "Ho Chi Minh City", "remoteWork": true, "relocationAssistance": false, "skills": [{"name": "Creative Direction", "level": "expert", "required": true}, {"name": "Digital Marketing", "level": "advanced", "required": true}, {"name": "Adobe Creative Suite", "level": "advanced", "required": true}, {"name": "Team Leadership", "level": "advanced", "required": true}, {"name": "Brand Strategy", "level": "intermediate", "required": false}], "benefits": ["Creative freedom and flexibility", "Health insurance", "Annual creative development budget", "Flexible working hours", "International conference attendance", "Performance bonuses"], "applicationDeadline": "2025-02-20", "startDate": "2025-03-01", "urgency": "medium", "placementFee": {"percentage": 20, "currency": "USD", "guaranteePeriod": 6}, "clientContact": {"name": "<PERSON>", "title": "Managing Director", "email": "<EMAIL>", "phone": "+84 9333 444 555"}, "postedBy": "hh_003", "postedAt": "2025-01-12T11:15:00.000Z", "status": "active", "applicationsCount": 8, "viewsCount": 67, "tags": ["creative", "marketing", "digital", "advertising", "leadership", "brand"]}, {"id": "job_hh_004", "title": "Backend Developer - Fintech", "company": {"name": "Momo Financial", "size": "large", "industry": "Technology", "location": "Ho Chi Minh City", "logo": "/logos/momo.png"}, "description": "We're hiring a Backend Developer to work on our core financial services platform. You'll be building scalable systems that handle millions of transactions daily.", "requirements": ["4+ years of backend development experience", "Strong knowledge of Java or Go", "Experience with microservices architecture", "Database design and optimization skills", "Knowledge of financial systems preferred"], "responsibilities": ["Develop and maintain backend services", "Design scalable system architectures", "Implement security best practices", "Optimize database performance", "Collaborate with frontend and mobile teams"], "salary": {"currency": "USD", "min": 3500, "max": 6500, "period": "monthly", "negotiable": true}, "jobLevel": "mid", "jobType": "full-time", "location": "Ho Chi Minh City", "remoteWork": true, "relocationAssistance": false, "skills": [{"name": "Java", "level": "advanced", "required": true}, {"name": "Microservices", "level": "intermediate", "required": true}, {"name": "SQL", "level": "advanced", "required": true}, {"name": "AWS", "level": "intermediate", "required": false}, {"name": "Redis", "level": "intermediate", "required": false}], "benefits": ["Competitive salary", "Health insurance", "Stock options", "Learning budget", "Flexible hours", "Free meals"], "applicationDeadline": "2025-02-15", "startDate": "2025-03-01", "urgency": "high", "placementFee": {"percentage": 22, "currency": "USD", "guaranteePeriod": 6}, "clientContact": {"name": "<PERSON><PERSON>", "title": "Engineering Manager", "email": "<EMAIL>", "phone": "+84 9444 555 666"}, "postedBy": "hh_001", "postedAt": "2025-01-18T16:45:00.000Z", "status": "active", "applicationsCount": 15, "viewsCount": 123, "tags": ["backend", "fintech", "java", "microservices", "remote"]}, {"id": "job_hh_005", "title": "Supply Chain Manager", "company": {"name": "Unilever Vietnam", "size": "enterprise", "industry": "Manufacturing", "location": "<PERSON><PERSON>", "logo": "/logos/unilever.png"}, "description": "Lead our supply chain operations for the Vietnam market. This role involves managing end-to-end supply chain processes for consumer goods.", "requirements": ["7+ years in supply chain management", "Experience in FMCG industry", "Strong analytical and problem-solving skills", "Knowledge of SAP or similar ERP systems", "Bachelor's degree in Supply Chain or related field"], "responsibilities": ["Manage supply chain operations across Vietnam", "Optimize inventory levels and logistics", "Work with suppliers and distributors", "Implement supply chain best practices", "Ensure compliance with regulations", "Lead cross-functional projects"], "salary": {"currency": "USD", "min": 4500, "max": 7500, "period": "monthly", "negotiable": true}, "jobLevel": "senior", "jobType": "full-time", "location": "<PERSON><PERSON>", "remoteWork": false, "relocationAssistance": true, "skills": [{"name": "Supply Chain Management", "level": "expert", "required": true}, {"name": "FMCG", "level": "advanced", "required": true}, {"name": "SAP", "level": "intermediate", "required": true}, {"name": "Analytics", "level": "advanced", "required": true}, {"name": "Project Management", "level": "intermediate", "required": false}], "benefits": ["Multinational company benefits", "Health insurance for family", "Annual bonus", "Career development programs", "International assignments", "Company car"], "applicationDeadline": "2025-02-25", "startDate": "2025-03-10", "urgency": "medium", "placementFee": {"percentage": 25, "currency": "USD", "guaranteePeriod": 9}, "clientContact": {"name": "<PERSON>", "title": "Country Manager", "email": "<EMAIL>", "phone": "+84 9555 666 777"}, "postedBy": "hh_002", "postedAt": "2025-01-14T10:20:00.000Z", "status": "active", "applicationsCount": 7, "viewsCount": 34, "tags": ["supply-chain", "fmcg", "manufacturing", "senior", "sap", "logistics"]}, {"id": "job_hh_006", "title": "UX/UI Designer - Mobile Apps", "company": {"name": "Grab Vietnam", "size": "large", "industry": "Technology", "location": "Ho Chi Minh City", "logo": "/logos/grab.png"}, "description": "Join our design team to create exceptional user experiences for millions of users across Southeast Asia. Focus on mobile app design and user research.", "requirements": ["4+ years of UX/UI design experience", "Strong portfolio in mobile app design", "Proficiency in Figma, Sketch, Adobe XD", "Experience with user research and testing", "Understanding of mobile design principles"], "responsibilities": ["Design mobile app interfaces and experiences", "Conduct user research and usability testing", "Create wireframes, prototypes, and design systems", "Collaborate with product and engineering teams", "Present design concepts to stakeholders", "Iterate based on user feedback and data"], "salary": {"currency": "USD", "min": 2500, "max": 4500, "period": "monthly", "negotiable": true}, "jobLevel": "mid", "jobType": "full-time", "location": "Ho Chi Minh City", "remoteWork": true, "relocationAssistance": false, "skills": [{"name": "UX Design", "level": "advanced", "required": true}, {"name": "UI Design", "level": "advanced", "required": true}, {"name": "Figma", "level": "advanced", "required": true}, {"name": "User Research", "level": "intermediate", "required": true}, {"name": "Prototyping", "level": "advanced", "required": false}], "benefits": ["Competitive salary", "Health insurance", "Stock options", "Flexible working hours", "Learning and development budget", "Free Grab rides", "Team building activities"], "applicationDeadline": "2025-02-18", "startDate": "2025-03-05", "urgency": "medium", "placementFee": {"percentage": 20, "currency": "USD", "guaranteePeriod": 6}, "clientContact": {"name": "<PERSON>", "title": "Head of Design", "email": "<EMAIL>", "phone": "+84 9666 777 888"}, "postedBy": "hh_003", "postedAt": "2025-01-16T13:30:00.000Z", "status": "active", "applicationsCount": 11, "viewsCount": 78, "tags": ["ux", "ui", "mobile", "design", "figma", "remote"]}, {"id": "job_hh_007", "title": "Head of Digital Marketing", "company": {"name": "Shopee <PERSON>", "size": "large", "industry": "E-commerce", "location": "Ho Chi Minh City", "logo": "/logos/shopee.png"}, "description": "Lead our digital marketing efforts across all channels. Drive user acquisition, retention, and brand awareness through innovative marketing strategies.", "requirements": ["8+ years in digital marketing", "5+ years in leadership roles", "Experience in e-commerce or tech industry", "Strong analytical and data-driven mindset", "Knowledge of performance marketing and growth hacking"], "responsibilities": ["Develop and execute digital marketing strategy", "Lead marketing team of 20+ professionals", "Manage marketing budgets and ROI optimization", "Drive user acquisition and retention campaigns", "Collaborate with product and business teams", "Monitor and analyze marketing performance"], "salary": {"currency": "USD", "min": 6000, "max": 10000, "period": "monthly", "negotiable": true}, "jobLevel": "lead", "jobType": "full-time", "location": "Ho Chi Minh City", "remoteWork": true, "relocationAssistance": true, "skills": [{"name": "Digital Marketing", "level": "expert", "required": true}, {"name": "Performance Marketing", "level": "advanced", "required": true}, {"name": "Team Leadership", "level": "advanced", "required": true}, {"name": "Data Analytics", "level": "advanced", "required": true}, {"name": "E-commerce", "level": "advanced", "required": false}], "benefits": ["Executive compensation package", "Health insurance for family", "Stock options", "Annual performance bonus", "International travel opportunities", "Professional development budget", "Flexible working arrangements"], "applicationDeadline": "2025-03-01", "startDate": "2025-03-20", "urgency": "high", "placementFee": {"percentage": 28, "currency": "USD", "guaranteePeriod": 9}, "clientContact": {"name": "<PERSON>", "title": "Country Head", "email": "<EMAIL>", "phone": "+84 9777 888 999"}, "postedBy": "hh_003", "postedAt": "2025-01-17T09:45:00.000Z", "status": "active", "applicationsCount": 6, "viewsCount": 92, "tags": ["marketing", "digital", "ecommerce", "leadership", "performance", "head"]}]