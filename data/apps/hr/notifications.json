{"notifications": [{"id": "notif-001", "userId": "user-001", "type": "job_match", "title": "New Job Match Found", "message": "A new job opportunity matches your profile: Senior Software Engineer at TechCorp", "data": {"jobId": "job-001", "jobTitle": "Senior Software Engineer", "companyName": "TechCorp", "matchScore": 92}, "read": true, "createdAt": "2024-01-15T10:30:00Z", "tenantId": "default", "module": "hunter"}, {"id": "notif-002", "userId": "user-001", "type": "application_update", "title": "Application Status Update", "message": "Your application for Product Manager position has been reviewed", "data": {"applicationId": "app-001", "jobTitle": "Product Manager", "status": "under_review"}, "read": false, "createdAt": "2024-01-14T15:45:00Z", "tenantId": "default", "module": "recruitment"}, {"id": "notif-003", "userId": "user-002", "type": "interview_scheduled", "title": "Interview Scheduled", "message": "Your interview for Data Scientist position is scheduled for tomorrow at 2:00 PM", "data": {"interviewId": "int-001", "jobTitle": "Data Scientist", "scheduledAt": "2024-01-16T14:00:00Z", "interviewType": "video"}, "read": false, "createdAt": "2024-01-15T09:15:00Z", "tenantId": "default", "module": "recruitment"}, {"id": "notif-004", "userId": "user-001", "type": "system", "title": "Profile Update Required", "message": "Please update your profile to improve job matching accuracy", "data": {"profileCompleteness": 75, "missingFields": ["skills", "experience"]}, "read": true, "createdAt": "2024-01-13T12:00:00Z", "tenantId": "default", "module": "system"}, {"id": "notif-005", "userId": "user-003", "type": "message", "title": "New Message from <PERSON><PERSON><PERSON><PERSON>", "message": "<PERSON> from InnovateTech has sent you a message regarding the UX Designer position", "data": {"senderId": "recruiter-001", "senderName": "<PERSON>", "companyName": "InnovateTech", "messageId": "msg-001"}, "read": false, "createdAt": "2024-01-15T16:20:00Z", "tenantId": "default", "module": "messaging"}, {"id": "2522a5a4-8915-4e10-a78d-d80822fac376", "userId": "user-001", "type": "system", "title": "Test Notification", "message": "This is a test notification", "read": false, "createdAt": "2025-07-21T01:16:05.841Z", "tenantId": "default", "module": "system"}, {"id": "79bfbeaa-18e5-42f3-8809-a13324e295e3", "userId": "user-001", "type": "system", "title": "Test API Notification", "message": "This notification was created via API test", "data": {"testId": "api-test-001"}, "read": true, "createdAt": "2025-07-21T01:16:42.983Z", "tenantId": "default", "module": "test"}, {"id": "070390fb-8870-4619-8217-96c7d9d1c38b", "userId": "user-001", "type": "system", "title": "Event Test Notification", "message": "This notification tests the event system", "data": {"eventTest": true, "timestamp": "2025-07-21T01:21:47.637Z"}, "read": true, "createdAt": "2025-07-21T01:21:47.692Z", "tenantId": "default", "module": "test-events"}, {"id": "5989f41a-953f-4b1d-ae48-b0816c5b2ff8", "userId": "user-001", "type": "system", "title": "Test API Notification", "message": "This notification was created via API test", "data": {"testId": "api-test-001"}, "read": true, "createdAt": "2025-07-21T01:34:49.593Z", "tenantId": "default", "module": "test"}, {"id": "80f36ebe-a483-418a-bbad-5291c9dfed3c", "userId": "user-001", "type": "system", "title": "Event Test Notification", "message": "This notification tests the event system", "data": {"eventTest": true, "timestamp": "2025-07-21T01:34:57.591Z"}, "read": true, "createdAt": "2025-07-21T01:34:57.644Z", "tenantId": "default", "module": "test-events"}]}