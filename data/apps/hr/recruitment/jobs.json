[{"id": "job_1", "title": "Senior Software Engineer", "description": "We are looking for a Senior Software Engineer to join our growing team. You will be responsible for designing, developing, and maintaining high-quality software applications using modern technologies.", "requirements": "• 5+ years of software development experience\n• Proficiency in React, Node.js, and TypeScript\n• Experience with cloud platforms (AWS, Azure, or GCP)\n• Strong problem-solving skills\n• Bachelor's degree in Computer Science or related field", "responsibilities": "• Design and develop scalable web applications\n• Collaborate with cross-functional teams\n• Mentor junior developers\n• Participate in code reviews and architectural decisions\n• Stay up-to-date with latest technologies and best practices", "benefits": "• Competitive salary and equity\n• Health and dental insurance\n• Flexible working hours\n• Professional development budget\n• Work from home options", "salaryMin": 8000, "salaryMax": 12000, "salaryType": "monthly", "location": "Singapore", "remoteWork": true, "employmentType": "full-time", "experienceLevel": "senior", "department": "Engineering", "companyId": "comp_1", "postedById": "user_1", "applicationDeadline": "2025-02-15", "startDate": "2025-03-01", "isUrgent": false, "status": "active", "tenantId": "default", "viewCount": 234, "applicationCount": 45, "skills": [{"name": "React", "level": "advanced", "isRequired": true}, {"name": "Node.js", "level": "intermediate", "isRequired": true}, {"name": "TypeScript", "level": "intermediate", "isRequired": false}, {"name": "AWS", "level": "intermediate", "isRequired": false}], "createdAt": "2025-01-15T10:00:00.000Z", "updatedAt": "2025-01-15T10:00:00.000Z"}, {"id": "job_2", "title": "Product Manager", "description": "Join our product team to drive innovation and user experience. You will be responsible for defining product strategy, working with engineering teams, and ensuring successful product launches.", "requirements": "• 3+ years of product management experience\n• Strong analytical and data-driven decision making\n• Experience with agile development methodologies\n• Excellent communication and leadership skills\n• MBA or relevant degree preferred", "responsibilities": "• Define product roadmap and strategy\n• Work closely with engineering and design teams\n• Conduct market research and user analysis\n• Manage product launches and go-to-market strategies\n• Track and analyze product metrics", "benefits": "• Competitive compensation package\n• Equity participation\n• Health insurance coverage\n• Learning and development opportunities\n• Flexible remote work", "salaryMin": 6000, "salaryMax": 9000, "salaryType": "monthly", "location": "Remote", "remoteWork": true, "employmentType": "full-time", "experienceLevel": "mid", "department": "Product", "companyId": "comp_2", "postedById": "user_2", "isUrgent": true, "status": "active", "tenantId": "default", "viewCount": 156, "applicationCount": 23, "skills": [{"name": "Product Strategy", "level": "advanced", "isRequired": true}, {"name": "Data Analysis", "level": "intermediate", "isRequired": true}, {"name": "Agile Methodology", "level": "intermediate", "isRequired": false}], "createdAt": "2025-01-10T14:30:00.000Z", "updatedAt": "2025-01-10T14:30:00.000Z"}, {"id": "job_3", "title": "UX Designer", "description": "We're seeking a talented UX Designer to create intuitive and engaging user experiences for our digital products. You will work closely with product and engineering teams to design user-centered solutions.", "requirements": "• 2+ years of UX/UI design experience\n• Proficiency in design tools (Figma, Sketch, Adobe Creative Suite)\n• Strong portfolio demonstrating user-centered design\n• Understanding of user research methodologies\n• Bachelor's degree in Design or related field", "responsibilities": "• Create wireframes, prototypes, and high-fidelity designs\n• Conduct user research and usability testing\n• Collaborate with product managers and developers\n• Maintain design systems and style guides\n• Present design concepts to stakeholders", "benefits": "• Competitive salary\n• Creative work environment\n• Professional development budget\n• Health insurance\n• Flexible working arrangements", "salaryMin": 4000, "salaryMax": 7000, "salaryType": "monthly", "location": "Ho Chi Minh City", "remoteWork": false, "employmentType": "contract", "experienceLevel": "junior", "department": "Design", "companyId": "comp_3", "postedById": "user_3", "applicationDeadline": "2025-02-28", "status": "paused", "tenantId": "default", "viewCount": 89, "applicationCount": 12, "skills": [{"name": "Figma", "level": "advanced", "isRequired": true}, {"name": "User Research", "level": "intermediate", "isRequired": true}, {"name": "Prototyping", "level": "intermediate", "isRequired": false}], "createdAt": "2025-01-08T09:15:00.000Z", "updatedAt": "2025-01-08T09:15:00.000Z"}]