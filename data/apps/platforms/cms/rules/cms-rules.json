{"version": "1.0", "rules": [{"id": "rule-001", "name": "Admin Full Access", "description": "Administrators have full access to all objects", "conditions": {"user.role": "admin"}, "permissions": ["read", "write", "delete"], "objectFilters": {"all": true}}, {"id": "rule-002", "name": "User Object Access", "description": "Users can access objects assigned to them", "conditions": {"user.role": "user"}, "permissions": ["read", "write"], "objectFilters": {"assignedToUser": true}}, {"id": "rule-003", "name": "Department Data Access", "description": "Users can access objects from their department", "conditions": {"user.role": "manager"}, "permissions": ["read", "write"], "objectFilters": {"department": "{user.department}"}}, {"id": "rule-004", "name": "Read Only Access", "description": "Viewers can only read assigned objects", "conditions": {"user.role": "viewer"}, "permissions": ["read"], "objectFilters": {"assignedToUser": true}}]}