{"version": "1.0", "objects": [{"id": "obj-001", "name": "Product Catalog", "type": "catalog", "path": "data/apps/platforms/abnshops/products.json", "description": "Main product catalog for ABN Shops", "metadata": {"department": "Sales", "lastModified": "2025-07-20T00:00:00Z", "size": "145KB"}, "tags": ["products", "catalog", "ecommerce"]}, {"id": "obj-002", "name": "User Analytics", "type": "analytics", "path": "data/apps/platforms/edu/users.json", "description": "Educational platform user data", "metadata": {"department": "Education", "lastModified": "2025-07-20T00:00:00Z", "size": "89KB"}, "tags": ["users", "analytics", "education"]}, {"id": "obj-003", "name": "Healthcare Records", "type": "records", "path": "data/apps/platforms/healthcare/myhealth/health-records.json", "description": "Patient health records system", "metadata": {"department": "Healthcare", "lastModified": "2025-07-20T00:00:00Z", "size": "234KB"}, "tags": ["healthcare", "records", "patients"]}]}