{"system": {"uptime": 99.97, "cpuUsage": 67.3, "memoryUsage": 45.8, "diskUsage": 23.1, "networkIO": 12.4}, "services": [{"name": "Web Server", "status": "running", "uptime": 99.98, "responseTime": 125, "requests": 15420}, {"name": "Database", "status": "running", "uptime": 99.95, "responseTime": 45, "requests": 8750}, {"name": "API Gateway", "status": "running", "uptime": 99.99, "responseTime": 89, "requests": 22100}, {"name": "<PERSON><PERSON>", "status": "running", "uptime": 99.92, "responseTime": 12, "requests": 45600}, {"name": "Message Queue", "status": "running", "uptime": 99.87, "responseTime": 34, "requests": 12300}, {"name": "File Storage", "status": "running", "uptime": 99.94, "responseTime": 156, "requests": 3450}, {"name": "Auth Service", "status": "running", "uptime": 99.96, "responseTime": 78, "requests": 9870}, {"name": "Backup Service", "status": "running", "uptime": 99.85, "responseTime": 234, "requests": 1250}], "alerts": [{"id": "alert-001", "severity": "medium", "message": "CPU usage above 65% for 10 minutes", "timestamp": "2024-01-15T10:25:00Z", "resolved": false}, {"id": "alert-002", "severity": "low", "message": "Disk space usage increased by 5% in last hour", "timestamp": "2024-01-15T10:15:00Z", "resolved": false}, {"id": "alert-003", "severity": "high", "message": "Database connection pool near capacity", "timestamp": "2024-01-15T09:45:00Z", "resolved": true}, {"id": "alert-004", "severity": "critical", "message": "API Gateway response time spike detected", "timestamp": "2024-01-15T09:30:00Z", "resolved": true}, {"id": "alert-005", "severity": "medium", "message": "Cache hit ratio below optimal threshold", "timestamp": "2024-01-15T09:15:00Z", "resolved": true}, {"id": "alert-006", "severity": "low", "message": "Backup service completed with warnings", "timestamp": "2024-01-15T08:00:00Z", "resolved": true}], "performance": {"avgResponseTime": 145, "throughput": 1250, "errorRate": 0.2, "availability": 99.97}, "lastUpdated": "2024-01-15T10:30:00Z"}