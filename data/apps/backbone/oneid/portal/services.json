{"lastUpdated": "2024-03-15T10:00:00.000Z", "availableServices": [{"id": "oneid-auth", "name": "OneID Authentication", "description": "Core authentication and user management system", "category": "core", "required": true, "price": 0, "features": ["Single Sign-On", "User Management", "Session Control", "<PERSON>"], "status": "operational", "uptime": 99.9, "adoptionRate": 100}, {"id": "admin-portal", "name": "Admin Management Portal", "description": "Comprehensive company and user management interface", "category": "core", "required": true, "price": 0, "features": ["Company Dashboard", "User Assignment", "Hierarchy Management", "Analytics"], "status": "operational", "uptime": 99.8, "adoptionRate": 80}, {"id": "analytics-suite", "name": "Analytics Suite", "description": "Advanced analytics and reporting capabilities", "category": "premium", "required": false, "price": 49, "features": ["Custom Reports", "Real-time Analytics", "Data Export", "API Metrics"], "status": "degraded", "uptime": 98.5, "adoptionRate": 60}, {"id": "api-gateway", "name": "API Gateway", "description": "Secure API management and rate limiting", "category": "infrastructure", "required": false, "price": 29, "features": ["Rate Limiting", "API Keys", "Request Logging", "Security Policies"], "status": "operational", "uptime": 99.7, "adoptionRate": 70}, {"id": "file-storage", "name": "File Storage Service", "description": "Secure cloud file storage and management", "category": "storage", "required": false, "price": 19, "features": ["10GB Storage", "File Sharing", "Version Control", "Backup & Recovery"], "status": "operational", "uptime": 99.6, "adoptionRate": 50}, {"id": "notification-service", "name": "Notification Service", "description": "Email and SMS notification system", "category": "communication", "required": false, "price": 15, "features": ["Email Templates", "SMS Notifications", "Delivery Tracking", "Automation"], "status": "operational", "uptime": 99.4, "adoptionRate": 85}, {"id": "document-management", "name": "Document Management", "description": "Document creation, storage, and collaboration", "category": "productivity", "required": false, "price": 39, "features": ["Document Editor", "Collaboration", "Templates", "Digital Signatures"], "status": "operational", "uptime": 99.2, "adoptionRate": 40}, {"id": "backup-service", "name": "Backup & Recovery", "description": "Automated backup and disaster recovery", "category": "infrastructure", "required": false, "price": 25, "features": ["Daily Backups", "Point-in-time Recovery", "Cross-region Backup", "Monitoring"], "status": "operational", "uptime": 99.1, "adoptionRate": 40}], "subscriptionTiers": [{"id": "starter", "name": "Starter", "description": "Perfect for small companies", "discount": 0, "maxUsers": 25, "features": ["Up to 25 users", "Basic support", "Standard features"]}, {"id": "standard", "name": "Standard", "description": "Great for growing businesses", "discount": 10, "maxUsers": 100, "features": ["Up to 100 users", "Priority support", "Advanced features", "10% discount"]}, {"id": "premium", "name": "Premium", "description": "For large organizations", "discount": 20, "maxUsers": -1, "features": ["Unlimited users", "24/7 support", "All features", "20% discount"]}], "companySubscriptions": {"comp_001_techcorp": {"tier": "premium", "services": ["oneid-auth", "admin-portal", "analytics-suite", "api-gateway"], "billingCycle": "annually", "autoRenewal": true, "nextBilling": "2024-12-01T00:00:00.000Z", "totalCost": 83.2}, "comp_002_healthplus": {"tier": "standard", "services": ["oneid-auth", "admin-portal", "notification-service"], "billingCycle": "monthly", "autoRenewal": true, "nextBilling": "2024-04-01T00:00:00.000Z", "totalCost": 13.5}, "comp_003_greenfinance": {"tier": "standard", "services": ["oneid-auth", "admin-portal", "file-storage"], "billingCycle": "monthly", "autoRenewal": true, "nextBilling": "2024-04-01T00:00:00.000Z", "totalCost": 17.1}, "comp_004_edulearn": {"tier": "starter", "services": ["oneid-auth", "admin-portal"], "billingCycle": "monthly", "autoRenewal": false, "nextBilling": "2024-04-01T00:00:00.000Z", "totalCost": 0}, "comp_005_retailmax": {"tier": "premium", "services": ["oneid-auth", "admin-portal", "analytics-suite", "api-gateway", "file-storage"], "billingCycle": "monthly", "autoRenewal": true, "nextBilling": "2024-04-01T00:00:00.000Z", "totalCost": 77.6}}}