#MONGODB_URI=mongodb+srv://aihumane:<EMAIL>/aihumane?retryWrites=true&w=majority
SMTP_HOST=email-smtp.us-east-1.amazonaws.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=AKIA5BI3T5T7DL3SDATJ
SMTP_PASS=BEuI55ZaIXTr7xsROuJlLP9Sro1S8d8Zen8CIyoGPUM0
SMTP_FROM=<EMAIL>
NODE_ENV=development
#NEXT_PUBLIC_TENANT_ID=vinchoco
NEXT_PUBLIC_TENANT_ID=abn
NEXT_APPROUTE_API_URL=https://green.abnasia.org
DATABASE_URL="postgresql://user:password@localhost:5432/dbname"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
MONGODB_URI=mongodb+srv://thanhson:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
OPENWEATHER_API_KEY=********************************
# Sentry
NEXT_PUBLIC_SENTRY_ENABLED=true
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/****************
NEXT_PUBLIC_SENTRY_ENVIRONMENT=development
NEXT_PUBLIC_SENTRY_TRACES_SAMPLE_RATE=0.1
NEXT_PUBLIC_SENTRY_REPLAYS_SESSION_SAMPLE_RATE=0.1
NEXT_PUBLIC_SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE=1.0
NEXT_PUBLIC_SENTRY_ORG=abnasia
NEXT_PUBLIC_SENTRY_PROJECT=abnasia
# Add your Sentry auth token here (get it from https://sentry.io/settings/account/api/auth-tokens/)
# SENTRY_AUTH_TOKEN=your_sentry_auth_token_here

# AppSignal
NEXT_PUBLIC_APPSIGNAL_ENABLED=true
NEXT_PUBLIC_APPSIGNAL_PUSH_API_KEY=f17970b4-7ebc-4855-846e-aef6191637ec
NEXT_PUBLIC_APPSIGNAL_APP_NAME=abnasia
NEXT_PUBLIC_APPSIGNAL_ENVIRONMENT=development

# OpenTelemetry
OPENTELEMETRY_ENABLED=false
OPENTELEMETRY_SERVICE_NAME=abn-green
OPENTELEMETRY_ENDPOINT=http://localhost:4318/v1/traces 
NEXTAUTH_SECRET=9d239c7b5af462f2a4954356281c3ad6fe770c71b0fe1a9e291d64af06e7dc15
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
NEXT_PUBLIC_GROQ_API_KEY=your_groq_api_key_here

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyAnF0IatgC3QoxSDBitqwnvYndPuowY23c
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=abngreen1.firebaseapp.com
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://your-project.firebaseio.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=abngreen1
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=abngreen1.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=588762931132
NEXT_PUBLIC_FIREBASE_APP_ID=1:588762931132:web:4e40cc3f943a812ccc15b9

# ============================================
# ABN Green OneTalk API Configuration
# ============================================

# Email Service Configuration
# Gmail API
GMAIL_CLIENT_ID=your_gmail_client_id_here
GMAIL_CLIENT_SECRET=your_gmail_client_secret_here
GMAIL_REFRESH_TOKEN=your_gmail_refresh_token_here

# Microsoft Graph API (Outlook/Exchange)
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here
MICROSOFT_TENANT_ID=your_microsoft_tenant_id_here

# Facebook/Meta Platform APIs
# Facebook Graph API
FACEBOOK_APP_ID=***************
FACEBOOK_APP_SECRET=********************************
FACEBOOK_ACCESS_TOKEN=your_facebook_page_access_token_here
FACEBOOK_VERIFY_TOKEN=your_facebook_verify_token_here

# Instagram Platform API (requires Facebook app)
INSTAGRAM_CLIENT_ID=your_instagram_client_id_here
INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret_here
INSTAGRAM_ACCESS_TOKEN=your_instagram_user_access_token_here
INSTAGRAM_BUSINESS_ACCOUNT_ID=your_instagram_business_account_id_here
INSTAGRAM_VERIFY_TOKEN=your_instagram_verify_token_here

# WhatsApp Business API
WHATSAPP_PHONE_NUMBER_ID=your_whatsapp_phone_number_id_here
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token_here
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_whatsapp_verify_token_here
WHATSAPP_BUSINESS_ACCOUNT_ID=your_whatsapp_business_account_id_here

# Zalo Official Account API
ZALO_APP_ID=3895436254982577950
ZALO_APP_SECRET=JsjuWKpdSYVwlW5Fm6nl
ZALO_OA_ID=your_zalo_oa_id_here
ZALO_ACCESS_TOKEN=your_zalo_access_token_here

# Webhook URLs (for development - replace with your ngrok URL)
WEBHOOK_BASE_URL=https://green.abnasia.org
FACEBOOK_WEBHOOK_URL=${WEBHOOK_BASE_URL}/api/webhooks/facebook
INSTAGRAM_WEBHOOK_URL=${WEBHOOK_BASE_URL}/api/webhooks/instagram
WHATSAPP_WEBHOOK_URL=${WEBHOOK_BASE_URL}/api/webhooks/whatsapp
ZALO_WEBHOOK_URL=${WEBHOOK_BASE_URL}/api/webhooks/zalo

# OneTalk Configuration
ONETALK_ENCRYPTION_KEY=2a13c19241a5db31e6694fc175190acf
ONETALK_TOKEN_KEY=60f4d88c1d687d44ce4040776b495012
NEXT_TELEMETRY_DISABLED=1