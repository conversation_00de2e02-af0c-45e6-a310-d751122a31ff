# HR Recruitment Portal API Documentation

## Overview

The HR Recruitment Portal provides both internal web interfaces and external APIs for HR system integrations. This documentation covers the external API endpoints that allow third-party HR systems to post jobs and manage applications.

## Authentication

All external API requests require an API key to be included in the `x-api-key` header.

```bash
curl -H "x-api-key: your_api_key_here" \
     -H "Content-Type: application/json" \
     https://your-domain.com/api/hr/external/jobs
```

### Getting an API Key

API keys are managed by system administrators and are tied to specific companies and tenants. Contact your system administrator to obtain an API key with the appropriate permissions.

## External API Endpoints

### 1. Get Jobs

Retrieve job postings for your company.

**Endpoint:** `GET /api/hr/external/jobs`

**Parameters:**
- `status` (optional): Filter by job status (`active`, `paused`, `closed`, `draft`)
- `department` (optional): Filter by department
- `employment_type` (optional): Filter by employment type
- `limit` (optional): Number of jobs to return (default: 50, max: 100)
- `offset` (optional): Number of jobs to skip (default: 0)
- `since` (optional): ISO date string to get jobs updated since this date

**Example Request:**
```bash
curl -H "x-api-key: hr_sk_test_1234567890abcdef" \
     "https://your-domain.com/api/hr/external/jobs?status=active&limit=10"
```

**Example Response:**
```json
{
  "jobs": [
    {
      "id": "job_1234",
      "title": "Senior Software Engineer",
      "description": "We are looking for a Senior Software Engineer...",
      "requirements": "5+ years of experience...",
      "responsibilities": "Design and develop applications...",
      "benefits": "Competitive salary, health insurance...",
      "salary": {
        "min": 8000,
        "max": 12000,
        "type": "monthly"
      },
      "location": "Singapore",
      "remote_work": true,
      "employment_type": "full-time",
      "experience_level": "senior",
      "department": "Engineering",
      "application_deadline": "2025-02-15",
      "start_date": "2025-03-01",
      "is_urgent": false,
      "status": "active",
      "skills": [
        {
          "name": "React",
          "level": "advanced",
          "isRequired": true
        }
      ],
      "created_at": "2025-01-15T10:00:00.000Z",
      "updated_at": "2025-01-15T10:00:00.000Z"
    }
  ],
  "pagination": {
    "total": 45,
    "limit": 10,
    "offset": 0,
    "has_more": true
  },
  "meta": {
    "company_id": "comp_1",
    "tenant_id": "default",
    "timestamp": "2025-01-20T12:00:00.000Z"
  }
}
```

### 2. Create Job

Create a new job posting from your HR system.

**Endpoint:** `POST /api/hr/external/jobs`

**Required Permissions:** `jobs:create`

**Required Fields:**
- `title`: Job title
- `description`: Job description
- `employment_type`: Type of employment (`full-time`, `part-time`, `contract`, `freelance`, `internship`)
- `experience_level`: Required experience level (`entry`, `junior`, `mid`, `senior`, `lead`, `executive`)

**Optional Fields:**
- `requirements`: Job requirements
- `responsibilities`: Job responsibilities  
- `benefits`: Benefits and perks
- `salary`: Salary information object with `min`, `max`, and `type` fields
- `location`: Job location
- `remote_work`: Boolean indicating if remote work is available
- `department`: Department name
- `application_deadline`: Application deadline (ISO date string)
- `start_date`: Expected start date (ISO date string)
- `is_urgent`: Boolean indicating if this is urgent hiring
- `skills`: Array of required skills with `name`, `level`, and `isRequired` fields
- `status`: Job status (defaults to `active`)
- `external_id`: Your system's internal ID for this job (for future updates)

**Example Request:**
```bash
curl -X POST \
     -H "x-api-key: hr_sk_test_1234567890abcdef" \
     -H "Content-Type: application/json" \
     -d '{
       "title": "Senior Software Engineer",
       "description": "We are looking for a Senior Software Engineer to join our team...",
       "employment_type": "full-time",
       "experience_level": "senior",
       "department": "Engineering",
       "location": "Singapore",
       "remote_work": true,
       "salary": {
         "min": 8000,
         "max": 12000,
         "type": "monthly"
       },
       "skills": [
         {
           "name": "React",
           "level": "advanced",
           "isRequired": true
         },
         {
           "name": "Node.js",
           "level": "intermediate",
           "isRequired": true
         }
       ],
       "external_id": "JOB-2025-001"
     }' \
     https://your-domain.com/api/hr/external/jobs
```

**Example Response:**
```json
{
  "job": {
    "id": "job_ext_1705747200_abc123",
    "title": "Senior Software Engineer",
    "description": "We are looking for a Senior Software Engineer...",
    "employment_type": "full-time",
    "experience_level": "senior",
    "status": "active",
    "created_at": "2025-01-20T12:00:00.000Z",
    "updated_at": "2025-01-20T12:00:00.000Z"
  },
  "message": "Job created successfully"
}
```

### 3. Update Job

Update an existing job posting.

**Endpoint:** `PUT /api/hr/external/jobs?id=job_id` or `PUT /api/hr/external/jobs?external_id=your_id`

**Required Permissions:** `jobs:update`

**Parameters:**
- `id`: Internal job ID, OR
- `external_id`: Your system's external ID for the job

You can update any of the fields that are available when creating a job.

**Example Request:**
```bash
curl -X PUT \
     -H "x-api-key: hr_sk_test_1234567890abcdef" \
     -H "Content-Type: application/json" \
     -d '{
       "status": "paused",
       "salary": {
         "min": 9000,
         "max": 13000,
         "type": "monthly"
       }
     }' \
     "https://your-domain.com/api/hr/external/jobs?external_id=JOB-2025-001"
```

## Error Handling

All API endpoints return appropriate HTTP status codes and error messages:

- `200`: Success
- `201`: Created successfully
- `400`: Bad request (missing required fields, validation errors)
- `401`: Unauthorized (missing or invalid API key)
- `403`: Forbidden (insufficient permissions)
- `404`: Not found
- `409`: Conflict (duplicate external_id, etc.)
- `500`: Internal server error

**Error Response Format:**
```json
{
  "error": "Human readable error message",
  "code": "ERROR_CODE"
}
```

## Rate Limiting

API requests are rate limited to prevent abuse:
- 1000 requests per hour per API key
- Burst allowance of 100 requests per minute

When rate limits are exceeded, you'll receive a `429 Too Many Requests` response.

## Webhooks (Coming Soon)

Webhook support for real-time notifications about application events will be available in a future release.

## Integration Examples

### Python Example

```python
import requests

API_KEY = "hr_sk_test_1234567890abcdef"
BASE_URL = "https://your-domain.com/api/hr/external"

headers = {
    "x-api-key": API_KEY,
    "Content-Type": "application/json"
}

# Create a job
job_data = {
    "title": "Software Engineer",
    "description": "Join our engineering team...",
    "employment_type": "full-time",
    "experience_level": "mid",
    "department": "Engineering",
    "external_id": "ENG-2025-042"
}

response = requests.post(f"{BASE_URL}/jobs", headers=headers, json=job_data)
if response.status_code == 201:
    job = response.json()["job"]
    print(f"Job created with ID: {job['id']}")
else:
    print(f"Error: {response.json()}")
```

### Node.js Example

```javascript
const axios = require('axios');

const API_KEY = 'hr_sk_test_1234567890abcdef';
const BASE_URL = 'https://your-domain.com/api/hr/external';

const headers = {
  'x-api-key': API_KEY,
  'Content-Type': 'application/json'
};

// Get active jobs
async function getActiveJobs() {
  try {
    const response = await axios.get(`${BASE_URL}/jobs?status=active`, { headers });
    console.log(`Found ${response.data.jobs.length} active jobs`);
    return response.data.jobs;
  } catch (error) {
    console.error('Error fetching jobs:', error.response?.data || error.message);
  }
}
```

## Support

For technical support or questions about the API:
- Check the application logs for detailed error messages
- Verify your API key has the correct permissions
- Ensure all required fields are provided in requests
- Contact your system administrator for API key issues

## Changelog

### Version 1.0 (2025-01-20)
- Initial release
- Job creation, reading, and updating endpoints
- API key authentication
- Basic error handling and validation