
================================================================================START
nvm use 22.17.0

Extra details when build:
NODE_ENV=production NEXT_PRIVATE_DEBUG=true DEBUG=* yarn build
find . -type d -name "node_modules"
    delete all those node_modules otherwise yarn build also works on them. Fuck.

    
================================================================================END



================================================================================START

OneID:

<EMAIL> / demo123



WIP - trying a local build and deploy workflow:

- One time: git submodule add https://github.com/stevennt/abn.green.build.git abn.green.build
- Option A: Server-side Building
  - On server: 
        sh build.deploy.sh
        sh build.deploy.sh --force
- Option B: Local Building + Server Deployment
  - sh build.local.sh
  - sh build.deploy.sh --prebuilt


Server Deploy with Prebuilt (build.deploy.sh --prebuilt)
1. Update submodule → gets latest .next/ and public/
2. Copy .next/ and public/ to project root
3. Run promote.js → moves .next/ to serve/.next 
4. Copy public/ to serve/public
5. Restart Docker

Server Deploy with Server Build (build.deploy.sh)
1. Build → creates .next/
2. Run promote.js → moves .next/ to serve/.next
3. Copy public/ to serve/public  
4. Restart Docker

================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START
WISE: wise.abnasia.org

Admin: eduwise_admin / admin123
Teacher: eduwise_teacher / password123
Student: eduwise_student / password123

http://wise.localhost:3000/backend/login

================================================================================END


================================================================================START
Prompts:
================================================================================END


================================================================================START
Prompt to create a sample login screen similar to what I have on the main abn.green page:



================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START
Create Paper Theme: Convert to Paper Theme:

src/themes/THEMING_GUIDE.md

Convert the existing app at src/app/platforms/b2b/procurement/ to use the paper wireframe theme system:

CONVERSION STEPS:
1. Add ThemeProvider to layout.tsx (keep as server component)
2. Create layout-client.tsx for theme-aware UI (client component)
3. Convert all pages to use theme hooks where needed
4. Replace hardcoded styles with theme utilities
5. Add ThemeToggleButton to navigation
6. Update all components to be theme-aware

SPECIFIC UPDATES NEEDED:
- Buttons: Replace with getButtonClass('primary'|'secondary'|'outline')
- Cards: Replace with getCardClass()
- Headings: Replace with getHeadingClass(1-6)
- Forms: Replace with getInputClass()
- Conditional styling: Use isPaperTheme for custom styles

MAINTAIN:
- All existing functionality
- Responsive design
- Performance
- Accessibility

Please provide the complete conversion with all files updated.

================================================================================END


================================================================================START


================================================================================END


Cache: http://localhost:3000/admin/cache
Based on the code, here's how writes work:

EnhancedCacheService.writeFile() → Writes to disk first
Then calls JsonCacheManager.writeFile() → Updates memory cache
Write queue protection prevents concurrent writes to same file


Convert the [APP_NAME] app at [APP_PATH] to use the existing JSON cache system instead of direct file reads. Please:

IMPORTANT: Use the existing EnhancedCacheService - DO NOT create new cache service wrapper files for individual apps.

1. Identify all direct file read operations (fs.readFile, fs.readFileSync, JSON.parse)
2. Replace them with cache-enabled reads using the existing EnhancedCacheService.getInstance()
3. Update write operations to use cache-safe methods from the existing cache utilities
4. Register all JSON files used by the app in the existing cache configuration at src/lib/cache/cacheConfig.ts
5. Create a migration test to verify performance improvements
6. Provide before/after performance statistics

Files to cache: [LIST OF JSON FILES]
Current read patterns: [DESCRIBE CURRENT USAGE]
Performance requirements: [SPECIFY REQUIREMENTS]

Example implementation:
import { EnhancedCacheService } from '@/lib/cache/enhanced-cache-service';
const cacheService = EnhancedCacheService.getInstance();
const data = await cacheService.get('data/your-file.json');

Convert the farmerapp app at src/app/farmerapp/ to use the existing JSON cache system instead of direct file reads. Please:

IMPORTANT: Use the existing EnhancedCacheService - DO NOT create new cache service wrapper files for individual apps.

1. Identify all direct file read operations (fs.readFile, fs.readFileSync, JSON.parse)
2. Replace them with cache-enabled reads using the existing EnhancedCacheService.getInstance()
3. Update write operations to use cache-safe methods from the existing cache utilities
4. Register all JSON files used by the app in the existing cache configuration at src/lib/cache/cacheConfig.ts
5. Create a migration test to verify performance improvements
6. Provide before/after performance statistics

Example implementation:
import { EnhancedCacheService } from '@/lib/cache/enhanced-cache-service';
const cacheService = EnhancedCacheService.getInstance();
const data = await cacheService.get('data/your-file.json');


PATH="/Users/<USER>/.nvm/versions/node/v20.18.3/bin:$PATH" yarn install

Publish:
gh pr create --base DEV --head DEV.1 --title "DEV.1 to DEV" --body "Publishing from DEV.1 to DEV" && PR_NUMBER=$(gh pr list | head -n 1 | awk '{print $1}') && gh pr merge $PR_NUMBER --merge


Submodules for files:
- Private Files: data/abn.green.data.1
- Public Files:  data/abn.green.data.public.1


FATAL ERROR: Reached heap limit Allocation failed - JavaScript heap out of memory
export NODE_OPTIONS="--max-old-space-size=16384"
export NODE_OPTIONS="--max-old-space-size=8192"

================================================================================START
QR is working:
http://localhost:3001/qr/daceagent

http://localhost:3001/qr/dacefarmer



When add new domain to docker compose: on server, need to: docker compose stop , and docker compose up -d 
just docker compose restart won't work


https://qr.abnasia.org/s/xzz9fa4f

https://qr.abnasia.org/s/xzz9ftkhe


https://qr.abnasia.org/dacefarmer

https://qr.abnasia.org/daceagent


================================================================================END




================================================================================START
Good Deals: zalo mini apps





================================================================================END



================================================================================START
Login Token:
 http://localhost:3000/api/auth/debug-token

Authentication:
- src/middleware.ts
- callbackUrl part was tricky
- .env.production: need to set properly else production doesn't know which URL it is on
- 




================================================================================END

================================================================================START
Tenants, Deployment Profiles:
src/config/deployment-profiles.json


Process to add a new tenant / company:
- Add a config folder for that tenant:
  cd src/data
  cp -r abn minirent
  edit the header files
- Logo:                     cp ..png public/images/

- Define the tenant: Edit this file:           src/config/deployment-profiles.json
- Login Front page and Authentication: Also need to edit this file (duplication, need to change / merge these two):
          + src/auth/config/authSettings.json
          + Add an user
          + Add a tenant profile (that displays on the login front page)





                

================================================================================END



================================================================================START
Deploy new code on server:


nvm use 20
yarn

16GB:
export NODE_OPTIONS="--max-old-space-size=16384"

8GB:
export NODE_OPTIONS="--max-old-space-size=8192"
sh build.deploy.sh
Whenever add a new domain to the docker, it is NOT enough to do sh build.deploy.sh, 
it is needed to do: docker-compose -f docker-compose-abn.green.yml stop then up (or maybe: docker-compose -f docker-compose-abn.green.yml up -d)


docker-compose -f docker-compose-abn.green.yml stop
docker-compose -f docker-compose-abn.green.yml up -d

need a better build process since while doing yarn build, the site green.abnasia.org turns blank


================================================================================END


================================================================================START
Display Charts on the Dashboard:
- First, define the charts: src/data/vinchoco/charts.json
- Second, define the blocks: src/data/vinchoco/DashboardBlocks.json
- Third, what blocks where (including the dashboard): src/data/vinchoco/SidebarMenu.json: pick what menu item which blocks from the src/data/vinchoco/DashboardBlocks.json

================================================================================END



================================================================================START


================================================================================END


================================================================================START
Full House Work Pool: http://localhost:3004/booksy/apps/fullhouse/workpool
Create Task: http://localhost:3004/booksy/apps/fullhouse/workpool/create
Provider Work Pool: http://localhost:3004/booksy/apps/providers/workpool
Admin Dashboard: http://localhost:3004/booksy/apps/workpool/admin


================================================================================END











================================================================================START
Fixing some issues:
.eslintrc.json:
//,
    //"**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"



================================================================================END


================================================================================START
Docs:
Login:
#       modified:   src/app/auth/login/page.tsx
#       modified:   src/auth/auth-config.ts
#       modified:   src/auth/config/authSettings.json
#       modified:   src/auth/helpers.ts
#       modified:   src/types/authSettings.ts


================================================================================END


================================================================================START
Authentication: Now use this:
src/
  auth/
    authSettings.json    # JSON data structure for auth
    auth-config.ts        # NextAuth configuration
    helpers.ts           # Authentication helper functions
  app/
    api/
      auth/
        [...nextauth]/
          route.ts       # NextAuth API route (minimal)
  types/
    authSettings.d.ts  # TypeScript types for auth structure
    json.d.ts           # JSON module declaration
    next-auth.d.ts      # NextAuth type extensions




Use:
<EMAIL>
Pass: password123


================================================================================END


================================================================================START
Monitoring Visitor Counts:
Import:
import { VisitMonitor } from "@/app/admin/monitoring/widget/widget";
Then:
  // Initialize visit monitoring when the component mounts
  useEffect(() => {
    // Initialize the monitoring widget with the profile page path
    const monitor = new VisitMonitor({
      endpoint: '/api/monitor/track',
      debug: false,
      autoTrack: true
    });
    


The below did NOT work:
Ok, so one of the two methods below:

Method 1: Simple:
Import this:
          import Script from 'next/script';
Add this to after the return statement:
<Script 
        src="/api/monitor/widget.js" 
        strategy="afterInteractive"
/>
This method also prints out the page where it was visitted.

Optional: or if want page id as well:
<Script 
  src="/api/monitor/widget.js?pageId=asdfsadf" 
  strategy="afterInteractive"
/>


Ey those did NOT work.

================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END



================================================================================START
Data Model:


================================================================================END


================================================================================START


================================================================================END



================================================================================START

Implementation Flow
The data flows through the layers like this:

API/UI Layer: Receives/sends domain objects through services
Service Layer: Orchestrates domain logic using repositories
Repository Layer: Abstracts data access and uses mappers
Mapper Layer: Converts between domain models and storage schema
Storage Layer: Actual database or storage implementation
This pattern follows Domain-Driven Design principles and provides a clean, maintainable architecture that minimizes changes when your schema evolves.
================================================================================END

================================================================================START

http://localhost:3006/monitoring/dashboards



================================================================================END


================================================================================START
Prisma Studio:
npx prisma studio
Environment variables loaded from .env
Prisma schema loaded from prisma/schema.prisma
Prisma Studio is up on http://localhost:5555


Current Authentication:
- Username:   <EMAIL>
- Pass:       admin123


To start clean:

# 1. Remove existing database and migrations
rm -rf prisma/migrations prisma/dev.db

# 2. Generate fresh client
npx prisma generate

# 3. Create and apply initial migration
npx prisma migrate dev --name init

# 4. Seed the database with the above data
npx prisma db seed

npx prisma studio


Now that we've fixed the type issues and verified the database is properly seeded, you should be able to:
Access the login page at /login
Log in with these credentials:
Email: <EMAIL>
Password: admin123



This also works:
curl -X POST http://localhost:3006/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'



This works:
curl -X POST http://localhost:3006/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123",
    "tenantId": "default"
  }'


  CSRF_TOKEN=$(curl -X GET http://localhost:3006/api/auth/csrf -s | jq -r '.csrfToken') && \
curl -X POST http://localhost:3006/api/auth/callback/credentials \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.csrf-token=$CSRF_TOKEN" \
  -d "{
    \"email\": \"<EMAIL>\",
    \"password\": \"admin123\",
    \"tenantId\": \"default\",
    \"csrfToken\": \"$CSRF_TOKEN\",
    \"json\": true
  }"


CSRF_TOKEN=$(curl -X GET http://localhost:3006/api/auth/csrf -s | jq -r '.csrfToken') && \
curl -v -X POST http://localhost:3006/api/auth/callback/credentials \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.csrf-token=$CSRF_TOKEN" \
  -d "{
    \"email\": \"<EMAIL>\",
    \"password\": \"admin123\",
    \"tenantId\": \"default\",
    \"csrfToken\": \"$CSRF_TOKEN\",
    \"json\": true
  }"


curl -X POST http://localhost:3004/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123",
    "tenantId": "default"
  }'
{"error":"Invalid credentials"}%                                                                            


thanhson@macmini abn.green % curl -X POST http://localhost:3004/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
{"error":"Invalid credentials"}%                  



================================================================================END

Website Management Module:
Create theme: 
curl -X POST http://localhost:3004/api/templates -H "Content-Type: application/json" -d '{"name":"Business Landing Page","description":"A professional template for business websites","thumbnail":"https://example.com/business-template.jpg","status":"active","structure":{"layout":"default","availableSections":[{"type":"hero","name":"Hero Section","description":"Main banner section","fields":[{"name":"title","type":"text","required":true,"default":"Welcome to Our Business"},{"name":"subtitle","type":"text","required":false,"default":"Your trusted partner in success"}]},{"type":"features","name":"Features Section","description":"Highlight key features or services","fields":[{"name":"features","type":"list","required":true,"default":[{"title":"Feature 1","description":"Description of feature 1"},{"title":"Feature 2","description":"Description of feature 2"},{"title":"Feature 3","description":"Description of feature 3"}]}]}]},"defaultTheme":{"primaryColor":"#007bff","secondaryColor":"#6c757d","fontFamily":"Arial"}}'            
           

================================================================================START
Work Queue:

curl -X POST http://localhost:3004/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "ThanhSon01"
  }'


  curl -X POST http://localhost:3004/api/work-queue \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.FeXCOjHbjiwkzz9MAcycwnfZeGPJJOiA5UZ9RoOAmI0" \
  -d '{
    "type": "DATA_UPDATE",
    "data": {
      "farmerId": "FARMER001",
      "field": "address",
      "newValue": "123 New Street"
    }
  }'


curl -X GET http://localhost:3004/api/work-queue \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.FeXCOjHbjiwkzz9MAcycwnfZeGPJJOiA5UZ9RoOAmI0"


curl -X GET "http://localhost:3004/api/work-queue?status=PENDING&type=DATA_UPDATE" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.FeXCOjHbjiwkzz9MAcycwnfZeGPJJOiA5UZ9RoOAmI0"


curl -X PUT http://localhost:3004/api/work-queue/81af4fab-9fb6-46e8-abd4-ce422224535c/status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.FeXCOjHbjiwkzz9MAcycwnfZeGPJJOiA5UZ9RoOAmI0" \
  -d '{
    "status": "IN_PROGRESS",
    "notes": "Processing farmer address update"
  }'


================================================================================END



================================================================================START
Sidebar Menu:
- Flow:


- Display Dashboards:
  + Data: src/data/DashboardBlocks.json
  + Dashboards from the data: src/data/DashboardBlocks.json
  + Define menu links: src/data/SidebarMenu.json
        + If data: use: "DashboardBlocks" 
        + E.g.:
            "DashboardBlocks": [
              "cbam_analytics"
            ]
        + If component: 
            + use: "view"
            + and: define what to do with that in MasterCard.tsx
        + E.g.: 
              "view": "cbam/integration"
  + 
  + 
  + 

- Display a component:
  + 
  + 
  + 
  + 
  + 
  + 
  + 
  + 


Need to hard code the json loading here:
src/components/SidebarMenu.tsx


================================================================================END


================================================================================START
curl -v -X POST http://localhost:3004/api/getFarmerDetails \
  -H "Content-Type: application/json" \
  -d '{"id": "FARMER001"}'


curl -v -X POST http://localhost:3004/api/getFarmerDetails \
  -H "Content-Type: application/json" \
  -d '{"id": "TH01"}'


================================================================================END

================================================================================START

curl -X POST http://localhost:3004/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'

curl -X POST http://localhost:3004/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "ThanhSon01"}'


curl -X POST http://localhost:3004/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "ThanhSon01"}'
{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o1XGwXV4VzXQcW3betzuCD5Hur-64RR-KzjYneKgduw","user":{"id":"1","username":"admin","role":"admin","name":"Admin User"}}%   



================================================================================END

================================================================================START
Installation:

- Both client and server
- Server:
cd server 
yarn 
yarn build 
yarn start 

- Client:
yarn 
yarn build 
yarn start 


Frontend still works at this commit:
git checkout 7dfa2e7263b1357dace036928c4995209945c143



Change port:
- Client: Right now I have to hard code, coudln't set in the .env yet
- Server: could set the port in the server/.env
- CORS: need to set manually else fail
I have to add the name of the frontend in this list.
so now I have to add here: 'https://ccc.abnasia.org'
origin: ['http://localhost:3000', 'http://localhost:3004','http://localhost:3005', 'https://api-dace.abnasia.org', 'https://dace.abnasia.org', 'https://ccc.abnasia.org'],





================================================================================END


================================================================================START
App Structure:


Entry Point:
- src/app/page.tsx
- It loads the Analytics Module which is ModuleAnalytics


Page:
- http://localhost:3000/
- http://localhost:3000/asldfjlkas
- It displays the code in: /src/app/folder/page.tsx
- E.g.: http://localhost:3000/news displays this file: src/app/news/page.tsx


Analytics:
- src/components/ModuleAnalytics.tsx
- Loads the DashboardProvider


================================================================================END


================================================================================START
Menu System:
- Header Menu: HeaderMenu.json
- Sidebar Menu:
          - SidebarMenu.json -> DashboardBlocks.json -> charts.json

          - Specified in DashboardBlocks.json
          - src/data/SidebarMenu.json: 
                - It seems to have both auto dashboard and links / routes
- Footer Menu: FooterMenu.json






================================================================================END



================================================================================START
admin:    <EMAIL>
password: password
(ThanhSon01 $2b$10$7vCSVel6QcA524V1/llc4.OokDSrN9MGe4i1WDa6ryw3pumiONvaa)


Login page: currently hardcoded <NAME_EMAIL> and password
user: <EMAIL>
password: password

src/utils/auth.ts





================================================================================END





================================================================================START
Define Dashboards and Charts:

- Data for Charts: DashboardBlocks.json
- Data for Sidebar Menu Item's Dashboard: Also in DashboardBlocks.json, under sections
- Sections contain array of chart IDs which were defined earlier in DashboardBlocks.json
- In the SidebarMenu.tsx, each MenuItem can have a submenu with a list of sections.
- When a section is clicked, the DashboardContext is updated with the new active sections.
- The DashboardBlocks component subscribes to these changes and re-renders with the new set of active charts.




================================================================================END




================================================================================START


Install Shadcn:

npx shadcn@latest add button
npx shadcn@latest add radio-group
npx shadcn@latest add checkbox
npx shadcn@latest add input
npx shadcn@latest add label
npx shadcn@latest add dropdown-menu
npx shadcn@latest add alert
================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START
Surveys:
Now you can add new surveys by simply adding JSON files to the surveys directory, and they'll be automatically loaded by the application.


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START
Now you can use either library icons (by specifying the icon name) or custom images (by providing an imagePath) in your InfoBoxes. The Lucide icons will be used by default, but you still have the flexibility to use custom images when needed.
Example usage in DashboardBlocks.json:
{
  "text": "System Status",
  "number": "Online",
  "smallText": "All systems operational",
  "icon": "CheckCircle"  // Uses Lucide CheckCircle icon
}

Or with a custom image:
{
  "text": "System Status",
  "number": "Online",
  "smallText": "All systems operational",
  "imagePath": "/custom-icons/status.svg"  // Uses custom image
}

This approach gives you the flexibility to use both library icons and custom images while maintaining a clean and consistent interface.

================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START
Charts Format:

{
  "charts": {
    "occupancy_rate": {
      "id": "occupancy_rate",
      "type": "line",
      "title": "Tỷ lệ lấp đầy",
      "subtitle": "Phần trăm (%)",
      "data": [
        { "name": "T1", "value": 85, "color": "#3B82F6" },
        { "name": "T2", "value": 87, "color": "#3B82F6" },
        { "name": "T3", "value": 90, "color": "#3B82F6" },
        { "name": "T4", "value": 88, "color": "#3B82F6" },
        { "name": "T5", "value": 92, "color": "#3B82F6" },
        { "name": "T6", "value": 95, "color": "#3B82F6" },
        { "name": "T7", "value": 94, "color": "#3B82F6" },
        { "name": "T8", "value": 93, "color": "#3B82F6" },
        { "name": "T9", "value": 91, "color": "#3B82F6" },
        { "name": "T10", "value": 89, "color": "#3B82F6" },
        { "name": "T11", "value": 86, "color": "#3B82F6" },
        { "name": "T12", "value": 88, "color": "#3B82F6" }
      ]
    },
    "revenue_by_property": {
      "id": "revenue_by_property",
      "type": "bar",
      "title": "Doanh thu theo loại phòng",
      "subtitle": "Triệu VNĐ",
      "data": [
        { "name": "Phòng đơn", "value": 15, "color": "#10B981" },
        { "name": "Phòng đôi", "value": 25, "color": "#10B981" },
        { "name": "Căn hộ Studio", "value": 35, "color": "#10B981" },
        { "name": "Căn hộ 1PN", "value": 45, "color": "#10B981" },
        { "name": "Căn hộ 2PN", "value": 60, "color": "#10B981" }
      ]
    },
    "maintenance_costs": {
      "id": "maintenance_costs",
      "type": "pie",
      "title": "Chi phí bảo trì",
      "subtitle": "Triệu VNĐ",
      "data": [
        { "name": "Q1", "value": 10, "color": "#3B82F6" },
        { "name": "Q2", "value": 15, "color": "#10B981" },
        { "name": "Q3", "value": 20, "color": "#F59E0B" },
        { "name": "Q4", "value": 25, "color": "#EF4444" }
      ]
    },
    "tenant_satisfaction": {
      "id": "tenant_satisfaction",
      "type": "radar",
      "title": "Sự hài lòng của khách hàng",
      "subtitle": "Phần trăm (%)",
      "data": [
        { "name": "Dịch vụ", "value": 85, "color": "#10B981" },
        { "name": "Giá cả", "value": 70, "color": "#10B981" },
        { "name": "Vị trí", "value": 90, "color": "#10B981" },
        { "name": "Tiện ích", "value": 75, "color": "#10B981" },
        { "name": "Bảo trì", "value": 65, "color": "#10B981" },
        { "name": "Giao tiếp", "value": 80, "color": "#10B981" }
      ]
    },
    "property_size_price": {
      "id": "property_size_price",
      "type": "scatter",
      "title": "Mối quan hệ giữa diện tích và giá thuê",
      "subtitle": "Diện tích (m²) vs. Giá thuê (triệu VNĐ)",
      "data": [
        { "x": 25, "y": 3.5, "name": "Phòng đơn A", "color": "#3B82F6" },
        { "x": 30, "y": 4.2, "name": "Phòng đơn B", "color": "#3B82F6" },
        { "x": 35, "y": 5.0, "name": "Phòng đôi A", "color": "#10B981" },
        { "x": 40, "y": 5.8, "name": "Phòng đôi B", "color": "#10B981" },
        { "x": 45, "y": 6.5, "name": "Studio A", "color": "#F59E0B" },
        { "x": 50, "y": 7.2, "name": "Studio B", "color": "#F59E0B" },
        { "x": 60, "y": 8.5, "name": "1PN A", "color": "#EF4444" },
        { "x": 70, "y": 10.0, "name": "1PN B", "color": "#EF4444" },
        { "x": 80, "y": 12.0, "name": "2PN A", "color": "#8B5CF6" },
        { "x": 90, "y": 14.0, "name": "2PN B", "color": "#8B5CF6" }
      ]
    }
  }
} 
================================================================================END


================================================================================START
Create QR via API:

# Custom colors
curl -X POST /api/qr/generate \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "template": "custom",
    "dotsColor": "#3498db",
    "cornerSquareColor": "#2980b9",
    "backgroundColor": "#f8f9fa",
    "returnImage": true
  }'

# With logo
curl -X POST /api/qr/generate \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "image": "https://example.com/logo.png",
    "imageSize": 0.25,
    "dotsColor": "#e74c3c"
  }'
  

================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END



================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START


================================================================================END


================================================================================START
ICS:
👑 ICS Manager (Full Access)
Username: ics_admin
Password: ICS2025!Admin
🔍 Internal Inspector
Username: inspector1
Password: Inspector2025!
🌾 Field Officer
Username: field_officer1
Password: Field2025!
📦 Purchase Manager
Username: purchase_mgr
Password: Purchase2025!


================================================================================END


================================================================================START
```
Add new product to the ABN Asia website at src/app/web/abnasia with the following details:

PRODUCT DETAILS:
- Name
- Category
- Description

CASE STUDY:
- Company
- Industry
- Location
- Challenge
- Solution
- Results

INDUSTRY

Requirements:
1. Add bilingual content (English + Vietnamese)
2. Include compelling, results-focused descriptions
3. Add to appropriate JSON files and update components if needed
4. Follow the AEO implementation pattern
5. Ensure content motivates companies to use ABN Asia's services
6. Include specific metrics and business impact
7. Test that content appears on products page, success stories, and industry showcase

Use the existing AEO Optimization Suite as a reference for quality and structure.
```


================================================================================END

