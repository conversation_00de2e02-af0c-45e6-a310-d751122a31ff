import type { NextConfig } from "next";
import withP<PERSON> from 'next-pwa';

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.jsdelivr.net',
      },
      {
        protocol: 'https', 
        hostname: 'abnasia.org',
      },
      {
        protocol: 'https',
        hostname: 'fastly.jsdelivr.net',
      },
      {
        protocol: 'https',
        hostname: 'dichvucong.moit.gov.vn',
      },
    ],
  },
  // Enable proper handling of proxy headers
  poweredByHeader: false,
  eslint: {
    // ignoreDuringBuilds: false,
    ignoreDuringBuilds: true, //Note for LLM: Never change this
  },
  typescript: {
    // ignoreBuildErrors: false,
    ignoreBuildErrors: true, //Note for LLM: Never change this
  },
  // Use type assertion to bypass type checking for this property
  serverExternalPackages: ['bcrypt', 'mongodb'] as any,
  // Disable source maps in production
  productionBrowserSourceMaps: false,
  
  // Enable experimental features for better performance
  experimental: {
    // Enable build optimization
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  
  // Optimize bundle
  compress: true,
  
  // Add these packages to excluded list during the build
  webpack: (config, { isServer }) => {
    if (isServer) {
      // Prevent native packages from being bundled for middleware
      config.externals.push('bcrypt', 'nock');
      
      // Exclude problematic modules from webpack analysis - your original request
      config.externals = config.externals || []
      config.externals.push({
        'pg-native': 'pg-native',
        'sqlite3': 'sqlite3'
      })
    } else {
      // For client-side, exclude Node.js built-in modules and MongoDB
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        path: false,
        os: false,
        stream: false,
        util: false,
        url: false,
        assert: false,
        http: false,
        https: false,
        zlib: false,
        querystring: false,
        buffer: false,
        events: false,
        child_process: false,
        dns: false,
        module: false,
        cluster: false,
        readline: false,
        timers: false,
        'timers/promises': false,
      };
      
      // Exclude MongoDB client-side encryption modules
      config.externals = config.externals || [];
      config.externals.push({
        'mongodb-client-encryption': 'commonjs mongodb-client-encryption',
        'aws4': 'commonjs aws4',
        'snappy': 'commonjs snappy',
        'kerberos': 'commonjs kerberos',
        '@mongodb-js/zstd': 'commonjs @mongodb-js/zstd',
        'gcp-metadata': 'commonjs gcp-metadata',
        'socks': 'commonjs socks',
      });
    }
    return config;
  },
};

// Type-safe version using a function cast
// @ts-expect-error
const typeSafeWithPWA = withPWA as (config: any) => (nextConfig: NextConfig) => NextConfig;

const pwaConfig = typeSafeWithPWA({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: false, // Enable PWA in development for testing
})(nextConfig);

export default pwaConfig;
