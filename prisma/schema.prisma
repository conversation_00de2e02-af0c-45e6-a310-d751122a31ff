// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id                String   @id @default(cuid())
  name             String?
  email            String?   @unique
  emailVerified    DateTime?
  image            String?
  password         String?
  role             String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  userTenants       UserTenant[]
  userSkills        UserSkill[]
  freelancerProfile FreelancerProfile?
  clientProfile     ClientProfile?
  proposals         Proposal[]
  clientProjects    Project[] @relation("ClientProjects")
  freelancerProjects Project[] @relation("FreelancerProjects")
  postedJobs        Job[]     @relation("JobPoster")
  clientContracts   Contract[] @relation("ClientContracts")
  authoredReviews   Review[]  @relation("ReviewAuthor")
  receivedReviews   Review[]  @relation("ReviewTarget")
  englishUser       EnglishUser?
  
  // Recruitment Portal Relations
  companyUsers      CompanyUser[]
  jobSeekerProfile  JobSeekerProfile?
  postedJobPostings JobPosting[]

  @@index([email])
}

model UserTenant {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  tenantId  String
  rolesList String   // Comma-separated list of roles
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, tenantId])
  @@index([tenantId])
  @@index([userId])
}

model Skill {
  id          String   @id @default(cuid())
  name        String
  category    String
  description String
  tenantId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userSkills  UserSkill[]
  jobSkills   JobSkill[]
  freelancerProfiles FreelancerProfile[] @relation("FreelancerProfileToSkill")
  jobs        Job[]     @relation("JobToSkill")
  jobPostingSkills JobPostingSkill[]

  @@index([tenantId])
}

model FreelancerProfile {
  id              String   @id @default(cuid())
  userId          String   @unique
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  title           String
  description     String
  hourlyRate      Float
  availability    String
  experienceLevel String
  tenantId        String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  skills          Skill[]  @relation("FreelancerProfileToSkill")

  @@index([tenantId])
}

model ClientProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  description String
  industry    String?
  website     String?
  tenantId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([tenantId])
}

model Job {
  id          String   @id @default(cuid())
  title       String
  description String
  budget      Float?
  type        String
  posterId    String
  poster      User     @relation("JobPoster", fields: [posterId], references: [id])
  status      String   @default("open")
  tenantId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  skills      Skill[]  @relation("JobToSkill")
  proposals   Proposal[]
  contract    Contract? @relation("JobContract")
  jobSkills   JobSkill[]

  @@index([tenantId])
  @@index([posterId])
}

model JobSkill {
  id        String   @id @default(cuid())
  jobId     String
  job       Job      @relation(fields: [jobId], references: [id], onDelete: Cascade)
  skillId   String
  skill     Skill    @relation(fields: [skillId], references: [id], onDelete: Cascade)
  level     Int
  createdAt DateTime @default(now())

  @@unique([jobId, skillId])
}

model Proposal {
  id           String   @id @default(cuid())
  jobId        String
  job          Job      @relation(fields: [jobId], references: [id])
  freelancerId String
  freelancer   User     @relation(fields: [freelancerId], references: [id])
  coverLetter  String
  amount       Float
  status       String   @default("pending")
  tenantId     String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([tenantId])
  @@index([jobId])
  @@index([freelancerId])
}

model Contract {
  id          String      @id @default(uuid())
  title       String
  content     String
  status      String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdBy   String
  templateId  String?
  metadata    Json
  signers     ContractSigner[]
  workflow    WorkflowStep[]
  template    ContractTemplate? @relation(fields: [templateId], references: [id])
  
  // Relations with existing models
  client      User     @relation("ClientContracts", fields: [clientId], references: [id])
  clientId    String
  job         Job?     @relation("JobContract", fields: [jobId], references: [id])
  jobId       String?  @unique // Make jobId unique for one-to-one relation
  projects    Project[]
  timeEntries TimeEntry[]
  milestones  Milestone[]
}

model ContractSigner {
  id          String    @id @default(uuid())
  email       String
  name        String
  status      String
  signedAt    DateTime?
  order       Int
  contract    Contract  @relation(fields: [contractId], references: [id], onDelete: Cascade)
  contractId  String
}

model ContractTemplate {
  id          String    @id @default(uuid())
  name        String
  description String
  content     String
  category    String
  metadata    Json
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  contracts   Contract[]
  clauses     ContractClause[]
}

model ContractClause {
  id          String    @id @default(uuid())
  title       String
  content     String
  category    String
  tags        Json
  isActive    Boolean   @default(true)
  template    ContractTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  templateId  String
}

model WorkflowStep {
  id          String    @id @default(uuid())
  type        String
  name        String
  assignee    String?
  dueDate     DateTime?
  status      String
  order       Int
  contract    Contract  @relation(fields: [contractId], references: [id], onDelete: Cascade)
  contractId  String
}

model Project {
  id           String   @id @default(cuid())
  contractId   String
  contract     Contract @relation(fields: [contractId], references: [id])
  title        String
  description  String
  clientId     String
  client       User     @relation("ClientProjects", fields: [clientId], references: [id])
  freelancerId String
  freelancer   User     @relation("FreelancerProjects", fields: [freelancerId], references: [id])
  status       String   @default("in-progress")
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  tasks        Task[]
  reviews      Review[]
}

model Task {
  id          String   @id @default(cuid())
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  title       String
  description String
  status      String   @default("todo")
  dueDate     DateTime?
  priority    String   @default("medium")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Review {
  id          String   @id @default(cuid())
  rating      Float
  comment     String
  authorId    String
  author      User     @relation("ReviewAuthor", fields: [authorId], references: [id])
  targetId    String
  target      User     @relation("ReviewTarget", fields: [targetId], references: [id])
  projectId   String?
  project     Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tenantId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([tenantId])
  @@index([projectId])
}

model TimeEntry {
  id          String   @id @default(cuid())
  contractId  String
  contract    Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  description String
  startTime   DateTime
  endTime     DateTime?
  duration    Int?
  status      String   @default("pending")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Milestone {
  id          String   @id @default(cuid())
  contractId  String
  contract    Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  title       String
  description String
  amount      Float
  dueDate     DateTime
  status      String   @default("pending")
  completedAt DateTime?
  approvedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model UserSkill {
  id                String   @id @default(cuid())
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  skillId           String
  skill             Skill    @relation(fields: [skillId], references: [id], onDelete: Cascade)
  level             Int
  yearsOfExperience Int
  isCertified       Boolean  @default(false)
  certificationUrl  String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([userId, skillId])
}

// English Learning Platform Models
model EnglishUser {
  id                String               @id @default(cuid())
  userId            String               @unique // Reference to the main User model
  user              User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  nativeLanguage    String
  targetAccent      String?
  startLevel        String               // 'beginner', 'intermediate', 'advanced'
  currentLevel      String               // 'beginner', 'intermediate', 'advanced'
  joinDate          DateTime             @default(now())
  lastActive        DateTime             @default(now())
  totalLearningTime Int                  @default(0) // in minutes
  streak            Int                  @default(0) // consecutive days
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  
  // Relations
  progress          EnglishUserProgress?
  completedLessons  CompletedLesson[]
  completedExercises CompletedExercise[]
  badges            UserBadge[]
  goals             UserGoal[]
  teams             UserTeam[]

  @@index([userId])
}

model EnglishUserProgress {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              EnglishUser @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Skill levels (0-100)
  pronunciationScore Int      @default(0)
  grammarScore      Int      @default(0)
  vocabularyScore   Int      @default(0)
  listeningScore    Int      @default(0)
  readingScore      Int      @default(0)
  writingScore      Int      @default(0)
  speakingScore     Int      @default(0)
  overallScore      Int      @default(0)
  
  // Learning path
  currentModule     String?
  nextLessons       String?  // Comma-separated IDs
  recommendedExercises String? // Comma-separated IDs
  
  // Exam prep
  targetExam        String?  // 'IELTS', 'TOEFL', etc.
  targetScore       Int?
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([userId])
}

model EnglishLesson {
  id                String   @id @default(cuid())
  blockId           String   @unique // Reference to the EnglishLessonBlock
  title             String
  description       String
  level             String   // 'beginner', 'intermediate', 'advanced', 'business'
  category          String   // 'pronunciation', 'grammar', 'vocabulary', etc.
  duration          Int      // in minutes
  tags              String?  // Comma-separated tags
  isPublished       Boolean  @default(false)
  publishedAt       DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  completedBy       CompletedLesson[]
  sections          LessonSection[]

  @@index([level, category])
  @@index([blockId])
}

model LessonSection {
  id                String   @id @default(cuid())
  lessonId          String
  lesson            EnglishLesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  title             String
  content           String
  examples          Json?    // JSON array of examples
  exerciseIds       String?  // Comma-separated IDs of exercises
  order             Int      // Order within the lesson
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([lessonId])
}

model PronunciationExercise {
  id                String   @id @default(cuid())
  blockId           String   @unique // Reference to the PronunciationExerciseBlock
  title             String
  description       String
  level             String   // 'beginner', 'intermediate', 'advanced', 'business'
  focusAreas        String   // Comma-separated focus areas
  accent            String   // 'american', 'british', etc.
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  completedBy       CompletedExercise[]

  @@index([level, focusAreas])
  @@index([blockId])
}

model CompletedLesson {
  id                String   @id @default(cuid())
  userId            String
  user              EnglishUser @relation(fields: [userId], references: [id], onDelete: Cascade)
  lessonId          String
  lesson            EnglishLesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  completedAt       DateTime @default(now())
  score             Int      // 0-100
  timeSpent         Int      // in minutes
  
  @@unique([userId, lessonId])
  @@index([userId])
  @@index([lessonId])
}

model CompletedExercise {
  id                String   @id @default(cuid())
  userId            String
  user              EnglishUser @relation(fields: [userId], references: [id], onDelete: Cascade)
  exerciseId        String
  exercise          PronunciationExercise @relation(fields: [exerciseId], references: [id], onDelete: Cascade)
  completedAt       DateTime @default(now())
  score             Int      // 0-100
  timeSpent         Int      // in minutes
  recordings        Json?    // JSON array of recording URLs
  feedback          Json?    // JSON object with feedback
  
  @@unique([userId, exerciseId, completedAt])
  @@index([userId])
  @@index([exerciseId])
}

model UserBadge {
  id                String   @id @default(cuid())
  userId            String
  user              EnglishUser @relation(fields: [userId], references: [id], onDelete: Cascade)
  badgeId           String
  name              String
  description       String
  imageUrl          String
  earnedAt          DateTime @default(now())
  
  @@unique([userId, badgeId])
  @@index([userId])
}

model UserGoal {
  id                String   @id @default(cuid())
  userId            String
  user              EnglishUser @relation(fields: [userId], references: [id], onDelete: Cascade)
  title             String
  description       String
  targetDate        DateTime
  progress          Int      @default(0) // 0-100
  isCompleted       Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@index([userId])
}

model Team {
  id                String   @id @default(cuid())
  name              String
  companyId         String
  department        String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  members           UserTeam[]
}

model UserTeam {
  id                String   @id @default(cuid())
  userId            String
  user              EnglishUser @relation(fields: [userId], references: [id], onDelete: Cascade)
  teamId            String
  team              Team     @relation(fields: [teamId], references: [id], onDelete: Cascade)
  role              String   @default("member") // 'admin', 'member'
  joinedAt          DateTime @default(now())
  
  @@unique([userId, teamId])
  @@index([userId])
  @@index([teamId])
}

// Recruitment Portal Models

model Company {
  id                String   @id @default(cuid())
  name              String
  description       String?
  industry          String?
  website           String?
  logo              String?
  size              String?  // 'startup', 'small', 'medium', 'large', 'enterprise'
  location          String?
  tenantId          String
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  jobPostings       JobPosting[]
  companyUsers      CompanyUser[]
  
  @@index([tenantId])
}

model CompanyUser {
  id                String   @id @default(cuid())
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  companyId         String
  company           Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)
  role              String   // 'hr-admin', 'hr-manager', 'hr-recruiter'
  permissions       String?  // JSON string of permissions
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@unique([userId, companyId])
  @@index([companyId])
  @@index([userId])
}

model JobSeekerProfile {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  firstName         String
  lastName          String
  headline          String?
  summary           String?
  phone             String?
  location          String?
  website           String?
  linkedinUrl       String?
  githubUrl         String?
  portfolioUrl      String?
  preferredSalary   Float?
  salaryType        String?  // 'hourly', 'monthly', 'yearly'
  availableFrom     DateTime?
  workAuthorization String?  // 'citizen', 'permanent_resident', 'visa_required'
  remoteWork        Boolean  @default(false)
  willingToRelocate Boolean  @default(false)
  resume            String?  // File path or URL
  profilePicture    String?
  isPublic          Boolean  @default(true)
  isActiveJobSeeker Boolean  @default(true)
  tenantId          String
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  applications      JobApplication[]
  experiences       WorkExperience[]
  educations        Education[]
  
  @@index([tenantId])
  @@index([userId])
}

model WorkExperience {
  id                String   @id @default(cuid())
  jobSeekerProfileId String
  jobSeekerProfile  JobSeekerProfile @relation(fields: [jobSeekerProfileId], references: [id], onDelete: Cascade)
  title             String
  company           String
  location          String?
  description       String?
  startDate         DateTime
  endDate           DateTime?
  isCurrent         Boolean  @default(false)
  industry          String?
  employmentType    String?  // 'full-time', 'part-time', 'contract', 'freelance', 'internship'
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@index([jobSeekerProfileId])
}

model Education {
  id                String   @id @default(cuid())
  jobSeekerProfileId String
  jobSeekerProfile  JobSeekerProfile @relation(fields: [jobSeekerProfileId], references: [id], onDelete: Cascade)
  institution       String
  degree            String
  fieldOfStudy      String?
  grade             String?
  startDate         DateTime?
  endDate           DateTime?
  isCurrent         Boolean  @default(false)
  description       String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@index([jobSeekerProfileId])
}

model JobPosting {
  id                String   @id @default(cuid())
  title             String
  description       String
  requirements      String?
  responsibilities  String?
  benefits          String?
  salaryMin         Float?
  salaryMax         Float?
  salaryType        String?  // 'hourly', 'monthly', 'yearly'
  location          String?
  remoteWork        Boolean  @default(false)
  employmentType    String   // 'full-time', 'part-time', 'contract', 'freelance', 'internship'
  experienceLevel   String   // 'entry', 'junior', 'mid', 'senior', 'lead', 'executive'
  department        String?
  companyId         String
  company           Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)
  postedById        String
  postedBy          User     @relation(fields: [postedById], references: [id])
  status            String   @default("active") // 'active', 'paused', 'closed', 'draft'
  applicationDeadline DateTime?
  startDate         DateTime?
  isUrgent          Boolean  @default(false)
  viewCount         Int      @default(0)
  applicationCount  Int      @default(0)
  tenantId          String
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  applications      JobApplication[]
  requiredSkills    JobPostingSkill[]
  
  @@index([tenantId])
  @@index([companyId])
  @@index([status])
  @@index([employmentType])
  @@index([experienceLevel])
}

model JobPostingSkill {
  id                String   @id @default(cuid())
  jobPostingId      String
  jobPosting        JobPosting @relation(fields: [jobPostingId], references: [id], onDelete: Cascade)
  skillId           String
  skill             Skill    @relation(fields: [skillId], references: [id], onDelete: Cascade)
  level             String   // 'beginner', 'intermediate', 'advanced', 'expert'
  isRequired        Boolean  @default(true)
  createdAt         DateTime @default(now())
  
  @@unique([jobPostingId, skillId])
  @@index([jobPostingId])
  @@index([skillId])
}

model JobApplication {
  id                String   @id @default(cuid())
  jobPostingId      String
  jobPosting        JobPosting @relation(fields: [jobPostingId], references: [id], onDelete: Cascade)
  jobSeekerProfileId String
  jobSeekerProfile  JobSeekerProfile @relation(fields: [jobSeekerProfileId], references: [id], onDelete: Cascade)
  coverLetter       String?
  resume            String?  // File path or URL
  status            String   @default("applied") // 'applied', 'screening', 'interview', 'offer', 'hired', 'rejected', 'withdrawn'
  appliedAt         DateTime @default(now())
  lastUpdated       DateTime @updatedAt
  reviewedAt        DateTime?
  reviewedBy        String?
  reviewerNotes     String?
  salaryExpectation Float?
  availableFrom     DateTime?
  tenantId          String
  
  // Relations
  interviews        Interview[]
  assessments       Assessment[]
  
  @@unique([jobPostingId, jobSeekerProfileId])
  @@index([jobPostingId])
  @@index([jobSeekerProfileId])
  @@index([status])
  @@index([tenantId])
}

model Interview {
  id                String   @id @default(cuid())
  jobApplicationId  String
  jobApplication    JobApplication @relation(fields: [jobApplicationId], references: [id], onDelete: Cascade)
  title             String
  description       String?
  type              String   // 'phone', 'video', 'in-person', 'technical'
  scheduledAt       DateTime
  duration          Int      // in minutes
  location          String?  // Physical location or meeting link
  interviewerIds    String   // Comma-separated user IDs
  status            String   @default("scheduled") // 'scheduled', 'completed', 'cancelled', 'rescheduled'
  feedback          String?
  rating            Int?     // 1-5
  notes             String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@index([jobApplicationId])
  @@index([scheduledAt])
  @@index([status])
}

model Assessment {
  id                String   @id @default(cuid())
  jobApplicationId  String
  jobApplication    JobApplication @relation(fields: [jobApplicationId], references: [id], onDelete: Cascade)
  title             String
  description       String?
  type              String   // 'technical', 'cognitive', 'personality', 'skills'
  questions         Json?    // Assessment questions/structure
  responses         Json?    // Candidate responses
  score             Float?
  maxScore          Float?
  status            String   @default("pending") // 'pending', 'in-progress', 'completed', 'expired'
  timeLimit         Int?     // in minutes
  startedAt         DateTime?
  completedAt       DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@index([jobApplicationId])
  @@index([status])
}